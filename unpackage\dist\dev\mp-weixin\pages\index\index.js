"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const composables_useBankSearch = require("../../composables/useBankSearch.js");
const composables_useAuth = require("../../composables/useAuth.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const {
      isLoggedIn,
      userInfo,
      openid,
      isLogging,
      needLogin,
      checkLoginStatus,
      wxLogin,
      ensureLogin
    } = composables_useAuth.useAuth();
    const {
      searchKeyword,
      searchResults,
      isLoading,
      hasResults,
      showNoResult,
      isSearchDisabled,
      executeSearch,
      pasteAndSearch,
      clearSearch,
      selectResult
    } = composables_useBankSearch.useBankSearch();
    const { formatCode } = composables_useBankSearch.useBankCodeFormatter();
    const showLoginModal = common_vendor.ref(false);
    common_vendor.onMounted(async () => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:177", "AI联行助手页面已加载");
      const isLoggedInLocal = checkLoginStatus();
      if (!isLoggedInLocal) {
        showLoginModal.value = true;
        try {
          await wxLogin();
          showLoginModal.value = false;
        } catch (error) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:191", "自动登录失败，需要用户手动登录");
        }
      }
    });
    const handleSearch = async () => {
      try {
        const loginSuccess = await ensureLogin();
        if (!loginSuccess) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          showLoginModal.value = true;
          return;
        }
        await executeSearch();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:213", "搜索失败:", error);
      }
    };
    const handlePaste = async () => {
      try {
        const loginSuccess = await ensureLogin();
        if (!loginSuccess) {
          common_vendor.index.showToast({
            title: "请先登录",
            icon: "none"
          });
          showLoginModal.value = true;
          return;
        }
        await pasteAndSearch();
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:234", "粘贴搜索失败:", error);
      }
    };
    const handleClear = clearSearch;
    const formatBankCodeDisplay = formatCode;
    const handleLogin = async () => {
      try {
        const success = await wxLogin();
        if (success) {
          showLoginModal.value = false;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:252", "登录失败:", error);
      }
    };
    const closeLoginModal = () => {
      showLoginModal.value = false;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0,
        b: common_vendor.unref(searchKeyword),
        c: common_vendor.o(($event) => common_vendor.isRef(searchKeyword) ? searchKeyword.value = $event.detail.value : null),
        d: common_vendor.t(common_vendor.unref(isLoading) ? "搜索中..." : "搜索"),
        e: common_vendor.unref(isSearchDisabled),
        f: common_vendor.unref(isSearchDisabled) ? 1 : "",
        g: common_vendor.o(handleSearch),
        h: common_vendor.unref(isLoading),
        i: common_vendor.unref(isLoading) ? 1 : "",
        j: common_vendor.o(handlePaste),
        k: common_vendor.o((...args) => common_vendor.unref(handleClear) && common_vendor.unref(handleClear)(...args)),
        l: common_vendor.unref(hasResults)
      }, common_vendor.unref(hasResults) ? {
        m: common_vendor.f(common_vendor.unref(searchResults), (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(common_vendor.unref(formatBankCodeDisplay)(item.code)),
            b: common_vendor.t(item.bankName),
            c: common_vendor.t(item.branchName),
            d: item.distance
          }, item.distance ? {
            e: common_vendor.t(item.distance)
          } : {}, {
            f: index,
            g: common_vendor.o(($event) => common_vendor.unref(selectResult)(item), index)
          });
        })
      } : {}, {
        n: common_vendor.unref(isLoading)
      }, common_vendor.unref(isLoading) ? {} : {}, {
        o: common_vendor.unref(showNoResult)
      }, common_vendor.unref(showNoResult) ? {} : {}, {
        p: showLoginModal.value
      }, showLoginModal.value ? {
        q: common_vendor.t(common_vendor.unref(isLogging) ? "登录中..." : "微信授权登录"),
        r: common_vendor.unref(isLogging),
        s: common_vendor.unref(isLogging),
        t: common_vendor.o(handleLogin),
        v: common_vendor.o(closeLoginModal),
        w: common_vendor.o(() => {
        }),
        x: common_vendor.o(closeLoginModal)
      } : {}, {
        y: common_vendor.unref(isLoggedIn) && common_vendor.unref(userInfo)
      }, common_vendor.unref(isLoggedIn) && common_vendor.unref(userInfo) ? {
        z: common_vendor.t(common_vendor.unref(userInfo).nickname || "用户"),
        A: common_vendor.t(common_vendor.unref(openid).substring(0, 8))
      } : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
