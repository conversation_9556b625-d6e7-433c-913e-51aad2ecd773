"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const composables_useBankSearch = require("../../composables/useBankSearch.js");
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const {
      searchKeyword,
      searchResults,
      isLoading,
      hasResults,
      showNoResult,
      isSearchDisabled,
      executeSearch,
      pasteAndSearch,
      clearSearch,
      selectResult
    } = composables_useBankSearch.useBankSearch();
    const { formatCode } = composables_useBankSearch.useBankCodeFormatter();
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/index/index.vue:126", "银行联行号查询页面已加载");
    });
    const handleSearch = executeSearch;
    const handlePaste = pasteAndSearch;
    const handleClear = clearSearch;
    const formatBankCodeDisplay = formatCode;
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0,
        b: common_vendor.unref(searchKeyword),
        c: common_vendor.o(($event) => common_vendor.isRef(searchKeyword) ? searchKeyword.value = $event.detail.value : null),
        d: common_vendor.t(common_vendor.unref(isLoading) ? "搜索中..." : "搜索"),
        e: common_vendor.unref(isSearchDisabled),
        f: common_vendor.unref(isSearchDisabled) ? 1 : "",
        g: common_vendor.o((...args) => common_vendor.unref(handleSearch) && common_vendor.unref(handleSearch)(...args)),
        h: common_vendor.unref(isLoading),
        i: common_vendor.unref(isLoading) ? 1 : "",
        j: common_vendor.o((...args) => common_vendor.unref(handlePaste) && common_vendor.unref(handlePaste)(...args)),
        k: common_vendor.o((...args) => common_vendor.unref(handleClear) && common_vendor.unref(handleClear)(...args)),
        l: common_vendor.unref(hasResults)
      }, common_vendor.unref(hasResults) ? {
        m: common_vendor.f(common_vendor.unref(searchResults), (item, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(common_vendor.unref(formatBankCodeDisplay)(item.code)),
            b: common_vendor.t(item.bankName),
            c: common_vendor.t(item.branchName),
            d: item.distance
          }, item.distance ? {
            e: common_vendor.t(item.distance)
          } : {}, {
            f: index,
            g: common_vendor.o(($event) => common_vendor.unref(selectResult)(item), index)
          });
        })
      } : {}, {
        n: common_vendor.unref(isLoading)
      }, common_vendor.unref(isLoading) ? {} : {}, {
        o: common_vendor.unref(showNoResult)
      }, common_vendor.unref(showNoResult) ? {} : {});
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
