# 银行联行号快查应用

这是一个基于 uni-app 开发的银行联行号查询应用，支持多平台运行（H5、小程序、App等）。

## 功能特性

- 🔍 **智能搜索**: 支持银行名称、地区、联行号等多种搜索方式
- 📋 **一键粘贴**: 从剪贴板快速粘贴内容并自动查询
- 🗑️ **快速清除**: 一键清空输入框和查询结果
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 💾 **结果复制**: 点击查询结果自动复制联行号到剪贴板
- 🔐 **登录验证**: 微信小程序授权登录，确保用户身份安全
- 🔒 **真实加密**: 使用crypto-js和jsencrypt实现RSA+AES混合加密，支持IV提取
- 🤖 **AI助手**: 依托AI大模型能力，提供精确的联行号查询服务

## 页面功能说明

### 主要功能区域

1. **顶部标题栏**
   - 显示应用名称"联行号快查"
   - 包含操作按钮（•••、—、⊙）

2. **二维码和网站信息区域**
   - 显示应用二维码
   - 网站品牌信息展示

3. **搜索输入区域**
   - 输入框：支持输入银行名称、地区、联行号等
   - 提示信息：指导用户如何使用搜索功能

4. **操作按钮区域**
   - **搜索按钮**（绿色）：执行查询操作
   - **粘贴按钮**（蓝色）：从剪贴板粘贴内容并自动查询
   - **清除按钮**（橙色）：清空输入框和查询结果

5. **查询结果区域**
   - 显示查询到的银行信息
   - 包含联行号、银行名称、支行信息等
   - 点击结果项可复制联行号到剪贴板

## 使用方法

### 搜索银行信息

1. 在搜索框中输入关键字，例如：
   - 银行名称：`工商银行`、`农业银行`
   - 地区名称：`北京`、`上海`
   - 简称：`工行`、`农行`、`中行`、`建行`
   - 联行号：`************`

2. 点击"搜索"按钮执行查询

3. 查看查询结果，点击任意结果项可复制联行号

### 使用粘贴功能

1. 先复制银行相关信息到剪贴板
2. 点击"粘贴"按钮
3. 应用会自动将剪贴板内容填入搜索框并执行查询

### 清除功能

点击"清除"按钮可以：
- 清空搜索输入框
- 清除所有查询结果
- 重置页面状态

## 技术实现

### 项目结构

```
BankCodeSearch/
├── pages/
│   └── index/
│       └── index.vue          # 主页面（Vue 3 + 登录验证）
├── composables/
│   ├── useBankSearch.js       # 银行查询组合式函数
│   └── useAuth.js             # 用户认证组合式函数
├── utils/
│   ├── api.js                 # API工具类
│   ├── crypto.js              # 混合加密工具类
│   └── request.js             # 加密HTTP请求工具
├── types/
│   └── bank.js                # 银行相关类型定义和常量
├── docs/
│   ├── vue3-features.md       # Vue 3特性说明
│   ├── auth-encryption.md     # 登录加密功能说明
│   ├── real-encryption.md     # 真实加密实现说明
│   ├── iv-extraction.md       # IV提取功能说明
│   ├── api-integration.md     # API集成说明
│   └── quick-start.md         # 快速开始指南
├── config/
│   ├── crypto.js              # 加密配置文件
│   └── api.js                 # API接口配置文件
├── static/
│   └── logo.jpg              # 应用图标
├── pages.json                # 页面配置
├── App.vue                   # 应用入口（Vue 3）
├── main.js                   # 应用启动文件
├── manifest.json             # 应用配置清单
└── package.json              # 项目依赖配置
```

### 核心文件说明

- **pages/index/index.vue**: 主页面，集成登录验证和银行查询功能
- **composables/useAuth.js**: 用户认证组合式函数，管理登录状态和微信授权
- **composables/useBankSearch.js**: 银行查询组合式函数，提供响应式状态管理
- **utils/crypto.js**: 真实加密工具类，使用crypto-js和jsencrypt实现RSA+AES加密
- **config/crypto.js**: 加密配置管理，包含RSA公钥、AES参数等配置
- **utils/request.js**: 加密HTTP请求工具，自动处理请求加密和响应解密
- **utils/api.js**: 银行查询API，支持加密通信、重试机制和降级策略
- **config/api.js**: API接口配置管理，包含接口地址、重试策略等
- **types/bank.js**: 银行相关的类型定义、常量和工具函数

### Vue 3 特性

本项目采用Vue 3的最新特性：

1. **Composition API**: 使用`<script setup>`语法，代码更简洁
2. **组合式函数**: 将业务逻辑封装在可复用的composables中
3. **响应式系统**: 使用`ref`和`computed`提供更好的响应式体验
4. **类型安全**: 通过JSDoc提供类型提示和文档

### API接口

当前使用模拟数据进行演示，实际部署时需要：

1. 修改 `utils/api.js` 中的 `API_BASE_URL`
2. 取消注释真实API调用代码
3. 根据实际API接口调整数据格式

## 开发环境

- uni-app 框架
- Vue.js 2.x
- 支持编译到多个平台

## 运行项目

1. 使用 HBuilderX 打开项目
2. 选择运行平台（H5、微信小程序、App等）
3. 点击运行按钮启动项目

## 自定义配置

### 修改主题颜色

在 `pages/index/index.vue` 的样式部分修改以下颜色变量：

```css
/* 主色调 - 绿色 */
.search-btn { background-color: #4CAF50; }

/* 辅助色 - 蓝色 */
.paste-btn { background-color: #2196F3; }

/* 警告色 - 橙色 */
.clear-btn { background-color: #FF9800; }
```

### 修改API接口

在 `utils/api.js` 中修改：

```javascript
const API_BASE_URL = 'https://your-api-domain.com';
```

## 注意事项

1. 粘贴功能需要用户授权剪贴板访问权限
2. 在小程序平台可能需要配置网络请求域名白名单
3. 模拟数据仅用于演示，生产环境请使用真实API接口

## 许可证

本项目仅供学习和演示使用。
