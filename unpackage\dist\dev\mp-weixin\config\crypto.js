"use strict";
require("../common/vendor.js");
const CRYPTO_CONFIG = {
  // RSA配置
  RSA: {
    // 密钥长度
    KEY_SIZE: 2048,
    // 公钥 - 生产环境应该从服务器获取
    PUBLIC_KEY: `-----B<PERSON>IN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmWPDFsI9N+4Hsmo2
kwtPENGaJ+9Kiq4Nqscdac0LHfHLTEhPuPnnfYZ+g050Ag4IoS+C6U3MQM1P
gi4IuhL4Ef2awrMJxf4HwuVxtIZy+oiT8SRQU84/jgcpi9Omoi2wKcRc5Bpr
fp4wI2nzb9bS397KsPA0b/HSix+xlCkBCqTyL3HjFOBNSxsU3o0F0DyesomK
G5B6J9Csj8ALWLs/JUsZNfO1tMyHN2invduNPwvn7dETAGCeQ1t6Of986twv
EE8jDxEPQ35mZVF2cDkcvSSb8aJLzlyZS6zzmYF1UsfT+Z5j3lYUIIbVfacR
3V/FYL4qZx+0eYY2VrgWhoEZqQIDAQAB
-----END PUBLIC KEY-----`,
    // 算法配置
    ALGORITHM: "RSA-OAEP",
    HASH: "SHA-256"
  },
  // AES配置
  AES: {
    // 密钥长度（字符数）
    KEY_LENGTH: 32,
    // 加密模式
    MODE: "CBC",
    // 填充方式
    PADDING: "Pkcs5",
    // 字符集
    CHARSET: "UTF-8"
  },
  // 请求配置
  REQUEST: {
    // 是否启用加密
    ENABLE_ENCRYPTION: true,
    // 加密的请求方法
    ENCRYPTED_METHODS: ["POST", "PUT", "PATCH"],
    // 需要加密的接口路径
    ENCRYPTED_PATHS: [
      "/api/auth/login",
      "/api/bank/search",
      "/api/user/profile"
    ],
    // 请求超时时间
    TIMEOUT: 1e4
  },
  // 开发配置
  DEVELOPMENT: {
    // 是否启用调试日志
    ENABLE_DEBUG_LOG: false,
    // 是否启用加密测试
    ENABLE_CRYPTO_TEST: false,
    // 是否使用模拟数据
    USE_MOCK_DATA: false,
    // 测试模式下的密钥
    TEST_AES_KEY: "test-aes-key-32-characters-long",
    // 是否跳过RSA验证
    SKIP_RSA_VALIDATION: false
  }
};
function getRSAPublicKey() {
  return CRYPTO_CONFIG.RSA.PUBLIC_KEY;
}
function getAESConfig() {
  return CRYPTO_CONFIG.AES;
}
function isDebugEnabled() {
  return CRYPTO_CONFIG.DEVELOPMENT.ENABLE_DEBUG_LOG;
}
exports.getAESConfig = getAESConfig;
exports.getRSAPublicKey = getRSAPublicKey;
exports.isDebugEnabled = isDebugEnabled;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/crypto.js.map
