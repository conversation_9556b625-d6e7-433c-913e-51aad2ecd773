// 加密HTTP请求工具
import { crypto } from '@/utils/crypto.js'

/**
 * API基础配置
 */
const API_CONFIG = {
	baseURL: 'https://your-api-domain.com/api', // 替换为实际的API地址
	timeout: 10000,
	header: {
		'Content-Type': 'application/json'
	}
}

/**
 * 加密HTTP请求类
 */
export class EncryptedRequest {
	
	/**
	 * 发送加密请求
	 * @param {Object} options - 请求选项
	 * @returns {Promise<Object>} 响应数据
	 */
	static async request(options) {
		const {
			url,
			method = 'GET',
			data = {},
			header = {},
			needAuth = true,
			encrypt = true
		} = options
		
		try {
			// 1. 准备请求数据
			let requestData = data
			let aesKey = null
			
			// 2. 如果需要加密
			if (encrypt && (method === 'POST' || method === 'PUT')) {
				const encryptedRequest = crypto.encryptRequest(data)
				requestData = encryptedRequest
				// 保存AES密钥用于解密响应
				aesKey = crypto.generateAESKey()
			}
			
			// 3. 添加认证信息
			const requestHeader = { ...API_CONFIG.header, ...header }
			if (needAuth) {
				const openid = uni.getStorageSync('user_openid')
				if (openid) {
					requestHeader['Authorization'] = `Bearer ${openid}`
				}
			}
			
			// 4. 发送请求
			console.log(`发送${encrypt ? '加密' : '普通'}请求:`, {
				url: `${API_CONFIG.baseURL}${url}`,
				method,
				encrypt
			})
			
			const response = await uni.request({
				url: `${API_CONFIG.baseURL}${url}`,
				method,
				data: requestData,
				header: requestHeader,
				timeout: API_CONFIG.timeout
			})
			
			// 5. 处理响应
			if (response.statusCode === 200) {
				let responseData = response.data
				
				// 6. 如果响应是加密的，进行解密
				if (encrypt && responseData.encryptedData && aesKey) {
					responseData = crypto.decryptResponse(responseData.encryptedData, aesKey)
				}
				
				return {
					success: true,
					data: responseData,
					statusCode: response.statusCode
				}
			} else {
				throw new Error(`请求失败: ${response.statusCode}`)
			}
			
		} catch (error) {
			console.error('请求失败:', error)
			
			// 网络错误处理
			if (error.errMsg && error.errMsg.includes('request:fail')) {
				throw new Error('网络连接失败，请检查网络设置')
			}
			
			throw error
		}
	}
	
	/**
	 * GET请求
	 * @param {string} url - 请求URL
	 * @param {Object} params - 查询参数
	 * @param {Object} options - 其他选项
	 * @returns {Promise<Object>} 响应数据
	 */
	static async get(url, params = {}, options = {}) {
		// GET请求将参数拼接到URL
		const queryString = Object.keys(params)
			.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
			.join('&')
		
		const fullUrl = queryString ? `${url}?${queryString}` : url
		
		return this.request({
			url: fullUrl,
			method: 'GET',
			encrypt: false, // GET请求通常不加密
			...options
		})
	}
	
	/**
	 * POST请求
	 * @param {string} url - 请求URL
	 * @param {Object} data - 请求数据
	 * @param {Object} options - 其他选项
	 * @returns {Promise<Object>} 响应数据
	 */
	static async post(url, data = {}, options = {}) {
		return this.request({
			url,
			method: 'POST',
			data,
			encrypt: true, // POST请求默认加密
			...options
		})
	}
	
	/**
	 * PUT请求
	 * @param {string} url - 请求URL
	 * @param {Object} data - 请求数据
	 * @param {Object} options - 其他选项
	 * @returns {Promise<Object>} 响应数据
	 */
	static async put(url, data = {}, options = {}) {
		return this.request({
			url,
			method: 'PUT',
			data,
			encrypt: true,
			...options
		})
	}
	
	/**
	 * DELETE请求
	 * @param {string} url - 请求URL
	 * @param {Object} options - 其他选项
	 * @returns {Promise<Object>} 响应数据
	 */
	static async delete(url, options = {}) {
		return this.request({
			url,
			method: 'DELETE',
			encrypt: false,
			...options
		})
	}
}

/**
 * 简化的请求方法
 */
export const http = {
	get: (url, params, options) => EncryptedRequest.get(url, params, options),
	post: (url, data, options) => EncryptedRequest.post(url, data, options),
	put: (url, data, options) => EncryptedRequest.put(url, data, options),
	delete: (url, options) => EncryptedRequest.delete(url, options)
}

/**
 * 请求拦截器 - 自动处理登录状态
 */
export const createAuthenticatedRequest = (authComposable) => {
	return {
		async get(url, params, options = {}) {
			if (options.needAuth !== false) {
				await authComposable.ensureLogin()
			}
			return http.get(url, params, options)
		},
		
		async post(url, data, options = {}) {
			if (options.needAuth !== false) {
				await authComposable.ensureLogin()
			}
			return http.post(url, data, options)
		},
		
		async put(url, data, options = {}) {
			if (options.needAuth !== false) {
				await authComposable.ensureLogin()
			}
			return http.put(url, data, options)
		},
		
		async delete(url, options = {}) {
			if (options.needAuth !== false) {
				await authComposable.ensureLogin()
			}
			return http.delete(url, options)
		}
	}
}
