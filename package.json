{"name": "ai-bank-assistant", "version": "1.0.0", "description": "AI联行助手 - 银行联行号查询小程序", "main": "main.js", "scripts": {"dev": "uni-app dev", "build": "uni-app build", "build:mp-weixin": "uni-app build --platform mp-weixin", "build:h5": "uni-app build --platform h5", "build:app": "uni-app build --platform app"}, "keywords": ["uni-app", "vue3", "bank", "miniprogram", "wechat"], "author": "Your Name", "license": "MIT", "dependencies": {"crypto-js": "^4.1.1"}, "devDependencies": {"@dcloudio/types": "^3.0.0"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/ai-bank-assistant.git"}, "bugs": {"url": "https://github.com/your-username/ai-bank-assistant/issues"}, "homepage": "https://github.com/your-username/ai-bank-assistant#readme"}