"use strict";
const common_vendor = require("../common/vendor.js");
const utils_api = require("../utils/api.js");
function useBankSearch() {
  const searchKeyword = common_vendor.ref("");
  const searchResults = common_vendor.ref([]);
  const isLoading = common_vendor.ref(false);
  const error = common_vendor.ref(null);
  const hasResults = common_vendor.computed(() => searchResults.value.length > 0);
  const showNoResult = common_vendor.computed(() => !isLoading.value && !hasResults.value && searchKeyword.value.trim() !== "");
  const isSearchDisabled = common_vendor.computed(() => isLoading.value || searchKeyword.value.trim() === "");
  const executeSearch = async () => {
    if (!searchKeyword.value.trim()) {
      common_vendor.index.showToast({
        title: "请输入搜索关键字",
        icon: "none"
      });
      return;
    }
    isLoading.value = true;
    error.value = null;
    searchResults.value = [];
    try {
      const result = await utils_api.BankCodeAPI.searchBankCode(searchKeyword.value);
      searchResults.value = result || [];
      if (result && result.length === 0) {
        common_vendor.index.showToast({
          title: "未找到相关结果",
          icon: "none"
        });
      }
    } catch (err) {
      error.value = err.message || "查询失败";
      common_vendor.index.__f__("error", "at composables/useBankSearch.js:49", "查询失败:", err);
      common_vendor.index.showToast({
        title: "查询失败，请重试",
        icon: "none"
      });
    } finally {
      isLoading.value = false;
    }
  };
  const pasteAndSearch = async () => {
    try {
      const clipboardData = await common_vendor.index.getClipboardData();
      if (clipboardData.data && clipboardData.data.trim()) {
        searchKeyword.value = clipboardData.data.trim();
        await executeSearch();
      } else {
        common_vendor.index.showToast({
          title: "剪贴板为空",
          icon: "none"
        });
      }
    } catch (err) {
      common_vendor.index.__f__("error", "at composables/useBankSearch.js:75", "粘贴失败:", err);
      common_vendor.index.showToast({
        title: "粘贴失败",
        icon: "none"
      });
    }
  };
  const clearSearch = () => {
    searchKeyword.value = "";
    searchResults.value = [];
    error.value = null;
  };
  const selectResult = (item) => {
    if (!item.code)
      return;
    common_vendor.index.setClipboardData({
      data: item.code,
      success: () => {
        common_vendor.index.showToast({
          title: "联行号已复制",
          icon: "success"
        });
      },
      fail: () => {
        common_vendor.index.showToast({
          title: "复制失败",
          icon: "none"
        });
      }
    });
  };
  const refreshSearch = () => {
    if (searchKeyword.value.trim()) {
      executeSearch();
    }
  };
  return {
    // 响应式状态
    searchKeyword,
    searchResults,
    isLoading,
    error,
    // 计算属性
    hasResults,
    showNoResult,
    isSearchDisabled,
    // 方法
    executeSearch,
    pasteAndSearch,
    clearSearch,
    selectResult,
    refreshSearch
  };
}
function useBankCodeFormatter() {
  const formatCode = (code) => {
    if (!code)
      return "";
    return code.replace(/(\d{3})(\d{3})(\d{3})(\d{3})/, "$1-$2-$3-$4");
  };
  const validateCode = (code) => {
    if (!code)
      return false;
    const pattern = /^\d{12}$/;
    return pattern.test(code.replace(/[-\s]/g, ""));
  };
  const highlightKeyword = (text, keyword) => {
    if (!text || !keyword)
      return text;
    const regex = new RegExp(`(${keyword})`, "gi");
    return text.replace(regex, '<span class="highlight">$1</span>');
  };
  return {
    formatCode,
    validateCode,
    highlightKeyword
  };
}
exports.useBankCodeFormatter = useBankCodeFormatter;
exports.useBankSearch = useBankSearch;
//# sourceMappingURL=../../.sourcemap/mp-weixin/composables/useBankSearch.js.map
