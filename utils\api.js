// 银行代码查询API工具类
import { detectSearchType, getBankTypeByCode, SEARCH_TYPES } from '@/types/bank.js'
import { http } from '@/utils/request.js'

const API_BASE_URL = 'https://api.chahanghao.com'; // 替换为实际的API地址

/**
 * 银行代码查询API
 */
export class BankCodeAPI {

	/**
	 * 搜索银行代码
	 * @param {string} keyword - 搜索关键字（银行名称、地区、联行号等）
	 * @returns {Promise<Array>} 查询结果数组
	 */
	static async searchBankCode(keyword) {
		try {
			// 检测搜索类型
			const searchType = detectSearchType(keyword)
			console.log(`搜索类型: ${searchType}, 关键字: ${keyword}`)

			// 实际项目中调用加密API接口
			try {
				const response = await http.post('/bank/search', {
					keyword: keyword,
					type: searchType,
					limit: 20,
					timestamp: Date.now()
				})

				if (response.success && response.data) {
					return response.data.results || []
				}
			} catch (apiError) {
				console.log('API请求失败，使用模拟数据:', apiError.message)
			}

			// 如果API请求失败，使用模拟数据进行演示
			return new Promise((resolve) => {
				setTimeout(() => {
					const mockData = this.getMockData(keyword, searchType);
					resolve(mockData);
				}, 800); // 模拟网络延迟
			});

		} catch (error) {
			console.error('银行代码查询失败:', error);
			throw new Error('查询失败，请检查网络连接');
		}
	}
	
	/**
	 * 获取模拟数据（用于演示）
	 * @param {string} keyword - 搜索关键字
	 * @param {string} searchType - 搜索类型
	 * @returns {Array} 模拟的查询结果
	 */
	static getMockData(keyword, searchType = 'keyword') {
		const allMockData = [
			{
				code: '************',
				bankName: '中国工商银行股份有限公司北京国家文化与金融合作示范区支行',
				branchName: '营业部：中国工商银行股份有限公司北京王府井支行',
				distance: '100',
				address: '北京市东城区王府井大街',
				phone: '010-********'
			},
			{
				code: '************',
				bankName: '中国工商银行股份有限公司北京分行',
				branchName: '营业部：中国工商银行股份有限公司北京分行营业部',
				distance: '200',
				address: '北京市西城区复兴门内大街',
				phone: '010-********'
			},
			{
				code: '************',
				bankName: '中国农业银行股份有限公司北京分行',
				branchName: '营业部：中国农业银行股份有限公司北京分行营业部',
				distance: '150',
				address: '北京市东城区建国门内大街',
				phone: '010-********'
			},
			{
				code: '************',
				bankName: '中国银行股份有限公司北京分行',
				branchName: '营业部：中国银行股份有限公司北京分行营业部',
				distance: '180',
				address: '北京市西城区复兴门外大街',
				phone: '010-********'
			},
			{
				code: '************',
				bankName: '中国建设银行股份有限公司北京分行',
				branchName: '营业部：中国建设银行股份有限公司北京分行营业部',
				distance: '120',
				address: '北京市西城区金融大街',
				phone: '010-********'
			}
		];
		
		// 根据关键字和搜索类型过滤数据
		const keyword_lower = keyword.toLowerCase();

		return allMockData.filter(item => {
			// 精确联行号匹配
			if (searchType === SEARCH_TYPES.BANK_CODE) {
				return item.code === keyword || item.code.includes(keyword);
			}

			// 银行名称匹配
			if (searchType === SEARCH_TYPES.BANK_NAME) {
				return item.bankName.includes(keyword) ||
					   item.branchName.includes(keyword) ||
					   (keyword_lower.includes('工行') && item.bankName.includes('工商银行')) ||
					   (keyword_lower.includes('农行') && item.bankName.includes('农业银行')) ||
					   (keyword_lower.includes('中行') && item.bankName.includes('中国银行')) ||
					   (keyword_lower.includes('建行') && item.bankName.includes('建设银行'));
			}

			// 地区匹配
			if (searchType === SEARCH_TYPES.REGION) {
				return item.address && item.address.includes(keyword);
			}

			// 通用关键字匹配
			return item.bankName.includes(keyword) ||
				   item.branchName.includes(keyword) ||
				   item.code.includes(keyword) ||
				   (item.address && item.address.includes(keyword)) ||
				   (keyword_lower.includes('工行') && item.bankName.includes('工商银行')) ||
				   (keyword_lower.includes('农行') && item.bankName.includes('农业银行')) ||
				   (keyword_lower.includes('中行') && item.bankName.includes('中国银行')) ||
				   (keyword_lower.includes('建行') && item.bankName.includes('建设银行')) ||
				   (keyword_lower.includes('北京') && item.address && item.address.includes('北京'));
		});
	}
	
	/**
	 * 根据联行号获取详细信息
	 * @param {string} bankCode - 联行号
	 * @returns {Promise<Object>} 银行详细信息
	 */
	static async getBankDetail(bankCode) {
		try {
			// 实际项目中调用真实API
			// const response = await uni.request({
			// 	url: `${API_BASE_URL}/detail/${bankCode}`,
			// 	method: 'GET'
			// });
			// 
			// if (response.statusCode === 200 && response.data.success) {
			// 	return response.data.data;
			// }
			
			// 模拟数据
			const mockData = this.getMockData(bankCode, SEARCH_TYPES.BANK_CODE);
			return mockData.length > 0 ? mockData[0] : null;
			
		} catch (error) {
			console.error('获取银行详情失败:', error);
			throw new Error('获取详情失败');
		}
	}
}

/**
 * 工具函数：格式化银行代码显示
 * @param {string} code - 银行代码
 * @returns {string} 格式化后的代码
 */
export function formatBankCode(code) {
	if (!code) return '';
	// 将12位联行号格式化为 XXX-XXX-XXX-XXX 的形式
	return code.replace(/(\d{3})(\d{3})(\d{3})(\d{3})/, '$1-$2-$3-$4');
}

/**
 * 工具函数：验证联行号格式
 * @param {string} code - 银行代码
 * @returns {boolean} 是否为有效格式
 */
export function validateBankCode(code) {
	if (!code) return false;
	// 联行号通常为12位数字
	const pattern = /^\d{12}$/;
	return pattern.test(code.replace(/[-\s]/g, ''));
}
