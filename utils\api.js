// 银行代码查询API工具类
import { detectSearchType, getBankTypeByCode, SEARCH_TYPES } from '@/types/bank.js'
import { crypto } from '@/utils/crypto.js'
import { getApiUrl, ResponseHandler, ApiUtils, RETRY_CONFIG } from '@/config/api.js'

/**
 * 银行代码查询API
 */
export class BankCodeAPI {

	/**
	 * 搜索银行代码
	 * @param {string} keyword - 搜索关键字（银行名称、地区、联行号等）
	 * @returns {Promise<Array>} 查询结果数组
	 */
	static async searchBankCode(keyword) {
		// 验证搜索参数
		if (!ApiUtils.validateSearchParams({ keyword })) {
			throw new Error('搜索关键字不能为空')
		}

		// 检测搜索类型
		const searchType = detectSearchType(keyword)
		console.log(`搜索类型: ${searchType}, 关键字: ${keyword}`)

		// 构建标准化的请求参数
		const requestData = {
			openId: uni.getStorageSync('user_openid'),
			query: keyword
		}
		// console.log(requestData)

		// 尝试请求API，失败时使用模拟数据
		return await this.requestWithFallback(requestData, keyword, searchType)
	}

	/**
	 * 带降级的API请求
	 * @param {Object} requestData - 请求参数
	 * @param {string} keyword - 搜索关键字
	 * @param {string} searchType - 搜索类型
	 * @returns {Promise<Array>} 查询结果
	 */
	static async requestWithFallback(requestData, keyword, searchType) {
		let lastError = null

		// 重试机制
		for (let attempt = 0; attempt < 3; attempt++) {
			try {
				console.log(`银行查询尝试 ${attempt + 1}/${RETRY_CONFIG.getRetryDelay.length}`)

				// 使用混合加密
				const encryptedRequest = crypto.encryptRequest(requestData)
				console.log('银行查询请求已加密')

				// 调用实际的后台接口
				const response = await uni.request({
					url: getApiUrl('BANK_SEARCH'),
					method: 'POST',
					data: {
						parameter: encryptedRequest.encryptedData
					},
					header: {
						'Content-Type': 'application/json'
					},
					timeout: 10000
				})

				console.log('银行查询响应:', response)

				// 使用响应处理器处理结果
				const results = ResponseHandler.handleBankSearchResponse(response)

				// 解密响应数据
				if (response.statusCode == 200) {
					if (response.data.code == 200) {
						const decryptedData = crypto.decryptResponse(response.data.data, encryptedRequest.aesKey)
						console.log('银行查询解密成功:', decryptedData)

						// 格式化银行信息
						const formattedResults = Array.isArray(decryptedData.bankInfoList)
							? decryptedData.bankInfoList.map(item => ApiUtils.formatBankInfo(item))
							: [ApiUtils.formatBankInfo(decryptedData.bankInfoList)]
						console.log('银行查询结果已格式化:', formattedResults)
						return formattedResults.filter(item => item.code) // 过滤无效数据
					} else {
						uni.showToast({
							title: response.data.msg,
							icon: 'none',
							duration: 2000
						})
						return []
					}
					
				}

				return results

			} catch (error) {
				lastError = error
				console.error(`银行查询尝试 ${attempt + 1} 失败:`, error)

				// 检查是否应该重试
				if (!RETRY_CONFIG.shouldRetry(error, attempt)) {
					break
				}

				// 等待重试延迟
				if (attempt < 2) {
					const delay = RETRY_CONFIG.getRetryDelay(attempt)
					console.log(`等待 ${delay}ms 后重试...`)
					await new Promise(resolve => setTimeout(resolve, delay))
				}
			}
		}

		// 所有重试都失败，使用模拟数据
		console.log('API请求失败，使用模拟数据:', lastError?.message)
		return this.getMockData(keyword, searchType)
	}
	
	/**
	 * 获取模拟数据（用于演示）
	 * @param {string} keyword - 搜索关键字
	 * @param {string} searchType - 搜索类型
	 * @returns {Array} 模拟的查询结果
	 */
	static getMockData(keyword, searchType = 'keyword') {
		const allMockData = [
			{
				code: '************',
				bankName: '中国工商银行股份有限公司北京国家文化与金融合作示范区支行',
				branchName: '营业部：中国工商银行股份有限公司北京王府井支行',
				distance: '100',
				address: '北京市东城区王府井大街',
				phone: '010-********'
			},
			{
				code: '************',
				bankName: '中国工商银行股份有限公司北京分行',
				branchName: '营业部：中国工商银行股份有限公司北京分行营业部',
				distance: '200',
				address: '北京市西城区复兴门内大街',
				phone: '010-********'
			},
			{
				code: '************',
				bankName: '中国农业银行股份有限公司北京分行',
				branchName: '营业部：中国农业银行股份有限公司北京分行营业部',
				distance: '150',
				address: '北京市东城区建国门内大街',
				phone: '010-********'
			},
			{
				code: '************',
				bankName: '中国银行股份有限公司北京分行',
				branchName: '营业部：中国银行股份有限公司北京分行营业部',
				distance: '180',
				address: '北京市西城区复兴门外大街',
				phone: '010-********'
			},
			{
				code: '************',
				bankName: '中国建设银行股份有限公司北京分行',
				branchName: '营业部：中国建设银行股份有限公司北京分行营业部',
				distance: '120',
				address: '北京市西城区金融大街',
				phone: '010-********'
			}
		];
		
		// 根据关键字和搜索类型过滤数据
		const keyword_lower = keyword.toLowerCase();

		return allMockData.filter(item => {
			// 精确联行号匹配
			if (searchType === SEARCH_TYPES.BANK_CODE) {
				return item.code === keyword || item.code.includes(keyword);
			}

			// 银行名称匹配
			if (searchType === SEARCH_TYPES.BANK_NAME) {
				return item.bankName.includes(keyword) ||
					   item.branchName.includes(keyword) ||
					   (keyword_lower.includes('工行') && item.bankName.includes('工商银行')) ||
					   (keyword_lower.includes('农行') && item.bankName.includes('农业银行')) ||
					   (keyword_lower.includes('中行') && item.bankName.includes('中国银行')) ||
					   (keyword_lower.includes('建行') && item.bankName.includes('建设银行'));
			}

			// 地区匹配
			if (searchType === SEARCH_TYPES.REGION) {
				return item.address && item.address.includes(keyword);
			}

			// 通用关键字匹配
			return item.bankName.includes(keyword) ||
				   item.branchName.includes(keyword) ||
				   item.code.includes(keyword) ||
				   (item.address && item.address.includes(keyword)) ||
				   (keyword_lower.includes('工行') && item.bankName.includes('工商银行')) ||
				   (keyword_lower.includes('农行') && item.bankName.includes('农业银行')) ||
				   (keyword_lower.includes('中行') && item.bankName.includes('中国银行')) ||
				   (keyword_lower.includes('建行') && item.bankName.includes('建设银行')) ||
				   (keyword_lower.includes('北京') && item.address && item.address.includes('北京'));
		});
	}
	
	/**
	 * 根据联行号获取详细信息
	 * @param {string} bankCode - 联行号
	 * @returns {Promise<Object>} 银行详细信息
	 */
	static async getBankDetail(bankCode) {
		try {
			// 验证联行号格式
			if (!bankCode || typeof bankCode !== 'string' || bankCode.length !== 12) {
				throw new Error('联行号格式错误')
			}

			// 准备请求参数
			const requestData = {
				bankCode: bankCode,
				timestamp: Date.now()
			}

			// 使用混合加密
			const encryptedRequest = crypto.encryptRequest(requestData)
			console.log('银行详情查询请求已加密')

			// 调用实际的后台接口
			const response = await uni.request({
				url: getApiUrl('BANK_DETAIL'),
				method: 'POST',
				data: {
					parameter: encryptedRequest.encryptedData
				},
				header: {
					'Content-Type': 'application/json'
				},
				timeout: 10000
			})

			console.log('银行详情查询响应:', response)

			// 处理响应
			if (response.statusCode === 200 && response.data.code == 200) {
				// 解密响应数据
				const decryptedData = crypto.decryptResponse(response.data.data, encryptedRequest.aesKey)
				console.log('银行详情解密成功:', decryptedData)

				// 格式化银行信息
				return ApiUtils.formatBankInfo(decryptedData)
			}

			// API请求失败，使用模拟数据
			console.log('API请求失败，使用模拟数据')
			const mockData = this.getMockData(bankCode, SEARCH_TYPES.BANK_CODE)
			return mockData.length > 0 ? mockData[0] : null

		} catch (error) {
			console.error('获取银行详情失败:', error)

			// 网络错误时使用模拟数据
			if (error.errMsg && error.errMsg.includes('request:fail')) {
				console.log('网络请求失败，使用模拟数据')
				const mockData = this.getMockData(bankCode, SEARCH_TYPES.BANK_CODE)
				return mockData.length > 0 ? mockData[0] : null
			}

			throw new Error('获取详情失败: ' + error.message)
		}
	}
}

/**
 * 工具函数：格式化银行代码显示
 * @param {string} code - 银行代码
 * @returns {string} 格式化后的代码
 */
export function formatBankCode(code) {
	if (!code) return '';
	// 将12位联行号格式化为 XXX-XXX-XXX-XXX 的形式
	return code.replace(/(\d{3})(\d{3})(\d{3})(\d{3})/, '$1-$2-$3-$4');
}

/**
 * 工具函数：验证联行号格式
 * @param {string} code - 银行代码
 * @returns {boolean} 是否为有效格式
 */
export function validateBankCode(code) {
	if (!code) return false;
	// 联行号通常为12位数字
	const pattern = /^\d{12}$/;
	return pattern.test(code.replace(/[-\s]/g, ''));
}
