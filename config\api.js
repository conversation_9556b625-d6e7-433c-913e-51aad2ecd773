// API接口配置文件

/**
 * API基础配置
 */
export const API_CONFIG = {
	// 基础URL
	BASE_URL: 'https://hd.peixue100.cn/ylhapp',
	
	// 接口路径
	ENDPOINTS: {
		// 用户认证相关
		LOGIN: '/bankNum/jscode2session',
		
		// 银行查询相关
		BANK_SEARCH: '/bankNum/queryBankInfo',
		BANK_DETAIL: '/bankNum/detail',
		BANK_LIST: '/bankNum/list',
		
		// 其他接口
		USER_PROFILE: '/bankNum/user/profile',
		FEEDBACK: '/bankNum/feedback'
	},
	
	// 请求配置
	REQUEST: {
		TIMEOUT: 10000,
		RETRY_COUNT: 3,
		RETRY_DELAY: 1000
	},
	
	// 响应状态码
	STATUS_CODES: {
		SUCCESS: 200,
		UNAUTHORIZED: 401,
		FORBIDDEN: 403,
		NOT_FOUND: 404,
		SERVER_ERROR: 500
	},
	
	// 业务状态码
	BUSINESS_CODES: {
		SUCCESS: 200,
		PARAM_ERROR: 400,
		AUTH_FAILED: 401,
		NO_DATA: 404,
		SERVER_ERROR: 500
	}
}

/**
 * 获取完整的API URL
 * @param {string} endpoint - 接口路径
 * @returns {string} 完整的URL
 */
export function getApiUrl(endpoint) {
	const baseUrl = API_CONFIG.BASE_URL
	const path = API_CONFIG.ENDPOINTS[endpoint] || endpoint
	return `${baseUrl}${path}`
}

/**
 * 银行查询相关的API配置
 */
export const BANK_API_CONFIG = {
	// 查询参数配置
	SEARCH_PARAMS: {
		// 默认查询数量
		DEFAULT_LIMIT: 20,
		
		// 最大查询数量
		MAX_LIMIT: 100,
		
		// 支持的搜索类型
		SEARCH_TYPES: {
			KEYWORD: 'keyword',      // 关键字搜索
			BANK_CODE: 'bank_code',  // 联行号搜索
			BANK_NAME: 'bank_name',  // 银行名称搜索
			REGION: 'region'         // 地区搜索
		}
	},
	
	// 响应数据格式
	RESPONSE_FORMAT: {
		// 标准响应格式
		STANDARD: {
			code: 'number',      // 状态码
			message: 'string',   // 消息
			data: 'object',      // 数据
			timestamp: 'number'  // 时间戳
		},
		
		// 银行信息格式
		BANK_INFO: {
			code: 'string',        // 联行号
			bankName: 'string',    // 银行名称
			branchName: 'string',  // 支行名称
			address: 'string',     // 地址
			phone: 'string',       // 电话
			distance: 'string'     // 距离
		}
	}
}

/**
 * 请求重试配置
 */
export const RETRY_CONFIG = {
	// 需要重试的错误类型
	RETRY_CONDITIONS: [
		'request:fail',
		'timeout',
		'network error',
		'connection refused'
	],
	
	// 重试延迟策略（指数退避）
	getRetryDelay: (attempt) => {
		return Math.min(1000 * Math.pow(2, attempt), 10000)
	},
	
	// 是否应该重试
	shouldRetry: (error, attempt) => {
		if (attempt >= API_CONFIG.REQUEST.RETRY_COUNT) {
			return false
		}
		
		if (error.errMsg) {
			return RETRY_CONFIG.RETRY_CONDITIONS.some(condition => 
				error.errMsg.includes(condition)
			)
		}
		
		return false
	}
}

/**
 * API响应处理器
 */
export const ResponseHandler = {
	/**
	 * 处理标准响应
	 * @param {Object} response - 原始响应
	 * @returns {Object} 处理后的响应
	 */
	handleStandardResponse(response) {
		// 检查HTTP状态码
		if (response.statusCode !== API_CONFIG.STATUS_CODES.SUCCESS) {
			throw new Error(`HTTP错误: ${response.statusCode}`)
		}
		
		// // 检查业务状态码
		// if (response.data.code !== API_CONFIG.BUSINESS_CODES.SUCCESS) {
		// 	throw new Error(response.data.message || '业务处理失败')
		// }
		
		return {
			success: true,
			data: response.data.data,
			message: response.data.message,
			timestamp: response.data.timestamp || Date.now()
		}
	},
	
	/**
	 * 处理银行查询响应
	 * @param {Object} response - 原始响应
	 * @returns {Array} 银行信息数组
	 */
	handleBankSearchResponse(response) {
		const standardResponse = this.handleStandardResponse(response)
		
		if (!standardResponse.data) {
			return []
		}
		
		// 确保返回数组格式
		const results = Array.isArray(standardResponse.data) 
			? standardResponse.data 
			: [standardResponse.data]
		
		// 验证数据格式
		return results.filter(item => item && item.code)
	},
	
	/**
	 * 处理错误响应
	 * @param {Error} error - 错误对象
	 * @returns {Object} 标准化的错误信息
	 */
	handleErrorResponse(error) {
		let errorMessage = '未知错误'
		let errorCode = 'UNKNOWN_ERROR'
		
		if (error.errMsg) {
			if (error.errMsg.includes('request:fail')) {
				errorMessage = '网络请求失败'
				errorCode = 'NETWORK_ERROR'
			} else if (error.errMsg.includes('timeout')) {
				errorMessage = '请求超时'
				errorCode = 'TIMEOUT_ERROR'
			}
		} else if (error.message) {
			errorMessage = error.message
			errorCode = 'BUSINESS_ERROR'
		}
		
		return {
			success: false,
			error: errorMessage,
			code: errorCode,
			timestamp: Date.now()
		}
	}
}

/**
 * API请求工具函数
 */
export const ApiUtils = {
	/**
	 * 构建查询参数
	 * @param {Object} params - 参数对象
	 * @returns {Object} 标准化的参数
	 */
	buildSearchParams(params) {
		return {
			keyword: params.keyword || '',
			type: params.type || BANK_API_CONFIG.SEARCH_PARAMS.SEARCH_TYPES.KEYWORD,
			limit: Math.min(
				params.limit || BANK_API_CONFIG.SEARCH_PARAMS.DEFAULT_LIMIT,
				BANK_API_CONFIG.SEARCH_PARAMS.MAX_LIMIT
			),
			timestamp: Date.now()
		}
	},
	
	/**
	 * 验证搜索参数
	 * @param {Object} params - 参数对象
	 * @returns {boolean} 是否有效
	 */
	validateSearchParams(params) {
		if (!params.keyword || typeof params.keyword !== 'string') {
			return false
		}
		
		if (params.keyword.trim().length === 0) {
			return false
		}
		
		return true
	},
	
	/**
	 * 格式化银行信息
	 * @param {Object} bankInfo - 原始银行信息
	 * @returns {Object} 格式化后的银行信息
	 */
	formatBankInfo(bankInfo) {
		return {
			code: bankInfo.bankNum || '',
			bankName: bankInfo.bankDetail || bankInfo.bankDetail || '',
			branchName: bankInfo.bankDetail || bankInfo.bankDetail || '',
			address: bankInfo.bankDetail || '',
			phone: bankInfo.phone || '',
			distance: bankInfo.matching || ''
		}
	}
}

/**
 * 获取API配置
 * @returns {Object} API配置对象
 */
export function getApiConfig() {
	return {
		...API_CONFIG,
		BANK_API: BANK_API_CONFIG,
		RETRY: RETRY_CONFIG
	}
}
