// 用户认证组合式函数
import { ref, computed } from 'vue'
import { crypto } from '@/utils/crypto.js'

/**
 * 用户认证状态管理
 */
export function useAuth() {
	// 响应式状态
	const isLoggedIn = ref(false)
	const userInfo = ref(null)
	const openid = ref('')
	const isLogging = ref(false)
	const loginError = ref(null)
	
	// 计算属性
	const isAuthenticated = computed(() => isLoggedIn.value && openid.value)
	const needLogin = computed(() => !isAuthenticated.value)
	
	/**
	 * 检查登录状态
	 */
	const checkLoginStatus = () => {
		try {
			// 从本地存储获取用户信息
			const storedOpenid = uni.getStorageSync('user_openid')
			const storedUserInfo = uni.getStorageSync('user_info')
			
			if (storedOpenid) {
				openid.value = storedOpenid
				userInfo.value = JSON.parse(storedUserInfo)
				isLoggedIn.value = true
				console.log('用户已登录:', openid.value)
				return true
			}
			
			console.log('用户未登录')
			return false
		} catch (error) {
			console.error('检查登录状态失败:', error)
			return false
		}
	}
	
	/**
	 * 微信小程序登录
	 */
	const wxLogin = async () => {
		if (isLogging.value) return
		
		isLogging.value = true
		loginError.value = null
		
		try {
			console.log('开始微信登录...')
			
			// 1. 调用wx.login获取code
			const loginResult = await new Promise((resolve, reject) => {
				uni.login({
					provider: 'weixin',
					success: resolve,
					fail: reject
				})
			})
			
			if (!loginResult.code) {
				throw new Error('获取登录凭证失败')
			}
			
			console.log('获取到登录code:', loginResult.code)
			
			// 2. 向后台发送code获取openid
			const openidResult = await getOpenidFromServer(loginResult.code)
			
			if (openidResult.openId) {
				// 3. 保存用户信息
				openid.value = openidResult.openId
				userInfo.value = openidResult.userInfo || {}
				isLoggedIn.value = true
				
				// 4. 存储到本地
				uni.setStorageSync('user_openid', openid.value)
				uni.setStorageSync('user_info', JSON.stringify(userInfo.value))
				
				console.log('登录成功:', openid.value)
				
				// uni.showToast({
				// 	title: '登录成功',
				// 	icon: 'success'
				// })
				
				return true
			} else {
				throw new Error(openidResult.message || '登录失败')
			}
			
		} catch (error) {
			console.error('登录失败:', error)
			loginError.value = error.message || '登录失败'
			
			// uni.showToast({
			// 	title: loginError.value,
			// 	icon: 'none'
			// })
			
			return false
		} finally {
			isLogging.value = false
		}
	}
	
	/**
	 * 向服务器发送code获取openid
	 * @param {string} code - 微信登录code
	 * @returns {Promise<Object>} 服务器响应
	 */
	const getOpenidFromServer = async (code) => {
		try {
			// 准备请求参数
			const requestData = {
				weChatCode: code
			}
			
			// 使用混合加密
			const encryptedRequest = crypto.encryptRequest(requestData)
			
			console.log('发送登录请求到服务器...')
			
			// 发送请求到后台
			const response = await uni.request({
				url: 'https://hd.peixue100.cn/ylhapp/bankNum/jscode2session', // 替换为实际的API地址
				method: 'POST',
				data:{
					parameter: encryptedRequest.encryptedData
				},
				header: {
					'Content-Type': 'application/json'
				}
			})
			
			if (response.statusCode === 200 && response.data.code == 200) {
				// 解密响应数据
				const decryptedData = crypto.decryptResponse(response.data.data, encryptedRequest.aesKey)
				
				return decryptedData
			} else {
				throw new Error('服务器响应异常')
			}
			
		} catch (error) {
			console.error('获取openid失败:', error)
			
			// 如果是网络错误，返回模拟数据用于开发测试
			if (error.errMsg && error.errMsg.includes('request:fail')) {
				console.log('网络请求失败，使用模拟数据')
				return {
					success: true,
					openid: `mock_openid_${Date.now()}`,
					userInfo: {
						nickname: '测试用户',
						avatar: ''
					},
					message: '登录成功（模拟）'
				}
			}
			
			throw error
		}
	}
	
	/**
	 * 退出登录
	 */
	const logout = () => {
		try {
			// 清除状态
			isLoggedIn.value = false
			userInfo.value = null
			openid.value = ''
			loginError.value = null
			
			// 清除本地存储
			uni.removeStorageSync('user_openid')
			uni.removeStorageSync('user_info')
			
			console.log('用户已退出登录')
			
			uni.showToast({
				title: '已退出登录',
				icon: 'success'
			})
		} catch (error) {
			console.error('退出登录失败:', error)
		}
	}
	
	/**
	 * 确保用户已登录
	 * @returns {Promise<boolean>} 是否已登录
	 */
	const ensureLogin = async () => {
		// 先检查本地登录状态
		if (checkLoginStatus()) {
			return true
		}
		
		// 如果未登录，尝试自动登录
		return await wxLogin()
	}
	
	/**
	 * 获取用户授权信息
	 */
	const getUserProfile = async () => {
		try {
			const userProfile = await new Promise((resolve, reject) => {
				uni.getUserProfile({
					desc: '用于完善用户资料',
					success: resolve,
					fail: reject
				})
			})
			
			// 更新用户信息
			if (userProfile.userInfo) {
				userInfo.value = {
					...userInfo.value,
					...userProfile.userInfo
				}
				
				// 保存到本地
				uni.setStorageSync('user_info', JSON.stringify(userInfo.value))
			}
			
			return userProfile.userInfo
		} catch (error) {
			console.error('获取用户信息失败:', error)
			throw error
		}
	}
	
	// 返回响应式数据和方法
	return {
		// 响应式状态
		isLoggedIn,
		userInfo,
		openid,
		isLogging,
		loginError,
		
		// 计算属性
		isAuthenticated,
		needLogin,
		
		// 方法
		checkLoginStatus,
		wxLogin,
		logout,
		ensureLogin,
		getUserProfile
	}
}
