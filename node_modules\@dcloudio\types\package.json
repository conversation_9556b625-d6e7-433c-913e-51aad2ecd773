{"name": "@dcloudio/types", "version": "3.4.16", "description": "uni-app types", "typings": "index.d.ts", "scripts": {"tslint": "tslint --project ./ --fix", "dtslint": "dtslint ./", "test": "dtslint ./", "build:wx": "node ./scripts/build-wx.js", "build": "npm run build:wx && npm run build:promisify", "prepublishOnly": "npm run test", "build:promisify": "node ./scripts/build-promisify.js", "publish:patch": "npm version patch && npm publish", "publish:minor": "npm version minor && npm publish", "publish:major": "npm version major  && npm publish", "postpublish": "npx cnpm sync @dcloudio/types"}, "author": "fxy060608", "license": "Apache-2.0", "devDependencies": {"@definitelytyped/dtslint": "^0.0.115", "miniprogram-api-typings": "3.7.1", "ts-morph": "^17.0.1", "tslint": "^5.14.0", "typescript": "5.0.4", "vue": "2.6"}, "packageManager": "pnpm@9.5.0+sha512.140036830124618d624a2187b50d04289d5a087f326c9edfc0ccd733d76c4f52c3a313d4fc148794a2a9d81553016004e6742e8cf850670268a7387fc220c903"}