// 混合加密工具类
// 注意：在实际项目中建议使用 crypto-js 库
// 这里提供简化版本用于演示

/**
 * 混合加密工具类
 * 使用RSA加密AES密钥，AES加密实际数据
 */
export class CryptoUtil {
	
	// RSA公钥 - 实际项目中应该从服务器获取
	static RSA_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef...
-----END PUBLIC KEY-----`
	
	/**
	 * 生成随机AES密钥
	 * @returns {string} 32位随机字符串
	 */
	static generateAESKey() {
		const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
		let result = ''
		for (let i = 0; i < 32; i++) {
			result += chars.charAt(Math.floor(Math.random() * chars.length))
		}
		return result
	}
	
	/**
	 * 简化的AES加密（Base64编码模拟）
	 * @param {string} data - 要加密的数据
	 * @param {string} key - AES密钥
	 * @returns {string} 加密后的数据
	 */
	static aesEncrypt(data, key) {
		try {
			// 简化版本：使用Base64编码模拟AES加密
			// 实际项目中应该使用真正的AES加密
			const combined = `${key}:${data}`
			const encoded = btoa(unescape(encodeURIComponent(combined)))
			return `AES_${encoded}`
		} catch (error) {
			console.error('AES加密失败:', error)
			throw new Error('数据加密失败')
		}
	}

	/**
	 * 简化的AES解密（Base64解码模拟）
	 * @param {string} encryptedData - 加密的数据
	 * @param {string} key - AES密钥
	 * @returns {string} 解密后的数据
	 */
	static aesDecrypt(encryptedData, key) {
		try {
			// 简化版本：使用Base64解码模拟AES解密
			if (!encryptedData.startsWith('AES_')) {
				throw new Error('无效的加密数据格式')
			}

			const encoded = encryptedData.substring(4)
			const decoded = decodeURIComponent(escape(atob(encoded)))
			const [dataKey, ...dataParts] = decoded.split(':')

			if (dataKey !== key) {
				throw new Error('密钥不匹配')
			}

			return dataParts.join(':')
		} catch (error) {
			console.error('AES解密失败:', error)
			throw new Error('数据解密失败')
		}
	}
	
	/**
	 * RSA加密（使用JSEncrypt库）
	 * @param {string} data - 要加密的数据
	 * @returns {string} 加密后的数据
	 */
	static rsaEncrypt(data) {
		try {
			// 注意：在实际项目中需要引入JSEncrypt库
			// import JSEncrypt from 'jsencrypt'
			
			// 模拟RSA加密过程
			// const encrypt = new JSEncrypt()
			// encrypt.setPublicKey(this.RSA_PUBLIC_KEY)
			// return encrypt.encrypt(data)
			
			// 临时模拟加密结果
			const base64Data = btoa(unescape(encodeURIComponent(data)))
			return `RSA_ENCRYPTED_${base64Data}`
		} catch (error) {
			console.error('RSA加密失败:', error)
			throw new Error('密钥加密失败')
		}
	}
	
	/**
	 * 混合加密请求参数
	 * @param {Object} params - 请求参数
	 * @returns {Object} 加密后的请求对象
	 */
	static encryptRequest(params) {
		try {
			// 1. 生成AES密钥
			const aesKey = this.generateAESKey()
			
			// 2. 将参数转换为JSON字符串并用AES加密
			const paramsJson = JSON.stringify(params)
			const encryptedParams = this.aesEncrypt(paramsJson, aesKey)
			
			// 3. 创建包含AES密钥的对象
			const keyObject = { aesKey: aesKey }
			const keyJson = JSON.stringify(keyObject)
			
			// 4. 用RSA加密AES密钥
			const encryptedKey = this.rsaEncrypt(keyJson)
			
			// 5. 返回加密后的请求对象
			return {
				encryptedData: encryptedParams,
				encryptedKey: encryptedKey
			}
		} catch (error) {
			console.error('请求加密失败:', error)
			throw new Error('请求加密失败')
		}
	}
	
	/**
	 * 解密响应数据
	 * @param {string} encryptedResponse - 加密的响应数据
	 * @param {string} aesKey - AES密钥
	 * @returns {Object} 解密后的响应对象
	 */
	static decryptResponse(encryptedResponse, aesKey) {
		try {
			const decryptedJson = this.aesDecrypt(encryptedResponse, aesKey)
			return JSON.parse(decryptedJson)
		} catch (error) {
			console.error('响应解密失败:', error)
			throw new Error('响应解密失败')
		}
	}
}

/**
 * 简化的加密工具函数
 */
export const crypto = {
	/**
	 * 加密请求
	 * @param {Object} data - 请求数据
	 * @returns {Object} 加密后的请求
	 */
	encryptRequest: (data) => CryptoUtil.encryptRequest(data),
	
	/**
	 * 解密响应
	 * @param {string} encryptedData - 加密的响应数据
	 * @param {string} aesKey - AES密钥
	 * @returns {Object} 解密后的数据
	 */
	decryptResponse: (encryptedData, aesKey) => CryptoUtil.decryptResponse(encryptedData, aesKey),
	
	/**
	 * 生成AES密钥
	 * @returns {string} AES密钥
	 */
	generateAESKey: () => CryptoUtil.generateAESKey()
}
