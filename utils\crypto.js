// 混合加密工具类 - 使用真实的加密算法
import CryptoJS from 'crypto-js'
import JSEncrypt from 'jsencrypt'
import { getRSAPublicKey, getAESConfig, getRSAConfig, isDebugEnabled } from '@/config/crypto.js'

/**
 * 混合加密工具类
 * 使用RSA加密AES密钥，AES加密实际数据
 */
export class CryptoUtil {

	/**
	 * 获取当前RSA公钥
	 * @returns {string} RSA公钥
	 */
	static getRSAPublicKey() {
		return getRSAPublicKey()
	}

	/**
	 * 设置RSA公钥（更新配置）
	 * @param {string} publicKey - RSA公钥
	 */
	static setRSAPublicKey(publicKey) {
		if (this.validateRSAPublicKey(publicKey)) {
			// 这里应该更新配置，实际项目中可能需要调用API
			console.log('RSA公钥已更新')
		} else {
			throw new Error('无效的RSA公钥格式')
		}
	}
	
	/**
	 * 生成随机AES密钥
	 * @returns {string} 指定长度的随机字符串
	 */
	static generateAESKey() {
		const config = getAESConfig()
		const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
		let result = ''
		for (let i = 0; i < config.KEY_LENGTH; i++) {
			result += chars.charAt(Math.floor(Math.random() * chars.length))
		}

		if (isDebugEnabled()) {
			console.log(`生成AES密钥，长度: ${config.KEY_LENGTH}`)
		}

		return result
	}
	
	/**
	 * AES加密 - 使用CryptoJS实现真正的AES加密
	 * @param {string} data - 要加密的数据
	 * @param {string} key - AES密钥
	 * @returns {string} 加密后的数据
	 */
	static aesEncrypt(data, key) {
		try {
			const config = getAESConfig()

			if (isDebugEnabled()) {
				console.log('AES加密开始，数据长度:', data.length)
			}

			// 使用CryptoJS进行AES加密
			const encrypted = CryptoJS.AES.encrypt(data, key, {
				iv: iv,
				mode: CryptoJS.mode[config.MODE],
				padding: CryptoJS.pad[config.PADDING]
			})

			const result = encrypted.toString()

			if (isDebugEnabled()) {
				console.log('AES加密完成，结果长度:', result.length)
			}

			return result
		} catch (error) {
			console.error('AES加密失败:', error)
			throw new Error('数据加密失败: ' + error.message)
		}
	}

	/**
	 * AES解密 - 使用CryptoJS实现真正的AES解密
	 * @param {string} encryptedData - 加密的数据
	 * @param {string} key - AES密钥
	 * @returns {string} 解密后的数据
	 */
	static aesDecrypt(encryptedData, key) {
		try {
			const config = getAESConfig()

			if (isDebugEnabled()) {
				console.log('AES解密开始，数据长度:', encryptedData.length)
			}

			// 使用CryptoJS进行AES解密
			const decrypted = CryptoJS.AES.decrypt(encryptedData, key, {
				iv: iv,
				mode: CryptoJS.mode[config.MODE],
				padding: CryptoJS.pad[config.PADDING]
			})

			const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)

			if (!decryptedText) {
				throw new Error('解密结果为空，可能是密钥错误或数据损坏')
			}

			if (isDebugEnabled()) {
				console.log('AES解密完成，结果长度:', decryptedText.length)
			}

			return decryptedText
		} catch (error) {
			console.error('AES解密失败:', error)
			throw new Error('数据解密失败: ' + error.message)
		}
	}
	
	/**
	 * RSA加密 - 使用JSEncrypt实现真正的RSA加密
	 * @param {string} data - 要加密的数据
	 * @returns {string} 加密后的数据
	 */
	static rsaEncrypt(data) {
		try {
			const publicKey = this.getRSAPublicKey()

			if (isDebugEnabled()) {
				console.log('RSA加密开始，数据长度:', data.length)
				console.log('使用公钥长度:', publicKey.length)
			}

			// 检查数据长度（RSA有长度限制）
			if (data.length > 200) {
				console.warn('RSA加密数据较长，可能失败。建议数据长度小于200字符')
			}

			// 使用JSEncrypt进行RSA加密
			const encrypt = new JSEncrypt()
			encrypt.setPublicKey(publicKey)

			const encrypted = encrypt.encrypt(data)
			if (!encrypted) {
				throw new Error('RSA加密失败，可能是公钥格式错误或数据过长')
			}

			if (isDebugEnabled()) {
				console.log('RSA加密完成，结果长度:', encrypted.length)
			}

			return encrypted
		} catch (error) {
			console.error('RSA加密失败:', error)
			throw new Error('密钥加密失败: ' + error.message)
		}
	}

	/**
	 * RSA解密 - 使用JSEncrypt实现真正的RSA解密（需要私钥，通常在服务器端）
	 * @param {string} encryptedData - 加密的数据
	 * @param {string} privateKey - RSA私钥
	 * @returns {string} 解密后的数据
	 */
	static rsaDecrypt(encryptedData, privateKey) {
		try {
			// 使用JSEncrypt进行RSA解密
			const decrypt = new JSEncrypt()
			decrypt.setPrivateKey(privateKey)

			const decrypted = decrypt.decrypt(encryptedData)
			if (!decrypted) {
				throw new Error('RSA解密失败，可能是私钥错误或数据损坏')
			}

			return decrypted
		} catch (error) {
			console.error('RSA解密失败:', error)
			throw new Error('密钥解密失败')
		}
	}
	
	/**
	 * 混合加密请求参数
	 * @param {Object} params - 请求参数
	 * @returns {Object} 加密后的请求对象，包含AES密钥用于后续解密响应
	 */
	static encryptRequest(params) {
		try {
			if (isDebugEnabled()) {
				console.log('开始混合加密，参数:', JSON.stringify(params))
			}

			// 1. 生成AES密钥
			const aesKey = this.generateAESKey()
			params.aesKey = aesKey
			if (isDebugEnabled()) {
				console.log('生成AES密钥:', aesKey.substring(0, 8) + '...')
			}

			// 2. 将参数转换为JSON字符串并用AES加密
			const paramsJson = JSON.stringify(params)
			// const encryptedParams = this.aesEncrypt(paramsJson, aesKey)
			// 先采用直接使用RSA加密整个前端传参
			const encryptedParams = this.rsaEncrypt(paramsJson)

			// 3. 创建包含AES密钥的对象
			// 先采用直接使用RSA加密整个前端传参
			// const keyObject = { aesKey: aesKey }
			// const keyJson = JSON.stringify(keyObject)

			// 4. 用RSA加密AES密钥
			// 先采用直接使用RSA加密整个前端传参
			// const encryptedKey = this.rsaEncrypt(keyJson)

			if (isDebugEnabled()) {
				console.log('混合加密完成')
				console.log('- 加密数据长度:', encryptedParams.length)
				// console.log('- 加密密钥长度:', encryptedKey.length)// 先采用直接使用RSA加密整个前端传参
			}

			// 5. 返回加密后的请求对象，同时返回AES密钥用于解密响应
			return {
				encryptedData: encryptedParams,
				// encryptedKey: encryptedKey,// 先采用直接使用RSA加密整个前端传参
				aesKey: aesKey // 保存AES密钥用于解密响应
			}
		} catch (error) {
			console.error('请求加密失败:', error)
			throw new Error('请求加密失败: ' + error.message)
		}
	}
	
	/**
	 * 解密响应数据
	 * @param {string} encryptedResponse - 加密的响应数据
	 * @param {string} aesKey - AES密钥
	 * @returns {Object} 解密后的响应对象
	 */
	static decryptResponse(encryptedResponse, aesKey) {
		try {
			if (isDebugEnabled()) {
				console.log('开始解密响应数据')
				console.log('- 密钥:', aesKey.substring(0, 8) + '...')
				console.log('- 加密数据长度:', encryptedResponse.length)
			}

			const decryptedJson = this.aesDecrypt(encryptedResponse, aesKey)
			const responseData = JSON.parse(decryptedJson)

			if (isDebugEnabled()) {
				console.log('响应解密成功')
				console.log('- 解密数据长度:', decryptedJson.length)
			}

			return responseData
		} catch (error) {
			console.error('响应解密失败:', error)
			throw new Error('响应解密失败: ' + error.message)
		}
	}

	/**
	 * 验证RSA公钥格式
	 * @param {string} publicKey - RSA公钥
	 * @returns {boolean} 是否为有效格式
	 */
	static validateRSAPublicKey(publicKey) {
		try {
			const keyRegex = /^-----BEGIN PUBLIC KEY-----[\s\S]*-----END PUBLIC KEY-----$/
			return keyRegex.test(publicKey.trim())
		} catch (error) {
			return false
		}
	}

	/**
	 * 测试加密解密功能
	 * @returns {boolean} 测试是否通过
	 */
	static testEncryption() {
		try {
			console.log('开始加密解密测试...')

			// 测试AES加密解密
			const testData = 'Hello, World! 测试数据 123'
			const testKey = this.generateAESKey()

			const encrypted = this.aesEncrypt(testData, testKey)
			const decrypted = this.aesDecrypt(encrypted, testKey)

			if (decrypted !== testData) {
				throw new Error('AES加密解密测试失败')
			}

			console.log('AES加密解密测试通过')

			// 测试RSA加密（只测试加密，解密需要私钥）
			const testKeyObject = { aesKey: testKey }
			const rsaEncrypted = this.rsaEncrypt(JSON.stringify(testKeyObject))

			if (!rsaEncrypted || rsaEncrypted.length === 0) {
				throw new Error('RSA加密测试失败')
			}

			console.log('RSA加密测试通过')

			// 测试混合加密
			const testParams = { message: 'test', timestamp: Date.now() }
			const encryptedRequest = this.encryptRequest(testParams)

			if (!encryptedRequest.encryptedData || !encryptedRequest.encryptedKey) {
				throw new Error('混合加密测试失败')
			}

			console.log('混合加密测试通过')
			console.log('所有加密测试通过！')

			return true
		} catch (error) {
			console.error('加密测试失败:', error)
			return false
		}
	}
}

/**
 * 简化的加密工具函数
 */
export const crypto = {
	/**
	 * 加密请求
	 * @param {Object} data - 请求数据
	 * @returns {Object} 加密后的请求
	 */
	encryptRequest: (data) => CryptoUtil.encryptRequest(data),
	
	/**
	 * 解密响应
	 * @param {string} encryptedData - 加密的响应数据
	 * @param {string} aesKey - AES密钥
	 * @returns {Object} 解密后的数据
	 */
	decryptResponse: (encryptedData, aesKey) => CryptoUtil.decryptResponse(encryptedData, aesKey),
	
	/**
	 * 生成AES密钥
	 * @returns {string} AES密钥
	 */
	generateAESKey: () => CryptoUtil.generateAESKey(),
	/**
	 * 加密请求
	 * @param {Object} data - 请求数据
	 * @returns {Object} 加密后的请求
	 */
	rsaEncrypt: ()=>{CryptoUtil.rsaEncrypt(data)}
}
