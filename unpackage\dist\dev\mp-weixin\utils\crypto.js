"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
const common_vendor = require("../common/vendor.js");
class CryptoUtil {
  /**
   * 生成随机AES密钥
   * @returns {string} 32位随机字符串
   */
  static generateAESKey() {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  /**
   * 简化的AES加密（Base64编码模拟）
   * @param {string} data - 要加密的数据
   * @param {string} key - AES密钥
   * @returns {string} 加密后的数据
   */
  static aesEncrypt(data, key) {
    try {
      const combined = `${key}:${data}`;
      const encoded = btoa(unescape(encodeURIComponent(combined)));
      return `AES_${encoded}`;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:43", "AES加密失败:", error);
      throw new Error("数据加密失败");
    }
  }
  /**
   * 简化的AES解密（Base64解码模拟）
   * @param {string} encryptedData - 加密的数据
   * @param {string} key - AES密钥
   * @returns {string} 解密后的数据
   */
  static aesDecrypt(encryptedData, key) {
    try {
      if (!encryptedData.startsWith("AES_")) {
        throw new Error("无效的加密数据格式");
      }
      const encoded = encryptedData.substring(4);
      const decoded = decodeURIComponent(escape(atob(encoded)));
      const [dataKey, ...dataParts] = decoded.split(":");
      if (dataKey !== key) {
        throw new Error("密钥不匹配");
      }
      return dataParts.join(":");
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:71", "AES解密失败:", error);
      throw new Error("数据解密失败");
    }
  }
  /**
   * RSA加密（使用JSEncrypt库）
   * @param {string} data - 要加密的数据
   * @returns {string} 加密后的数据
   */
  static rsaEncrypt(data) {
    try {
      const base64Data = btoa(unescape(encodeURIComponent(data)));
      return `RSA_ENCRYPTED_${base64Data}`;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:95", "RSA加密失败:", error);
      throw new Error("密钥加密失败");
    }
  }
  /**
   * 混合加密请求参数
   * @param {Object} params - 请求参数
   * @returns {Object} 加密后的请求对象
   */
  static encryptRequest(params) {
    try {
      const aesKey = this.generateAESKey();
      const paramsJson = JSON.stringify(params);
      const encryptedParams = this.aesEncrypt(paramsJson, aesKey);
      const keyObject = { aesKey };
      const keyJson = JSON.stringify(keyObject);
      const encryptedKey = this.rsaEncrypt(keyJson);
      return {
        encryptedData: encryptedParams,
        encryptedKey
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:127", "请求加密失败:", error);
      throw new Error("请求加密失败");
    }
  }
  /**
   * 解密响应数据
   * @param {string} encryptedResponse - 加密的响应数据
   * @param {string} aesKey - AES密钥
   * @returns {Object} 解密后的响应对象
   */
  static decryptResponse(encryptedResponse, aesKey) {
    try {
      const decryptedJson = this.aesDecrypt(encryptedResponse, aesKey);
      return JSON.parse(decryptedJson);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:143", "响应解密失败:", error);
      throw new Error("响应解密失败");
    }
  }
}
// RSA公钥 - 实际项目中应该从服务器获取
__publicField(CryptoUtil, "RSA_PUBLIC_KEY", `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef...
-----END PUBLIC KEY-----`);
const crypto = {
  /**
   * 加密请求
   * @param {Object} data - 请求数据
   * @returns {Object} 加密后的请求
   */
  encryptRequest: (data) => CryptoUtil.encryptRequest(data),
  /**
   * 解密响应
   * @param {string} encryptedData - 加密的响应数据
   * @param {string} aesKey - AES密钥
   * @returns {Object} 解密后的数据
   */
  decryptResponse: (encryptedData, aesKey) => CryptoUtil.decryptResponse(encryptedData, aesKey),
  /**
   * 生成AES密钥
   * @returns {string} AES密钥
   */
  generateAESKey: () => CryptoUtil.generateAESKey()
};
exports.crypto = crypto;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/crypto.js.map
