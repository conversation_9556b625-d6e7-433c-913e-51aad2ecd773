"use strict";
const common_vendor = require("../common/vendor.js");
const config_crypto = require("../config/crypto.js");
class CryptoUtil {
  /**
   * 获取当前RSA公钥
   * @returns {string} RSA公钥
   */
  static getRSAPublicKey() {
    return config_crypto.getRSAPublicKey();
  }
  /**
   * 设置RSA公钥（更新配置）
   * @param {string} publicKey - RSA公钥
   */
  static setRSAPublicKey(publicKey) {
    if (this.validateRSAPublicKey(publicKey)) {
      common_vendor.index.__f__("log", "at utils/crypto.js:27", "RSA公钥已更新");
    } else {
      throw new Error("无效的RSA公钥格式");
    }
  }
  /**
   * 生成随机AES密钥
   * @returns {string} 指定长度的随机字符串
   */
  static generateAESKey() {
    const config = config_crypto.getAESConfig();
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < config.KEY_LENGTH; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
  /**
   * AES加密 - 生成随机IV并与加密数据组合
   * @param {string} data - 要加密的数据
   * @param {string} key - AES密钥
   * @returns {string} 加密后的数据（包含IV）
   */
  static aesEncrypt(data2, key) {
    try {
      const config = config_crypto.getAESConfig();
      if (config_crypto.isDebugEnabled())
        ;
      const iv = common_vendor.CryptoJS.lib.WordArray.random(16);
      if (config_crypto.isDebugEnabled())
        ;
      const encrypted = common_vendor.CryptoJS.AES.encrypt(data2, common_vendor.CryptoJS.enc.Utf8.parse(key), {
        iv,
        mode: common_vendor.CryptoJS.mode.CBC,
        // 使用CBC模式，因为有IV
        padding: common_vendor.CryptoJS.pad[config.PADDING]
      });
      const ivAndData = iv.concat(encrypted.ciphertext);
      const result = ivAndData.toString(common_vendor.CryptoJS.enc.Base64);
      if (config_crypto.isDebugEnabled())
        ;
      return result;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:97", "AES加密失败:", error);
      throw new Error("数据加密失败: " + error.message);
    }
  }
  /**
   * AES解密 - 从encryptedData中提取IV和实际加密数据进行解密
   * @param {string} encryptedData - 加密的数据（包含IV）
   * @param {string} key - AES密钥
   * @returns {string} 解密后的数据
   */
  static aesDecrypt(encryptedData, key) {
    try {
      const config = config_crypto.getAESConfig();
      if (config_crypto.isDebugEnabled())
        ;
      let base64Data = encryptedData;
      const encryptedBytes = common_vendor.CryptoJS.enc.Base64.parse(base64Data);
      if (config_crypto.isDebugEnabled())
        ;
      if (encryptedBytes.sigBytes < 16) {
        throw new Error("加密数据长度不足，无法提取IV");
      }
      const ivBytes = common_vendor.CryptoJS.lib.WordArray.create(encryptedBytes.words.slice(0, 4));
      ivBytes.sigBytes = 16;
      const actualDataBytes = common_vendor.CryptoJS.lib.WordArray.create(
        encryptedBytes.words.slice(4),
        encryptedBytes.sigBytes - 16
      );
      if (config_crypto.isDebugEnabled())
        ;
      const decrypted = common_vendor.CryptoJS.AES.decrypt(
        { ciphertext: actualDataBytes },
        common_vendor.CryptoJS.enc.Utf8.parse(key),
        {
          iv: ivBytes,
          mode: common_vendor.CryptoJS.mode.CBC,
          // 使用CBC模式，因为有IV
          padding: common_vendor.CryptoJS.pad[config.PADDING]
        }
      );
      const decryptedText = decrypted.toString(common_vendor.CryptoJS.enc.Utf8);
      if (!decryptedText) {
        throw new Error("解密结果为空，可能是密钥错误、IV错误或数据损坏");
      }
      if (config_crypto.isDebugEnabled())
        ;
      return decryptedText;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:175", "AES解密失败:", error);
      throw new Error("数据解密失败: " + error.message);
    }
  }
  /**
   * 检查字符串是否为有效的Base64格式
   * @param {string} str - 要检查的字符串
   * @returns {boolean} 是否为Base64格式
   */
  static isBase64(str) {
    try {
      const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
      if (!base64Regex.test(str)) {
        return false;
      }
      if (str.length % 4 !== 0) {
        return false;
      }
      atob(str);
      return true;
    } catch (error) {
      return false;
    }
  }
  /**
   * RSA加密 - 使用JSEncrypt实现真正的RSA加密
   * @param {string} data - 要加密的数据
   * @returns {string} 加密后的数据
   */
  static rsaEncrypt(data2) {
    try {
      const publicKey = this.getRSAPublicKey();
      if (config_crypto.isDebugEnabled())
        ;
      if (data2.length > 200) {
        common_vendor.index.__f__("warn", "at utils/crypto.js:224", "RSA加密数据较长，可能失败。建议数据长度小于200字符");
      }
      const encrypt = new common_vendor.JSEncrypt();
      encrypt.setPublicKey(publicKey);
      const encrypted = encrypt.encrypt(data2);
      if (!encrypted) {
        throw new Error("RSA加密失败，可能是公钥格式错误或数据过长");
      }
      if (config_crypto.isDebugEnabled())
        ;
      return encrypted;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:242", "RSA加密失败:", error);
      throw new Error("密钥加密失败: " + error.message);
    }
  }
  /**
   * RSA解密 - 使用JSEncrypt实现真正的RSA解密（需要私钥，通常在服务器端）
   * @param {string} encryptedData - 加密的数据
   * @param {string} privateKey - RSA私钥
   * @returns {string} 解密后的数据
   */
  static rsaDecrypt(encryptedData, privateKey) {
    try {
      const decrypt = new common_vendor.JSEncrypt();
      decrypt.setPrivateKey(privateKey);
      const decrypted = decrypt.decrypt(encryptedData);
      if (!decrypted) {
        throw new Error("RSA解密失败，可能是私钥错误或数据损坏");
      }
      return decrypted;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:266", "RSA解密失败:", error);
      throw new Error("密钥解密失败");
    }
  }
  /**
   * 混合加密请求参数
   * @param {Object} params - 请求参数
   * @returns {Object} 加密后的请求对象，包含AES密钥用于后续解密响应
   */
  static encryptRequest(params) {
    try {
      if (config_crypto.isDebugEnabled())
        ;
      const aesKey = this.generateAESKey();
      params.aesKey = aesKey;
      if (config_crypto.isDebugEnabled())
        ;
      const paramsJson = JSON.stringify(params);
      const encryptedParams = this.rsaEncrypt(paramsJson);
      if (config_crypto.isDebugEnabled())
        ;
      return {
        encryptedData: encryptedParams,
        // encryptedKey: encryptedKey,// 先采用直接使用RSA加密整个前端传参
        aesKey
        // 保存AES密钥用于解密响应
      };
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:317", "请求加密失败:", error);
      throw new Error("请求加密失败: " + error.message);
    }
  }
  /**
   * 解密响应数据
   * @param {string} encryptedResponse - 加密的响应数据
   * @param {string} aesKey - AES密钥
   * @returns {Object} 解密后的响应对象
   */
  static decryptResponse(encryptedResponse, aesKey) {
    try {
      if (config_crypto.isDebugEnabled())
        ;
      const decryptedJson = this.aesDecrypt(encryptedResponse, aesKey);
      const responseData = JSON.parse(decryptedJson);
      if (config_crypto.isDebugEnabled())
        ;
      return responseData;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:347", "响应解密失败:", error);
      throw new Error("响应解密失败: " + error.message);
    }
  }
  /**
   * 验证RSA公钥格式
   * @param {string} publicKey - RSA公钥
   * @returns {boolean} 是否为有效格式
   */
  static validateRSAPublicKey(publicKey) {
    try {
      const keyRegex = /^-----BEGIN PUBLIC KEY-----[\s\S]*-----END PUBLIC KEY-----$/;
      return keyRegex.test(publicKey.trim());
    } catch (error) {
      return false;
    }
  }
  /**
   * 测试加密解密功能
   * @returns {boolean} 测试是否通过
   */
  static testEncryption() {
    try {
      common_vendor.index.__f__("log", "at utils/crypto.js:372", "开始加密解密测试...");
      const testData = "Hello, World! 测试数据 123";
      const testKey = this.generateAESKey();
      const encrypted = this.aesEncrypt(testData, testKey);
      const decrypted = this.aesDecrypt(encrypted, testKey);
      if (decrypted !== testData) {
        throw new Error("AES加密解密测试失败");
      }
      common_vendor.index.__f__("log", "at utils/crypto.js:385", "AES加密解密测试通过");
      const testKeyObject = { aesKey: testKey };
      const rsaEncrypted = this.rsaEncrypt(JSON.stringify(testKeyObject));
      if (!rsaEncrypted || rsaEncrypted.length === 0) {
        throw new Error("RSA加密测试失败");
      }
      common_vendor.index.__f__("log", "at utils/crypto.js:395", "RSA加密测试通过");
      const testParams = { message: "test", timestamp: Date.now() };
      const encryptedRequest = this.encryptRequest(testParams);
      if (!encryptedRequest.encryptedData || !encryptedRequest.encryptedKey) {
        throw new Error("混合加密测试失败");
      }
      common_vendor.index.__f__("log", "at utils/crypto.js:405", "混合加密测试通过");
      common_vendor.index.__f__("log", "at utils/crypto.js:406", "所有加密测试通过！");
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/crypto.js:410", "加密测试失败:", error);
      return false;
    }
  }
}
const crypto = {
  /**
   * 加密请求
   * @param {Object} data - 请求数据
   * @returns {Object} 加密后的请求
   */
  encryptRequest: (data2) => CryptoUtil.encryptRequest(data2),
  /**
   * 解密响应
   * @param {string} encryptedData - 加密的响应数据
   * @param {string} aesKey - AES密钥
   * @returns {Object} 解密后的数据
   */
  decryptResponse: (encryptedData, aesKey) => CryptoUtil.decryptResponse(encryptedData, aesKey),
  /**
   * 生成AES密钥
   * @returns {string} AES密钥
   */
  generateAESKey: () => CryptoUtil.generateAESKey(),
  /**
   * 加密请求
   * @param {Object} data - 请求数据
   * @returns {Object} 加密后的请求
   */
  rsaEncrypt: () => {
    CryptoUtil.rsaEncrypt(data);
  }
};
exports.CryptoUtil = CryptoUtil;
exports.crypto = crypto;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/crypto.js.map
