// Vue 3 组合式函数 - 银行查询逻辑
import { ref, computed } from 'vue'
import { BankCodeAPI } from '@/utils/api.js'

/**
 * 银行查询组合式函数
 * @returns {Object} 查询相关的响应式数据和方法
 */
export function useBankSearch() {
	// 响应式状态
	const searchKeyword = ref('')
	const searchResults = ref([])
	const isLoading = ref(false)
	const error = ref(null)
	
	// 计算属性
	const hasResults = computed(() => searchResults.value.length > 0)
	const showNoResult = computed(() => !isLoading.value && !hasResults.value && searchKeyword.value.trim() !== '')
	const isSearchDisabled = computed(() => isLoading.value || searchKeyword.value.trim() === '')
	
	/**
	 * 执行搜索
	 */
	const executeSearch = async () => {
		if (!searchKeyword.value.trim()) {
			uni.showToast({
				title: '请输入搜索关键字',
				icon: 'none'
			})
			return
		}

		isLoading.value = true
		error.value = null
		searchResults.value = []

		try {
			const result = await BankCodeAPI.searchBankCode(searchKeyword.value)
			searchResults.value = result || []
			
			if (result && result.length === 0) {
				uni.showToast({
					title: '未找到相关结果',
					icon: 'none'
				})
			}
		} catch (err) {
			error.value = err.message || '查询失败'
			console.error('查询失败:', err)
			uni.showToast({
				title: '查询失败，请重试',
				icon: 'none'
			})
		} finally {
			isLoading.value = false
		}
	}

	/**
	 * 从剪贴板粘贴并搜索
	 */
	const pasteAndSearch = async () => {
		try {
			const clipboardData = await uni.getClipboardData()
			if (clipboardData.data && clipboardData.data.trim()) {
				searchKeyword.value = clipboardData.data.trim()
				await executeSearch()
			} else {
				uni.showToast({
					title: '剪贴板为空',
					icon: 'none'
				})
			}
		} catch (err) {
			console.error('粘贴失败:', err)
			uni.showToast({
				title: '粘贴失败',
				icon: 'none'
			})
		}
	}

	/**
	 * 清除搜索
	 */
	const clearSearch = () => {
		searchKeyword.value = ''
		searchResults.value = []
		error.value = null
	}

	/**
	 * 选择搜索结果
	 * @param {Object} item - 选中的银行信息
	 */
	const selectResult = (item) => {
		if (!item.code) return
		
		uni.setClipboardData({
			data: item.code,
			success: () => {
				uni.showToast({
					title: '联行号已复制',
					icon: 'success'
				})
			},
			fail: () => {
				uni.showToast({
					title: '复制失败',
					icon: 'none'
				})
			}
		})
	}

	/**
	 * 重新搜索（刷新）
	 */
	const refreshSearch = () => {
		if (searchKeyword.value.trim()) {
			executeSearch()
		}
	}

	// 返回响应式数据和方法
	return {
		// 响应式状态
		searchKeyword,
		searchResults,
		isLoading,
		error,
		
		// 计算属性
		hasResults,
		showNoResult,
		isSearchDisabled,
		
		// 方法
		executeSearch,
		pasteAndSearch,
		clearSearch,
		selectResult,
		refreshSearch
	}
}

/**
 * 银行代码格式化组合式函数
 */
export function useBankCodeFormatter() {
	/**
	 * 格式化银行代码显示
	 * @param {string} code - 银行代码
	 * @returns {string} 格式化后的代码
	 */
	const formatCode = (code) => {
		if (!code) return ''
		// 将12位联行号格式化为 XXX-XXX-XXX-XXX 的形式
		return code.replace(/(\d{3})(\d{3})(\d{3})(\d{3})/, '$1-$2-$3-$4')
	}

	/**
	 * 验证银行代码格式
	 * @param {string} code - 银行代码
	 * @returns {boolean} 是否为有效格式
	 */
	const validateCode = (code) => {
		if (!code) return false
		// 联行号通常为12位数字
		const pattern = /^\d{12}$/
		return pattern.test(code.replace(/[-\s]/g, ''))
	}

	/**
	 * 高亮显示搜索关键字
	 * @param {string} text - 原始文本
	 * @param {string} keyword - 搜索关键字
	 * @returns {string} 高亮后的文本
	 */
	const highlightKeyword = (text, keyword) => {
		if (!text || !keyword) return text
		const regex = new RegExp(`(${keyword})`, 'gi')
		return text.replace(regex, '<span class="highlight">$1</span>')
	}

	return {
		formatCode,
		validateCode,
		highlightKeyword
	}
}
