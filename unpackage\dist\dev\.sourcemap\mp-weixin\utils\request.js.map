{"version": 3, "file": "request.js", "sources": ["utils/request.js"], "sourcesContent": ["// 加密HTTP请求工具\nimport { crypto } from '@/utils/crypto.js'\n\n/**\n * API基础配置\n */\nconst API_CONFIG = {\n\tbaseURL: 'https://your-api-domain.com/api', // 替换为实际的API地址\n\ttimeout: 10000,\n\theader: {\n\t\t'Content-Type': 'application/json'\n\t}\n}\n\n/**\n * 加密HTTP请求类\n */\nexport class EncryptedRequest {\n\t\n\t/**\n\t * 发送加密请求\n\t * @param {Object} options - 请求选项\n\t * @returns {Promise<Object>} 响应数据\n\t */\n\tstatic async request(options) {\n\t\tconst {\n\t\t\turl,\n\t\t\tmethod = 'GET',\n\t\t\tdata = {},\n\t\t\theader = {},\n\t\t\tneedAuth = true,\n\t\t\tencrypt = true\n\t\t} = options\n\t\t\n\t\ttry {\n\t\t\t// 1. 准备请求数据\n\t\t\tlet requestData = data\n\t\t\tlet aesKey = null\n\t\t\t\n\t\t\t// 2. 如果需要加密\n\t\t\tif (encrypt && (method === 'POST' || method === 'PUT')) {\n\t\t\t\tconst encryptedRequest = crypto.encryptRequest(data)\n\t\t\t\trequestData = encryptedRequest\n\t\t\t\t// 保存AES密钥用于解密响应\n\t\t\t\taesKey = crypto.generateAESKey()\n\t\t\t}\n\t\t\t\n\t\t\t// 3. 添加认证信息\n\t\t\tconst requestHeader = { ...API_CONFIG.header, ...header }\n\t\t\tif (needAuth) {\n\t\t\t\tconst openid = uni.getStorageSync('user_openid')\n\t\t\t\tif (openid) {\n\t\t\t\t\trequestHeader['Authorization'] = `Bearer ${openid}`\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 4. 发送请求\n\t\t\tconsole.log(`发送${encrypt ? '加密' : '普通'}请求:`, {\n\t\t\t\turl: `${API_CONFIG.baseURL}${url}`,\n\t\t\t\tmethod,\n\t\t\t\tencrypt\n\t\t\t})\n\t\t\t\n\t\t\tconst response = await uni.request({\n\t\t\t\turl: `${API_CONFIG.baseURL}${url}`,\n\t\t\t\tmethod,\n\t\t\t\tdata: requestData,\n\t\t\t\theader: requestHeader,\n\t\t\t\ttimeout: API_CONFIG.timeout\n\t\t\t})\n\t\t\t\n\t\t\t// 5. 处理响应\n\t\t\tif (response.statusCode === 200) {\n\t\t\t\tlet responseData = response.data\n\t\t\t\t\n\t\t\t\t// 6. 如果响应是加密的，进行解密\n\t\t\t\tif (encrypt && responseData.encryptedData && aesKey) {\n\t\t\t\t\tresponseData = crypto.decryptResponse(responseData.encryptedData, aesKey)\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\treturn {\n\t\t\t\t\tsuccess: true,\n\t\t\t\t\tdata: responseData,\n\t\t\t\t\tstatusCode: response.statusCode\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tthrow new Error(`请求失败: ${response.statusCode}`)\n\t\t\t}\n\t\t\t\n\t\t} catch (error) {\n\t\t\tconsole.error('请求失败:', error)\n\t\t\t\n\t\t\t// 网络错误处理\n\t\t\tif (error.errMsg && error.errMsg.includes('request:fail')) {\n\t\t\t\tthrow new Error('网络连接失败，请检查网络设置')\n\t\t\t}\n\t\t\t\n\t\t\tthrow error\n\t\t}\n\t}\n\t\n\t/**\n\t * GET请求\n\t * @param {string} url - 请求URL\n\t * @param {Object} params - 查询参数\n\t * @param {Object} options - 其他选项\n\t * @returns {Promise<Object>} 响应数据\n\t */\n\tstatic async get(url, params = {}, options = {}) {\n\t\t// GET请求将参数拼接到URL\n\t\tconst queryString = Object.keys(params)\n\t\t\t.map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)\n\t\t\t.join('&')\n\t\t\n\t\tconst fullUrl = queryString ? `${url}?${queryString}` : url\n\t\t\n\t\treturn this.request({\n\t\t\turl: fullUrl,\n\t\t\tmethod: 'GET',\n\t\t\tencrypt: false, // GET请求通常不加密\n\t\t\t...options\n\t\t})\n\t}\n\t\n\t/**\n\t * POST请求\n\t * @param {string} url - 请求URL\n\t * @param {Object} data - 请求数据\n\t * @param {Object} options - 其他选项\n\t * @returns {Promise<Object>} 响应数据\n\t */\n\tstatic async post(url, data = {}, options = {}) {\n\t\treturn this.request({\n\t\t\turl,\n\t\t\tmethod: 'POST',\n\t\t\tdata,\n\t\t\tencrypt: true, // POST请求默认加密\n\t\t\t...options\n\t\t})\n\t}\n\t\n\t/**\n\t * PUT请求\n\t * @param {string} url - 请求URL\n\t * @param {Object} data - 请求数据\n\t * @param {Object} options - 其他选项\n\t * @returns {Promise<Object>} 响应数据\n\t */\n\tstatic async put(url, data = {}, options = {}) {\n\t\treturn this.request({\n\t\t\turl,\n\t\t\tmethod: 'PUT',\n\t\t\tdata,\n\t\t\tencrypt: true,\n\t\t\t...options\n\t\t})\n\t}\n\t\n\t/**\n\t * DELETE请求\n\t * @param {string} url - 请求URL\n\t * @param {Object} options - 其他选项\n\t * @returns {Promise<Object>} 响应数据\n\t */\n\tstatic async delete(url, options = {}) {\n\t\treturn this.request({\n\t\t\turl,\n\t\t\tmethod: 'DELETE',\n\t\t\tencrypt: false,\n\t\t\t...options\n\t\t})\n\t}\n}\n\n/**\n * 简化的请求方法\n */\nexport const http = {\n\tget: (url, params, options) => EncryptedRequest.get(url, params, options),\n\tpost: (url, data, options) => EncryptedRequest.post(url, data, options),\n\tput: (url, data, options) => EncryptedRequest.put(url, data, options),\n\tdelete: (url, options) => EncryptedRequest.delete(url, options)\n}\n\n/**\n * 请求拦截器 - 自动处理登录状态\n */\nexport const createAuthenticatedRequest = (authComposable) => {\n\treturn {\n\t\tasync get(url, params, options = {}) {\n\t\t\tif (options.needAuth !== false) {\n\t\t\t\tawait authComposable.ensureLogin()\n\t\t\t}\n\t\t\treturn http.get(url, params, options)\n\t\t},\n\t\t\n\t\tasync post(url, data, options = {}) {\n\t\t\tif (options.needAuth !== false) {\n\t\t\t\tawait authComposable.ensureLogin()\n\t\t\t}\n\t\t\treturn http.post(url, data, options)\n\t\t},\n\t\t\n\t\tasync put(url, data, options = {}) {\n\t\t\tif (options.needAuth !== false) {\n\t\t\t\tawait authComposable.ensureLogin()\n\t\t\t}\n\t\t\treturn http.put(url, data, options)\n\t\t},\n\t\t\n\t\tasync delete(url, options = {}) {\n\t\t\tif (options.needAuth !== false) {\n\t\t\t\tawait authComposable.ensureLogin()\n\t\t\t}\n\t\t\treturn http.delete(url, options)\n\t\t}\n\t}\n}\n"], "names": ["crypto", "uni"], "mappings": ";;;AAMA,MAAM,aAAa;AAAA,EAClB,SAAS;AAAA;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,IACP,gBAAgB;AAAA,EAChB;AACF;AAKO,MAAM,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7B,aAAa,QAAQ,SAAS;AAC7B,UAAM;AAAA,MACL;AAAA,MACA,SAAS;AAAA,MACT,OAAO,CAAE;AAAA,MACT,SAAS,CAAE;AAAA,MACX,WAAW;AAAA,MACX,UAAU;AAAA,IACb,IAAM;AAEJ,QAAI;AAEH,UAAI,cAAc;AAClB,UAAI,SAAS;AAGb,UAAI,YAAY,WAAW,UAAU,WAAW,QAAQ;AACvD,cAAM,mBAAmBA,aAAAA,OAAO,eAAe,IAAI;AACnD,sBAAc;AAEd,iBAASA,aAAM,OAAC,eAAgB;AAAA,MAChC;AAGD,YAAM,gBAAgB,EAAE,GAAG,WAAW,QAAQ,GAAG,OAAQ;AACzD,UAAI,UAAU;AACb,cAAM,SAASC,cAAAA,MAAI,eAAe,aAAa;AAC/C,YAAI,QAAQ;AACX,wBAAc,eAAe,IAAI,UAAU,MAAM;AAAA,QACjD;AAAA,MACD;AAGDA,0BAAA,MAAA,OAAA,0BAAY,KAAK,UAAU,OAAO,IAAI,OAAO;AAAA,QAC5C,KAAK,GAAG,WAAW,OAAO,GAAG,GAAG;AAAA,QAChC;AAAA,QACA;AAAA,MACJ,CAAI;AAED,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QAClC,KAAK,GAAG,WAAW,OAAO,GAAG,GAAG;AAAA,QAChC;AAAA,QACA,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,SAAS,WAAW;AAAA,MACxB,CAAI;AAGD,UAAI,SAAS,eAAe,KAAK;AAChC,YAAI,eAAe,SAAS;AAG5B,YAAI,WAAW,aAAa,iBAAiB,QAAQ;AACpD,yBAAeD,aAAAA,OAAO,gBAAgB,aAAa,eAAe,MAAM;AAAA,QACxE;AAED,eAAO;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,UACN,YAAY,SAAS;AAAA,QACrB;AAAA,MACL,OAAU;AACN,cAAM,IAAI,MAAM,SAAS,SAAS,UAAU,EAAE;AAAA,MAC9C;AAAA,IAED,SAAQ,OAAO;AACfC,oBAAAA,MAAA,MAAA,SAAA,0BAAc,SAAS,KAAK;AAG5B,UAAI,MAAM,UAAU,MAAM,OAAO,SAAS,cAAc,GAAG;AAC1D,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAChC;AAED,YAAM;AAAA,IACN;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,aAAa,IAAI,KAAK,SAAS,CAAA,GAAI,UAAU,CAAA,GAAI;AAEhD,UAAM,cAAc,OAAO,KAAK,MAAM,EACpC,IAAI,SAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EAC1E,KAAK,GAAG;AAEV,UAAM,UAAU,cAAc,GAAG,GAAG,IAAI,WAAW,KAAK;AAExD,WAAO,KAAK,QAAQ;AAAA,MACnB,KAAK;AAAA,MACL,QAAQ;AAAA,MACR,SAAS;AAAA;AAAA,MACT,GAAG;AAAA,IACN,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,aAAa,KAAK,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,GAAI;AAC/C,WAAO,KAAK,QAAQ;AAAA,MACnB;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA,SAAS;AAAA;AAAA,MACT,GAAG;AAAA,IACN,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,aAAa,IAAI,KAAK,OAAO,CAAA,GAAI,UAAU,CAAA,GAAI;AAC9C,WAAO,KAAK,QAAQ;AAAA,MACnB;AAAA,MACA,QAAQ;AAAA,MACR;AAAA,MACA,SAAS;AAAA,MACT,GAAG;AAAA,IACN,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,aAAa,OAAO,KAAK,UAAU,IAAI;AACtC,WAAO,KAAK,QAAQ;AAAA,MACnB;AAAA,MACA,QAAQ;AAAA,MACR,SAAS;AAAA,MACT,GAAG;AAAA,IACN,CAAG;AAAA,EACD;AACF;AAKY,MAAC,OAAO;AAAA,EACnB,KAAK,CAAC,KAAK,QAAQ,YAAY,iBAAiB,IAAI,KAAK,QAAQ,OAAO;AAAA,EACxE,MAAM,CAAC,KAAK,MAAM,YAAY,iBAAiB,KAAK,KAAK,MAAM,OAAO;AAAA,EACtE,KAAK,CAAC,KAAK,MAAM,YAAY,iBAAiB,IAAI,KAAK,MAAM,OAAO;AAAA,EACpE,QAAQ,CAAC,KAAK,YAAY,iBAAiB,OAAO,KAAK,OAAO;AAC/D;;"}