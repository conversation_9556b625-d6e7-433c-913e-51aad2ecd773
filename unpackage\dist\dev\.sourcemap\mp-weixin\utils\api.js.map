{"version": 3, "file": "api.js", "sources": ["utils/api.js"], "sourcesContent": ["// 银行代码查询API工具类\nimport { detectSearchType, getBankTypeByCode, SEARCH_TYPES } from '@/types/bank.js'\nimport { crypto } from '@/utils/crypto.js'\nimport { getApiUrl, ResponseHandler, ApiUtils, RETRY_CONFIG } from '@/config/api.js'\n\n/**\n * 银行代码查询API\n */\nexport class BankCodeAPI {\n\n\t/**\n\t * 搜索银行代码\n\t * @param {string} keyword - 搜索关键字（银行名称、地区、联行号等）\n\t * @returns {Promise<Array>} 查询结果数组\n\t */\n\tstatic async searchBankCode(keyword) {\n\t\t// 验证搜索参数\n\t\tif (!ApiUtils.validateSearchParams({ keyword })) {\n\t\t\tthrow new Error('搜索关键字不能为空')\n\t\t}\n\n\t\t// 检测搜索类型\n\t\tconst searchType = detectSearchType(keyword)\n\t\tconsole.log(`搜索类型: ${searchType}, 关键字: ${keyword}`)\n\n\t\t// 构建标准化的请求参数\n\t\tconst requestData = {\n\t\t\topenId: uni.getStorageSync('user_openid'),\n\t\t\tquery: keyword\n\t\t}\n\t\t// console.log(requestData)\n\n\t\t// 尝试请求API，失败时使用模拟数据\n\t\treturn await this.requestWithFallback(requestData, keyword, searchType)\n\t}\n\n\t/**\n\t * 带降级的API请求\n\t * @param {Object} requestData - 请求参数\n\t * @param {string} keyword - 搜索关键字\n\t * @param {string} searchType - 搜索类型\n\t * @returns {Promise<Array>} 查询结果\n\t */\n\tstatic async requestWithFallback(requestData, keyword, searchType) {\n\t\tlet lastError = null\n\n\t\t// 重试机制\n\t\tfor (let attempt = 0; attempt < 3; attempt++) {\n\t\t\ttry {\n\t\t\t\tconsole.log(`银行查询尝试 ${attempt + 1}/${RETRY_CONFIG.getRetryDelay.length}`)\n\n\t\t\t\t// 使用混合加密\n\t\t\t\tconst encryptedRequest = crypto.encryptRequest(requestData)\n\t\t\t\tconsole.log('银行查询请求已加密')\n\n\t\t\t\t// 调用实际的后台接口\n\t\t\t\tconst response = await uni.request({\n\t\t\t\t\turl: getApiUrl('BANK_SEARCH'),\n\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tparameter: encryptedRequest.encryptedData\n\t\t\t\t\t},\n\t\t\t\t\theader: {\n\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t},\n\t\t\t\t\ttimeout: 10000\n\t\t\t\t})\n\n\t\t\t\tconsole.log('银行查询响应:', response)\n\n\t\t\t\t// 使用响应处理器处理结果\n\t\t\t\tconst results = ResponseHandler.handleBankSearchResponse(response)\n\n\t\t\t\t// 解密响应数据\n\t\t\t\tif (response.statusCode == 200) {\n\t\t\t\t\tif (response.data.code == 200) {\n\t\t\t\t\t\tconst decryptedData = crypto.decryptResponse(response.data.data, encryptedRequest.aesKey)\n\t\t\t\t\t\tconsole.log('银行查询解密成功:', decryptedData)\n\n\t\t\t\t\t\t// 格式化银行信息\n\t\t\t\t\t\tconst formattedResults = Array.isArray(decryptedData.bankInfoList)\n\t\t\t\t\t\t\t? decryptedData.bankInfoList.map(item => ApiUtils.formatBankInfo(item))\n\t\t\t\t\t\t\t: [ApiUtils.formatBankInfo(decryptedData.bankInfoList)]\n\t\t\t\t\t\tconsole.log('银行查询结果已格式化:', formattedResults)\n\t\t\t\t\t\treturn formattedResults.filter(item => item.code) // 过滤无效数据\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: response.data.msg,\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t})\n\t\t\t\t\t\treturn []\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t}\n\n\t\t\t\treturn results\n\n\t\t\t} catch (error) {\n\t\t\t\tlastError = error\n\t\t\t\tconsole.error(`银行查询尝试 ${attempt + 1} 失败:`, error)\n\n\t\t\t\t// 检查是否应该重试\n\t\t\t\tif (!RETRY_CONFIG.shouldRetry(error, attempt)) {\n\t\t\t\t\tbreak\n\t\t\t\t}\n\n\t\t\t\t// 等待重试延迟\n\t\t\t\tif (attempt < 2) {\n\t\t\t\t\tconst delay = RETRY_CONFIG.getRetryDelay(attempt)\n\t\t\t\t\tconsole.log(`等待 ${delay}ms 后重试...`)\n\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, delay))\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// 所有重试都失败，使用模拟数据\n\t\tconsole.log('API请求失败，使用模拟数据:', lastError?.message)\n\t\treturn this.getMockData(keyword, searchType)\n\t}\n\t\n\t/**\n\t * 获取模拟数据（用于演示）\n\t * @param {string} keyword - 搜索关键字\n\t * @param {string} searchType - 搜索类型\n\t * @returns {Array} 模拟的查询结果\n\t */\n\tstatic getMockData(keyword, searchType = 'keyword') {\n\t\tconst allMockData = [\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国工商银行股份有限公司北京国家文化与金融合作示范区支行',\n\t\t\t\tbranchName: '营业部：中国工商银行股份有限公司北京王府井支行',\n\t\t\t\tdistance: '100',\n\t\t\t\taddress: '北京市东城区王府井大街',\n\t\t\t\tphone: '010-********'\n\t\t\t},\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国工商银行股份有限公司北京分行',\n\t\t\t\tbranchName: '营业部：中国工商银行股份有限公司北京分行营业部',\n\t\t\t\tdistance: '200',\n\t\t\t\taddress: '北京市西城区复兴门内大街',\n\t\t\t\tphone: '010-********'\n\t\t\t},\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国农业银行股份有限公司北京分行',\n\t\t\t\tbranchName: '营业部：中国农业银行股份有限公司北京分行营业部',\n\t\t\t\tdistance: '150',\n\t\t\t\taddress: '北京市东城区建国门内大街',\n\t\t\t\tphone: '010-********'\n\t\t\t},\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国银行股份有限公司北京分行',\n\t\t\t\tbranchName: '营业部：中国银行股份有限公司北京分行营业部',\n\t\t\t\tdistance: '180',\n\t\t\t\taddress: '北京市西城区复兴门外大街',\n\t\t\t\tphone: '010-********'\n\t\t\t},\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国建设银行股份有限公司北京分行',\n\t\t\t\tbranchName: '营业部：中国建设银行股份有限公司北京分行营业部',\n\t\t\t\tdistance: '120',\n\t\t\t\taddress: '北京市西城区金融大街',\n\t\t\t\tphone: '010-********'\n\t\t\t}\n\t\t];\n\t\t\n\t\t// 根据关键字和搜索类型过滤数据\n\t\tconst keyword_lower = keyword.toLowerCase();\n\n\t\treturn allMockData.filter(item => {\n\t\t\t// 精确联行号匹配\n\t\t\tif (searchType === SEARCH_TYPES.BANK_CODE) {\n\t\t\t\treturn item.code === keyword || item.code.includes(keyword);\n\t\t\t}\n\n\t\t\t// 银行名称匹配\n\t\t\tif (searchType === SEARCH_TYPES.BANK_NAME) {\n\t\t\t\treturn item.bankName.includes(keyword) ||\n\t\t\t\t\t   item.branchName.includes(keyword) ||\n\t\t\t\t\t   (keyword_lower.includes('工行') && item.bankName.includes('工商银行')) ||\n\t\t\t\t\t   (keyword_lower.includes('农行') && item.bankName.includes('农业银行')) ||\n\t\t\t\t\t   (keyword_lower.includes('中行') && item.bankName.includes('中国银行')) ||\n\t\t\t\t\t   (keyword_lower.includes('建行') && item.bankName.includes('建设银行'));\n\t\t\t}\n\n\t\t\t// 地区匹配\n\t\t\tif (searchType === SEARCH_TYPES.REGION) {\n\t\t\t\treturn item.address && item.address.includes(keyword);\n\t\t\t}\n\n\t\t\t// 通用关键字匹配\n\t\t\treturn item.bankName.includes(keyword) ||\n\t\t\t\t   item.branchName.includes(keyword) ||\n\t\t\t\t   item.code.includes(keyword) ||\n\t\t\t\t   (item.address && item.address.includes(keyword)) ||\n\t\t\t\t   (keyword_lower.includes('工行') && item.bankName.includes('工商银行')) ||\n\t\t\t\t   (keyword_lower.includes('农行') && item.bankName.includes('农业银行')) ||\n\t\t\t\t   (keyword_lower.includes('中行') && item.bankName.includes('中国银行')) ||\n\t\t\t\t   (keyword_lower.includes('建行') && item.bankName.includes('建设银行')) ||\n\t\t\t\t   (keyword_lower.includes('北京') && item.address && item.address.includes('北京'));\n\t\t});\n\t}\n\t\n\t/**\n\t * 根据联行号获取详细信息\n\t * @param {string} bankCode - 联行号\n\t * @returns {Promise<Object>} 银行详细信息\n\t */\n\tstatic async getBankDetail(bankCode) {\n\t\ttry {\n\t\t\t// 验证联行号格式\n\t\t\tif (!bankCode || typeof bankCode !== 'string' || bankCode.length !== 12) {\n\t\t\t\tthrow new Error('联行号格式错误')\n\t\t\t}\n\n\t\t\t// 准备请求参数\n\t\t\tconst requestData = {\n\t\t\t\tbankCode: bankCode,\n\t\t\t\ttimestamp: Date.now()\n\t\t\t}\n\n\t\t\t// 使用混合加密\n\t\t\tconst encryptedRequest = crypto.encryptRequest(requestData)\n\t\t\tconsole.log('银行详情查询请求已加密')\n\n\t\t\t// 调用实际的后台接口\n\t\t\tconst response = await uni.request({\n\t\t\t\turl: getApiUrl('BANK_DETAIL'),\n\t\t\t\tmethod: 'POST',\n\t\t\t\tdata: {\n\t\t\t\t\tparameter: encryptedRequest.encryptedData\n\t\t\t\t},\n\t\t\t\theader: {\n\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t},\n\t\t\t\ttimeout: 10000\n\t\t\t})\n\n\t\t\tconsole.log('银行详情查询响应:', response)\n\n\t\t\t// 处理响应\n\t\t\tif (response.statusCode === 200 && response.data.code == 200) {\n\t\t\t\t// 解密响应数据\n\t\t\t\tconst decryptedData = crypto.decryptResponse(response.data.data, encryptedRequest.aesKey)\n\t\t\t\tconsole.log('银行详情解密成功:', decryptedData)\n\n\t\t\t\t// 格式化银行信息\n\t\t\t\treturn ApiUtils.formatBankInfo(decryptedData)\n\t\t\t}\n\n\t\t\t// API请求失败，使用模拟数据\n\t\t\tconsole.log('API请求失败，使用模拟数据')\n\t\t\tconst mockData = this.getMockData(bankCode, SEARCH_TYPES.BANK_CODE)\n\t\t\treturn mockData.length > 0 ? mockData[0] : null\n\n\t\t} catch (error) {\n\t\t\tconsole.error('获取银行详情失败:', error)\n\n\t\t\t// 网络错误时使用模拟数据\n\t\t\tif (error.errMsg && error.errMsg.includes('request:fail')) {\n\t\t\t\tconsole.log('网络请求失败，使用模拟数据')\n\t\t\t\tconst mockData = this.getMockData(bankCode, SEARCH_TYPES.BANK_CODE)\n\t\t\t\treturn mockData.length > 0 ? mockData[0] : null\n\t\t\t}\n\n\t\t\tthrow new Error('获取详情失败: ' + error.message)\n\t\t}\n\t}\n}\n\n/**\n * 工具函数：格式化银行代码显示\n * @param {string} code - 银行代码\n * @returns {string} 格式化后的代码\n */\nexport function formatBankCode(code) {\n\tif (!code) return '';\n\t// 将12位联行号格式化为 XXX-XXX-XXX-XXX 的形式\n\treturn code.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{3})/, '$1-$2-$3-$4');\n}\n\n/**\n * 工具函数：验证联行号格式\n * @param {string} code - 银行代码\n * @returns {boolean} 是否为有效格式\n */\nexport function validateBankCode(code) {\n\tif (!code) return false;\n\t// 联行号通常为12位数字\n\tconst pattern = /^\\d{12}$/;\n\treturn pattern.test(code.replace(/[-\\s]/g, ''));\n}\n"], "names": ["ApiUtils", "detectSearchType", "uni", "RETRY_CONFIG", "crypto", "getApiUrl", "ResponseHandler", "SEARCH_TYPES"], "mappings": ";;;;;AAQO,MAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,aAAa,eAAe,SAAS;AAEpC,QAAI,CAACA,WAAAA,SAAS,qBAAqB,EAAE,QAAS,CAAA,GAAG;AAChD,YAAM,IAAI,MAAM,WAAW;AAAA,IAC3B;AAGD,UAAM,aAAaC,WAAgB,iBAAC,OAAO;AAC3CC,wBAAY,MAAA,OAAA,sBAAA,SAAS,UAAU,UAAU,OAAO,EAAE;AAGlD,UAAM,cAAc;AAAA,MACnB,QAAQA,cAAAA,MAAI,eAAe,aAAa;AAAA,MACxC,OAAO;AAAA,IACP;AAID,WAAO,MAAM,KAAK,oBAAoB,aAAa,SAAS,UAAU;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASD,aAAa,oBAAoB,aAAa,SAAS,YAAY;AAClE,QAAI,YAAY;AAGhB,aAAS,UAAU,GAAG,UAAU,GAAG,WAAW;AAC7C,UAAI;AACHA,sBAAAA,MAAA,MAAA,OAAA,sBAAY,UAAU,UAAU,CAAC,IAAIC,WAAY,aAAC,cAAc,MAAM,EAAE;AAGxE,cAAM,mBAAmBC,aAAAA,OAAO,eAAe,WAAW;AAC1DF,sBAAAA,MAAA,MAAA,OAAA,sBAAY,WAAW;AAGvB,cAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,UAClC,KAAKG,WAAS,UAAC,aAAa;AAAA,UAC5B,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,WAAW,iBAAiB;AAAA,UAC5B;AAAA,UACD,QAAQ;AAAA,YACP,gBAAgB;AAAA,UAChB;AAAA,UACD,SAAS;AAAA,QACd,CAAK;AAEDH,sBAAAA,MAAA,MAAA,OAAA,sBAAY,WAAW,QAAQ;AAG/B,cAAM,UAAUI,WAAAA,gBAAgB,yBAAyB,QAAQ;AAGjE,YAAI,SAAS,cAAc,KAAK;AAC/B,cAAI,SAAS,KAAK,QAAQ,KAAK;AAC9B,kBAAM,gBAAgBF,aAAM,OAAC,gBAAgB,SAAS,KAAK,MAAM,iBAAiB,MAAM;AACxFF,0BAAAA,MAAY,MAAA,OAAA,sBAAA,aAAa,aAAa;AAGtC,kBAAM,mBAAmB,MAAM,QAAQ,cAAc,YAAY,IAC9D,cAAc,aAAa,IAAI,UAAQF,WAAAA,SAAS,eAAe,IAAI,CAAC,IACpE,CAACA,oBAAS,eAAe,cAAc,YAAY,CAAC;AACvDE,0BAAAA,yCAAY,eAAe,gBAAgB;AAC3C,mBAAO,iBAAiB,OAAO,UAAQ,KAAK,IAAI;AAAA,UACtD,OAAY;AACNA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,SAAS,KAAK;AAAA,cACrB,MAAM;AAAA,cACN,UAAU;AAAA,YACjB,CAAO;AACD,mBAAO,CAAE;AAAA,UACT;AAAA,QAED;AAED,eAAO;AAAA,MAEP,SAAQ,OAAO;AACf,oBAAY;AACZA,4BAAc,MAAA,SAAA,uBAAA,UAAU,UAAU,CAAC,QAAQ,KAAK;AAGhD,YAAI,CAACC,WAAAA,aAAa,YAAY,OAAO,OAAO,GAAG;AAC9C;AAAA,QACA;AAGD,YAAI,UAAU,GAAG;AAChB,gBAAM,QAAQA,WAAAA,aAAa,cAAc,OAAO;AAChDD,wBAAA,MAAA,MAAA,OAAA,uBAAY,MAAM,KAAK,WAAW;AAClC,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,KAAK,CAAC;AAAA,QACvD;AAAA,MACD;AAAA,IACD;AAGDA,kBAAY,MAAA,MAAA,OAAA,uBAAA,mBAAmB,uCAAW,OAAO;AACjD,WAAO,KAAK,YAAY,SAAS,UAAU;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,YAAY,SAAS,aAAa,WAAW;AACnD,UAAM,cAAc;AAAA,MACnB;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,IACJ;AAGE,UAAM,gBAAgB,QAAQ;AAE9B,WAAO,YAAY,OAAO,UAAQ;AAEjC,UAAI,eAAeK,WAAY,aAAC,WAAW;AAC1C,eAAO,KAAK,SAAS,WAAW,KAAK,KAAK,SAAS,OAAO;AAAA,MAC1D;AAGD,UAAI,eAAeA,WAAY,aAAC,WAAW;AAC1C,eAAO,KAAK,SAAS,SAAS,OAAO,KACjC,KAAK,WAAW,SAAS,OAAO,KAC/B,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM;AAAA,MAClE;AAGD,UAAI,eAAeA,WAAY,aAAC,QAAQ;AACvC,eAAO,KAAK,WAAW,KAAK,QAAQ,SAAS,OAAO;AAAA,MACpD;AAGD,aAAO,KAAK,SAAS,SAAS,OAAO,KACjC,KAAK,WAAW,SAAS,OAAO,KAChC,KAAK,KAAK,SAAS,OAAO,KACzB,KAAK,WAAW,KAAK,QAAQ,SAAS,OAAO,KAC7C,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,WAAW,KAAK,QAAQ,SAAS,IAAI;AAAA,IAClF,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,aAAa,cAAc,UAAU;AACpC,QAAI;AAEH,UAAI,CAAC,YAAY,OAAO,aAAa,YAAY,SAAS,WAAW,IAAI;AACxE,cAAM,IAAI,MAAM,SAAS;AAAA,MACzB;AAGD,YAAM,cAAc;AAAA,QACnB;AAAA,QACA,WAAW,KAAK,IAAK;AAAA,MACrB;AAGD,YAAM,mBAAmBH,aAAAA,OAAO,eAAe,WAAW;AAC1DF,oBAAAA,MAAA,MAAA,OAAA,uBAAY,aAAa;AAGzB,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QAClC,KAAKG,WAAS,UAAC,aAAa;AAAA,QAC5B,QAAQ;AAAA,QACR,MAAM;AAAA,UACL,WAAW,iBAAiB;AAAA,QAC5B;AAAA,QACD,QAAQ;AAAA,UACP,gBAAgB;AAAA,QAChB;AAAA,QACD,SAAS;AAAA,MACb,CAAI;AAEDH,oBAAAA,MAAA,MAAA,OAAA,uBAAY,aAAa,QAAQ;AAGjC,UAAI,SAAS,eAAe,OAAO,SAAS,KAAK,QAAQ,KAAK;AAE7D,cAAM,gBAAgBE,aAAM,OAAC,gBAAgB,SAAS,KAAK,MAAM,iBAAiB,MAAM;AACxFF,sBAAAA,MAAA,MAAA,OAAA,uBAAY,aAAa,aAAa;AAGtC,eAAOF,WAAQ,SAAC,eAAe,aAAa;AAAA,MAC5C;AAGDE,oBAAAA,MAAA,MAAA,OAAA,uBAAY,gBAAgB;AAC5B,YAAM,WAAW,KAAK,YAAY,UAAUK,WAAAA,aAAa,SAAS;AAClE,aAAO,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAAA,IAE3C,SAAQ,OAAO;AACfL,oBAAAA,MAAc,MAAA,SAAA,uBAAA,aAAa,KAAK;AAGhC,UAAI,MAAM,UAAU,MAAM,OAAO,SAAS,cAAc,GAAG;AAC1DA,sBAAAA,0CAAY,eAAe;AAC3B,cAAM,WAAW,KAAK,YAAY,UAAUK,WAAAA,aAAa,SAAS;AAClE,eAAO,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAAA,MAC3C;AAED,YAAM,IAAI,MAAM,aAAa,MAAM,OAAO;AAAA,IAC1C;AAAA,EACD;AACF;;"}