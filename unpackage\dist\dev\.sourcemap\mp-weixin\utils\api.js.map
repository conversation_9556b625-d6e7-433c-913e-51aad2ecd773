{"version": 3, "file": "api.js", "sources": ["utils/api.js"], "sourcesContent": ["// 银行代码查询API工具类\nimport { detectSearchType, getBankTypeByCode, SEARCH_TYPES } from '@/types/bank.js'\n\nconst API_BASE_URL = 'https://api.chahanghao.com'; // 替换为实际的API地址\n\n/**\n * 银行代码查询API\n */\nexport class BankCodeAPI {\n\n\t/**\n\t * 搜索银行代码\n\t * @param {string} keyword - 搜索关键字（银行名称、地区、联行号等）\n\t * @returns {Promise<Array>} 查询结果数组\n\t */\n\tstatic async searchBankCode(keyword) {\n\t\ttry {\n\t\t\t// 检测搜索类型\n\t\t\tconst searchType = detectSearchType(keyword)\n\t\t\tconsole.log(`搜索类型: ${searchType}, 关键字: ${keyword}`)\n\n\t\t\t// 实际项目中应该调用真实的API接口\n\t\t\t// const response = await uni.request({\n\t\t\t// \turl: `${API_BASE_URL}/search`,\n\t\t\t// \tmethod: 'GET',\n\t\t\t// \tdata: {\n\t\t\t// \t\tkeyword: keyword,\n\t\t\t// \t\ttype: searchType,\n\t\t\t// \t\tlimit: 20\n\t\t\t// \t}\n\t\t\t// });\n\t\t\t//\n\t\t\t// if (response.statusCode === 200 && response.data.success) {\n\t\t\t// \treturn response.data.data || [];\n\t\t\t// }\n\n\t\t\t// 暂时使用模拟数据进行演示\n\t\t\treturn new Promise((resolve) => {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tconst mockData = this.getMockData(keyword, searchType);\n\t\t\t\t\tresolve(mockData);\n\t\t\t\t}, 800); // 模拟网络延迟\n\t\t\t});\n\n\t\t} catch (error) {\n\t\t\tconsole.error('银行代码查询失败:', error);\n\t\t\tthrow new Error('查询失败，请检查网络连接');\n\t\t}\n\t}\n\t\n\t/**\n\t * 获取模拟数据（用于演示）\n\t * @param {string} keyword - 搜索关键字\n\t * @param {string} searchType - 搜索类型\n\t * @returns {Array} 模拟的查询结果\n\t */\n\tstatic getMockData(keyword, searchType = 'keyword') {\n\t\tconst allMockData = [\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国工商银行股份有限公司北京国家文化与金融合作示范区支行',\n\t\t\t\tbranchName: '营业部：中国工商银行股份有限公司北京王府井支行',\n\t\t\t\tdistance: '100',\n\t\t\t\taddress: '北京市东城区王府井大街',\n\t\t\t\tphone: '010-********'\n\t\t\t},\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国工商银行股份有限公司北京分行',\n\t\t\t\tbranchName: '营业部：中国工商银行股份有限公司北京分行营业部',\n\t\t\t\tdistance: '200',\n\t\t\t\taddress: '北京市西城区复兴门内大街',\n\t\t\t\tphone: '010-********'\n\t\t\t},\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国农业银行股份有限公司北京分行',\n\t\t\t\tbranchName: '营业部：中国农业银行股份有限公司北京分行营业部',\n\t\t\t\tdistance: '150',\n\t\t\t\taddress: '北京市东城区建国门内大街',\n\t\t\t\tphone: '010-********'\n\t\t\t},\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国银行股份有限公司北京分行',\n\t\t\t\tbranchName: '营业部：中国银行股份有限公司北京分行营业部',\n\t\t\t\tdistance: '180',\n\t\t\t\taddress: '北京市西城区复兴门外大街',\n\t\t\t\tphone: '010-********'\n\t\t\t},\n\t\t\t{\n\t\t\t\tcode: '************',\n\t\t\t\tbankName: '中国建设银行股份有限公司北京分行',\n\t\t\t\tbranchName: '营业部：中国建设银行股份有限公司北京分行营业部',\n\t\t\t\tdistance: '120',\n\t\t\t\taddress: '北京市西城区金融大街',\n\t\t\t\tphone: '010-********'\n\t\t\t}\n\t\t];\n\t\t\n\t\t// 根据关键字和搜索类型过滤数据\n\t\tconst keyword_lower = keyword.toLowerCase();\n\n\t\treturn allMockData.filter(item => {\n\t\t\t// 精确联行号匹配\n\t\t\tif (searchType === SEARCH_TYPES.BANK_CODE) {\n\t\t\t\treturn item.code === keyword || item.code.includes(keyword);\n\t\t\t}\n\n\t\t\t// 银行名称匹配\n\t\t\tif (searchType === SEARCH_TYPES.BANK_NAME) {\n\t\t\t\treturn item.bankName.includes(keyword) ||\n\t\t\t\t\t   item.branchName.includes(keyword) ||\n\t\t\t\t\t   (keyword_lower.includes('工行') && item.bankName.includes('工商银行')) ||\n\t\t\t\t\t   (keyword_lower.includes('农行') && item.bankName.includes('农业银行')) ||\n\t\t\t\t\t   (keyword_lower.includes('中行') && item.bankName.includes('中国银行')) ||\n\t\t\t\t\t   (keyword_lower.includes('建行') && item.bankName.includes('建设银行'));\n\t\t\t}\n\n\t\t\t// 地区匹配\n\t\t\tif (searchType === SEARCH_TYPES.REGION) {\n\t\t\t\treturn item.address && item.address.includes(keyword);\n\t\t\t}\n\n\t\t\t// 通用关键字匹配\n\t\t\treturn item.bankName.includes(keyword) ||\n\t\t\t\t   item.branchName.includes(keyword) ||\n\t\t\t\t   item.code.includes(keyword) ||\n\t\t\t\t   (item.address && item.address.includes(keyword)) ||\n\t\t\t\t   (keyword_lower.includes('工行') && item.bankName.includes('工商银行')) ||\n\t\t\t\t   (keyword_lower.includes('农行') && item.bankName.includes('农业银行')) ||\n\t\t\t\t   (keyword_lower.includes('中行') && item.bankName.includes('中国银行')) ||\n\t\t\t\t   (keyword_lower.includes('建行') && item.bankName.includes('建设银行')) ||\n\t\t\t\t   (keyword_lower.includes('北京') && item.address && item.address.includes('北京'));\n\t\t});\n\t}\n\t\n\t/**\n\t * 根据联行号获取详细信息\n\t * @param {string} bankCode - 联行号\n\t * @returns {Promise<Object>} 银行详细信息\n\t */\n\tstatic async getBankDetail(bankCode) {\n\t\ttry {\n\t\t\t// 实际项目中调用真实API\n\t\t\t// const response = await uni.request({\n\t\t\t// \turl: `${API_BASE_URL}/detail/${bankCode}`,\n\t\t\t// \tmethod: 'GET'\n\t\t\t// });\n\t\t\t// \n\t\t\t// if (response.statusCode === 200 && response.data.success) {\n\t\t\t// \treturn response.data.data;\n\t\t\t// }\n\t\t\t\n\t\t\t// 模拟数据\n\t\t\tconst mockData = this.getMockData(bankCode, SEARCH_TYPES.BANK_CODE);\n\t\t\treturn mockData.length > 0 ? mockData[0] : null;\n\t\t\t\n\t\t} catch (error) {\n\t\t\tconsole.error('获取银行详情失败:', error);\n\t\t\tthrow new Error('获取详情失败');\n\t\t}\n\t}\n}\n\n/**\n * 工具函数：格式化银行代码显示\n * @param {string} code - 银行代码\n * @returns {string} 格式化后的代码\n */\nexport function formatBankCode(code) {\n\tif (!code) return '';\n\t// 将12位联行号格式化为 XXX-XXX-XXX-XXX 的形式\n\treturn code.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{3})/, '$1-$2-$3-$4');\n}\n\n/**\n * 工具函数：验证联行号格式\n * @param {string} code - 银行代码\n * @returns {boolean} 是否为有效格式\n */\nexport function validateBankCode(code) {\n\tif (!code) return false;\n\t// 联行号通常为12位数字\n\tconst pattern = /^\\d{12}$/;\n\treturn pattern.test(code.replace(/[-\\s]/g, ''));\n}\n"], "names": ["detectSearchType", "uni", "SEARCH_TYPES"], "mappings": ";;;AAQO,MAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,aAAa,eAAe,SAAS;AACpC,QAAI;AAEH,YAAM,aAAaA,WAAgB,iBAAC,OAAO;AAC3CC,0BAAA,MAAA,OAAA,sBAAY,SAAS,UAAU,UAAU,OAAO,EAAE;AAkBlD,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC/B,mBAAW,MAAM;AAChB,gBAAM,WAAW,KAAK,YAAY,SAAS,UAAU;AACrD,kBAAQ,QAAQ;AAAA,QAChB,GAAE,GAAG;AAAA,MACV,CAAI;AAAA,IAED,SAAQ,OAAO;AACfA,oBAAc,MAAA,MAAA,SAAA,sBAAA,aAAa,KAAK;AAChC,YAAM,IAAI,MAAM,cAAc;AAAA,IAC9B;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,YAAY,SAAS,aAAa,WAAW;AACnD,UAAM,cAAc;AAAA,MACnB;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,MACD;AAAA,QACC,MAAM;AAAA,QACN,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,SAAS;AAAA,QACT,OAAO;AAAA,MACP;AAAA,IACJ;AAGE,UAAM,gBAAgB,QAAQ;AAE9B,WAAO,YAAY,OAAO,UAAQ;AAEjC,UAAI,eAAeC,WAAY,aAAC,WAAW;AAC1C,eAAO,KAAK,SAAS,WAAW,KAAK,KAAK,SAAS,OAAO;AAAA,MAC1D;AAGD,UAAI,eAAeA,WAAY,aAAC,WAAW;AAC1C,eAAO,KAAK,SAAS,SAAS,OAAO,KACjC,KAAK,WAAW,SAAS,OAAO,KAC/B,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM;AAAA,MAClE;AAGD,UAAI,eAAeA,WAAY,aAAC,QAAQ;AACvC,eAAO,KAAK,WAAW,KAAK,QAAQ,SAAS,OAAO;AAAA,MACpD;AAGD,aAAO,KAAK,SAAS,SAAS,OAAO,KACjC,KAAK,WAAW,SAAS,OAAO,KAChC,KAAK,KAAK,SAAS,OAAO,KACzB,KAAK,WAAW,KAAK,QAAQ,SAAS,OAAO,KAC7C,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,SAAS,SAAS,MAAM,KAC7D,cAAc,SAAS,IAAI,KAAK,KAAK,WAAW,KAAK,QAAQ,SAAS,IAAI;AAAA,IAClF,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,aAAa,cAAc,UAAU;AACpC,QAAI;AAYH,YAAM,WAAW,KAAK,YAAY,UAAUA,WAAAA,aAAa,SAAS;AAClE,aAAO,SAAS,SAAS,IAAI,SAAS,CAAC,IAAI;AAAA,IAE3C,SAAQ,OAAO;AACfD,oBAAc,MAAA,MAAA,SAAA,uBAAA,aAAa,KAAK;AAChC,YAAM,IAAI,MAAM,QAAQ;AAAA,IACxB;AAAA,EACD;AACF;;"}