{"version": 3, "file": "crypto.js", "sources": ["utils/crypto.js"], "sourcesContent": ["// 混合加密工具类\n// 注意：在实际项目中建议使用 crypto-js 库\n// 这里提供简化版本用于演示\n\n/**\n * 混合加密工具类\n * 使用RSA加密AES密钥，AES加密实际数据\n */\nexport class CryptoUtil {\n\t\n\t// RSA公钥 - 实际项目中应该从服务器获取\n\tstatic RSA_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdef...\n-----END PUBLIC KEY-----`\n\t\n\t/**\n\t * 生成随机AES密钥\n\t * @returns {string} 32位随机字符串\n\t */\n\tstatic generateAESKey() {\n\t\tconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n\t\tlet result = ''\n\t\tfor (let i = 0; i < 32; i++) {\n\t\t\tresult += chars.charAt(Math.floor(Math.random() * chars.length))\n\t\t}\n\t\treturn result\n\t}\n\t\n\t/**\n\t * 简化的AES加密（Base64编码模拟）\n\t * @param {string} data - 要加密的数据\n\t * @param {string} key - AES密钥\n\t * @returns {string} 加密后的数据\n\t */\n\tstatic aesEncrypt(data, key) {\n\t\ttry {\n\t\t\t// 简化版本：使用Base64编码模拟AES加密\n\t\t\t// 实际项目中应该使用真正的AES加密\n\t\t\tconst combined = `${key}:${data}`\n\t\t\tconst encoded = btoa(unescape(encodeURIComponent(combined)))\n\t\t\treturn `AES_${encoded}`\n\t\t} catch (error) {\n\t\t\tconsole.error('AES加密失败:', error)\n\t\t\tthrow new Error('数据加密失败')\n\t\t}\n\t}\n\n\t/**\n\t * 简化的AES解密（Base64解码模拟）\n\t * @param {string} encryptedData - 加密的数据\n\t * @param {string} key - AES密钥\n\t * @returns {string} 解密后的数据\n\t */\n\tstatic aesDecrypt(encryptedData, key) {\n\t\ttry {\n\t\t\t// 简化版本：使用Base64解码模拟AES解密\n\t\t\tif (!encryptedData.startsWith('AES_')) {\n\t\t\t\tthrow new Error('无效的加密数据格式')\n\t\t\t}\n\n\t\t\tconst encoded = encryptedData.substring(4)\n\t\t\tconst decoded = decodeURIComponent(escape(atob(encoded)))\n\t\t\tconst [dataKey, ...dataParts] = decoded.split(':')\n\n\t\t\tif (dataKey !== key) {\n\t\t\t\tthrow new Error('密钥不匹配')\n\t\t\t}\n\n\t\t\treturn dataParts.join(':')\n\t\t} catch (error) {\n\t\t\tconsole.error('AES解密失败:', error)\n\t\t\tthrow new Error('数据解密失败')\n\t\t}\n\t}\n\t\n\t/**\n\t * RSA加密（使用JSEncrypt库）\n\t * @param {string} data - 要加密的数据\n\t * @returns {string} 加密后的数据\n\t */\n\tstatic rsaEncrypt(data) {\n\t\ttry {\n\t\t\t// 注意：在实际项目中需要引入JSEncrypt库\n\t\t\t// import JSEncrypt from 'jsencrypt'\n\t\t\t\n\t\t\t// 模拟RSA加密过程\n\t\t\t// const encrypt = new JSEncrypt()\n\t\t\t// encrypt.setPublicKey(this.RSA_PUBLIC_KEY)\n\t\t\t// return encrypt.encrypt(data)\n\t\t\t\n\t\t\t// 临时模拟加密结果\n\t\t\tconst base64Data = btoa(unescape(encodeURIComponent(data)))\n\t\t\treturn `RSA_ENCRYPTED_${base64Data}`\n\t\t} catch (error) {\n\t\t\tconsole.error('RSA加密失败:', error)\n\t\t\tthrow new Error('密钥加密失败')\n\t\t}\n\t}\n\t\n\t/**\n\t * 混合加密请求参数\n\t * @param {Object} params - 请求参数\n\t * @returns {Object} 加密后的请求对象\n\t */\n\tstatic encryptRequest(params) {\n\t\ttry {\n\t\t\t// 1. 生成AES密钥\n\t\t\tconst aesKey = this.generateAESKey()\n\t\t\t\n\t\t\t// 2. 将参数转换为JSON字符串并用AES加密\n\t\t\tconst paramsJson = JSON.stringify(params)\n\t\t\tconst encryptedParams = this.aesEncrypt(paramsJson, aesKey)\n\t\t\t\n\t\t\t// 3. 创建包含AES密钥的对象\n\t\t\tconst keyObject = { aesKey: aesKey }\n\t\t\tconst keyJson = JSON.stringify(keyObject)\n\t\t\t\n\t\t\t// 4. 用RSA加密AES密钥\n\t\t\tconst encryptedKey = this.rsaEncrypt(keyJson)\n\t\t\t\n\t\t\t// 5. 返回加密后的请求对象\n\t\t\treturn {\n\t\t\t\tencryptedData: encryptedParams,\n\t\t\t\tencryptedKey: encryptedKey\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('请求加密失败:', error)\n\t\t\tthrow new Error('请求加密失败')\n\t\t}\n\t}\n\t\n\t/**\n\t * 解密响应数据\n\t * @param {string} encryptedResponse - 加密的响应数据\n\t * @param {string} aesKey - AES密钥\n\t * @returns {Object} 解密后的响应对象\n\t */\n\tstatic decryptResponse(encryptedResponse, aesKey) {\n\t\ttry {\n\t\t\tconst decryptedJson = this.aesDecrypt(encryptedResponse, aesKey)\n\t\t\treturn JSON.parse(decryptedJson)\n\t\t} catch (error) {\n\t\t\tconsole.error('响应解密失败:', error)\n\t\t\tthrow new Error('响应解密失败')\n\t\t}\n\t}\n}\n\n/**\n * 简化的加密工具函数\n */\nexport const crypto = {\n\t/**\n\t * 加密请求\n\t * @param {Object} data - 请求数据\n\t * @returns {Object} 加密后的请求\n\t */\n\tencryptRequest: (data) => CryptoUtil.encryptRequest(data),\n\t\n\t/**\n\t * 解密响应\n\t * @param {string} encryptedData - 加密的响应数据\n\t * @param {string} aesKey - AES密钥\n\t * @returns {Object} 解密后的数据\n\t */\n\tdecryptResponse: (encryptedData, aesKey) => CryptoUtil.decryptResponse(encryptedData, aesKey),\n\t\n\t/**\n\t * 生成AES密钥\n\t * @returns {string} AES密钥\n\t */\n\tgenerateAESKey: () => CryptoUtil.generateAESKey()\n}\n"], "names": ["uni"], "mappings": ";;;;;;;;AAQO,MAAM,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAWvB,OAAO,iBAAiB;AACvB,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAQ,IAAG,MAAM,MAAM,CAAC;AAAA,IAC/D;AACD,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,WAAW,MAAM,KAAK;AAC5B,QAAI;AAGH,YAAM,WAAW,GAAG,GAAG,IAAI,IAAI;AAC/B,YAAM,UAAU,KAAK,SAAS,mBAAmB,QAAQ,CAAC,CAAC;AAC3D,aAAO,OAAO,OAAO;AAAA,IACrB,SAAQ,OAAO;AACfA,oBAAAA,MAAc,MAAA,SAAA,yBAAA,YAAY,KAAK;AAC/B,YAAM,IAAI,MAAM,QAAQ;AAAA,IACxB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,WAAW,eAAe,KAAK;AACrC,QAAI;AAEH,UAAI,CAAC,cAAc,WAAW,MAAM,GAAG;AACtC,cAAM,IAAI,MAAM,WAAW;AAAA,MAC3B;AAED,YAAM,UAAU,cAAc,UAAU,CAAC;AACzC,YAAM,UAAU,mBAAmB,OAAO,KAAK,OAAO,CAAC,CAAC;AACxD,YAAM,CAAC,SAAS,GAAG,SAAS,IAAI,QAAQ,MAAM,GAAG;AAEjD,UAAI,YAAY,KAAK;AACpB,cAAM,IAAI,MAAM,OAAO;AAAA,MACvB;AAED,aAAO,UAAU,KAAK,GAAG;AAAA,IACzB,SAAQ,OAAO;AACfA,oBAAAA,MAAc,MAAA,SAAA,yBAAA,YAAY,KAAK;AAC/B,YAAM,IAAI,MAAM,QAAQ;AAAA,IACxB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,WAAW,MAAM;AACvB,QAAI;AAUH,YAAM,aAAa,KAAK,SAAS,mBAAmB,IAAI,CAAC,CAAC;AAC1D,aAAO,iBAAiB,UAAU;AAAA,IAClC,SAAQ,OAAO;AACfA,oBAAAA,MAAc,MAAA,SAAA,yBAAA,YAAY,KAAK;AAC/B,YAAM,IAAI,MAAM,QAAQ;AAAA,IACxB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,eAAe,QAAQ;AAC7B,QAAI;AAEH,YAAM,SAAS,KAAK,eAAgB;AAGpC,YAAM,aAAa,KAAK,UAAU,MAAM;AACxC,YAAM,kBAAkB,KAAK,WAAW,YAAY,MAAM;AAG1D,YAAM,YAAY,EAAE,OAAgB;AACpC,YAAM,UAAU,KAAK,UAAU,SAAS;AAGxC,YAAM,eAAe,KAAK,WAAW,OAAO;AAG5C,aAAO;AAAA,QACN,eAAe;AAAA,QACf;AAAA,MACA;AAAA,IACD,SAAQ,OAAO;AACfA,oBAAAA,MAAA,MAAA,SAAA,0BAAc,WAAW,KAAK;AAC9B,YAAM,IAAI,MAAM,QAAQ;AAAA,IACxB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,gBAAgB,mBAAmB,QAAQ;AACjD,QAAI;AACH,YAAM,gBAAgB,KAAK,WAAW,mBAAmB,MAAM;AAC/D,aAAO,KAAK,MAAM,aAAa;AAAA,IAC/B,SAAQ,OAAO;AACfA,oBAAAA,MAAA,MAAA,SAAA,0BAAc,WAAW,KAAK;AAC9B,YAAM,IAAI,MAAM,QAAQ;AAAA,IACxB;AAAA,EACD;AACF;AAAA;AAvIC,cAHY,YAGL,kBAAiB;AAAA;AAAA;AA4Ib,MAAC,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,gBAAgB,CAAC,SAAS,WAAW,eAAe,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxD,iBAAiB,CAAC,eAAe,WAAW,WAAW,gBAAgB,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5F,gBAAgB,MAAM,WAAW,eAAgB;AAClD;;"}