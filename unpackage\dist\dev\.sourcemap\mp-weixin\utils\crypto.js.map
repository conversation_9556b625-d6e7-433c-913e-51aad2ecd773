{"version": 3, "file": "crypto.js", "sources": ["utils/crypto.js"], "sourcesContent": ["// 混合加密工具类 - 使用真实的加密算法\nimport CryptoJS from 'crypto-js'\nimport JSEncrypt from 'jsencrypt'\nimport { getRSAPublicKey, getAESConfig, getRSAConfig, isDebugEnabled } from '@/config/crypto.js'\n\n/**\n * 混合加密工具类\n * 使用RSA加密AES密钥，AES加密实际数据\n */\nexport class CryptoUtil {\n\n\t/**\n\t * 获取当前RSA公钥\n\t * @returns {string} RSA公钥\n\t */\n\tstatic getRSAPublicKey() {\n\t\treturn getRSAPublicKey()\n\t}\n\n\t/**\n\t * 设置RSA公钥（更新配置）\n\t * @param {string} publicKey - RSA公钥\n\t */\n\tstatic setRSAPublicKey(publicKey) {\n\t\tif (this.validateRSAPublicKey(publicKey)) {\n\t\t\t// 这里应该更新配置，实际项目中可能需要调用API\n\t\t\tconsole.log('RSA公钥已更新')\n\t\t} else {\n\t\t\tthrow new Error('无效的RSA公钥格式')\n\t\t}\n\t}\n\t\n\t/**\n\t * 生成随机AES密钥\n\t * @returns {string} 指定长度的随机字符串\n\t */\n\tstatic generateAESKey() {\n\t\tconst config = getAESConfig()\n\t\tconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\n\t\tlet result = ''\n\t\tfor (let i = 0; i < config.KEY_LENGTH; i++) {\n\t\t\tresult += chars.charAt(Math.floor(Math.random() * chars.length))\n\t\t}\n\n\t\tif (isDebugEnabled()) {\n\t\t\tconsole.log(`生成AES密钥，长度: ${config.KEY_LENGTH}`)\n\t\t}\n\n\t\treturn result\n\t}\n\t\n\t/**\n\t * AES加密 - 使用CryptoJS实现真正的AES加密\n\t * @param {string} data - 要加密的数据\n\t * @param {string} key - AES密钥\n\t * @returns {string} 加密后的数据\n\t */\n\tstatic aesEncrypt(data, key) {\n\t\ttry {\n\t\t\tconst config = getAESConfig()\n\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('AES加密开始，数据长度:', data.length)\n\t\t\t}\n\n\t\t\t// 使用CryptoJS进行AES加密\n\t\t\tconst encrypted = CryptoJS.AES.encrypt(data, key, {\r\n\t\t\t\tiv: iv,\n\t\t\t\tmode: CryptoJS.mode[config.MODE],\n\t\t\t\tpadding: CryptoJS.pad[config.PADDING]\n\t\t\t})\n\n\t\t\tconst result = encrypted.toString()\n\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('AES加密完成，结果长度:', result.length)\n\t\t\t}\n\n\t\t\treturn result\n\t\t} catch (error) {\n\t\t\tconsole.error('AES加密失败:', error)\n\t\t\tthrow new Error('数据加密失败: ' + error.message)\n\t\t}\n\t}\n\n\t/**\n\t * AES解密 - 使用CryptoJS实现真正的AES解密\n\t * @param {string} encryptedData - 加密的数据\n\t * @param {string} key - AES密钥\n\t * @returns {string} 解密后的数据\n\t */\n\tstatic aesDecrypt(encryptedData, key) {\n\t\ttry {\n\t\t\tconst config = getAESConfig()\n\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('AES解密开始，数据长度:', encryptedData.length)\n\t\t\t}\n\n\t\t\t// 使用CryptoJS进行AES解密\n\t\t\tconst decrypted = CryptoJS.AES.decrypt(encryptedData, key, {\r\n\t\t\t\tiv: iv,\n\t\t\t\tmode: CryptoJS.mode[config.MODE],\n\t\t\t\tpadding: CryptoJS.pad[config.PADDING]\n\t\t\t})\n\n\t\t\tconst decryptedText = decrypted.toString(CryptoJS.enc.Utf8)\n\n\t\t\tif (!decryptedText) {\n\t\t\t\tthrow new Error('解密结果为空，可能是密钥错误或数据损坏')\n\t\t\t}\n\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('AES解密完成，结果长度:', decryptedText.length)\n\t\t\t}\n\n\t\t\treturn decryptedText\n\t\t} catch (error) {\n\t\t\tconsole.error('AES解密失败:', error)\n\t\t\tthrow new Error('数据解密失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t/**\n\t * RSA加密 - 使用JSEncrypt实现真正的RSA加密\n\t * @param {string} data - 要加密的数据\n\t * @returns {string} 加密后的数据\n\t */\n\tstatic rsaEncrypt(data) {\n\t\ttry {\n\t\t\tconst publicKey = this.getRSAPublicKey()\n\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('RSA加密开始，数据长度:', data.length)\n\t\t\t\tconsole.log('使用公钥长度:', publicKey.length)\n\t\t\t}\n\n\t\t\t// 检查数据长度（RSA有长度限制）\n\t\t\tif (data.length > 200) {\n\t\t\t\tconsole.warn('RSA加密数据较长，可能失败。建议数据长度小于200字符')\n\t\t\t}\n\n\t\t\t// 使用JSEncrypt进行RSA加密\n\t\t\tconst encrypt = new JSEncrypt()\n\t\t\tencrypt.setPublicKey(publicKey)\n\n\t\t\tconst encrypted = encrypt.encrypt(data)\n\t\t\tif (!encrypted) {\n\t\t\t\tthrow new Error('RSA加密失败，可能是公钥格式错误或数据过长')\n\t\t\t}\n\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('RSA加密完成，结果长度:', encrypted.length)\n\t\t\t}\n\n\t\t\treturn encrypted\n\t\t} catch (error) {\n\t\t\tconsole.error('RSA加密失败:', error)\n\t\t\tthrow new Error('密钥加密失败: ' + error.message)\n\t\t}\n\t}\n\n\t/**\n\t * RSA解密 - 使用JSEncrypt实现真正的RSA解密（需要私钥，通常在服务器端）\n\t * @param {string} encryptedData - 加密的数据\n\t * @param {string} privateKey - RSA私钥\n\t * @returns {string} 解密后的数据\n\t */\n\tstatic rsaDecrypt(encryptedData, privateKey) {\n\t\ttry {\n\t\t\t// 使用JSEncrypt进行RSA解密\n\t\t\tconst decrypt = new JSEncrypt()\n\t\t\tdecrypt.setPrivateKey(privateKey)\n\n\t\t\tconst decrypted = decrypt.decrypt(encryptedData)\n\t\t\tif (!decrypted) {\n\t\t\t\tthrow new Error('RSA解密失败，可能是私钥错误或数据损坏')\n\t\t\t}\n\n\t\t\treturn decrypted\n\t\t} catch (error) {\n\t\t\tconsole.error('RSA解密失败:', error)\n\t\t\tthrow new Error('密钥解密失败')\n\t\t}\n\t}\n\t\n\t/**\n\t * 混合加密请求参数\n\t * @param {Object} params - 请求参数\n\t * @returns {Object} 加密后的请求对象，包含AES密钥用于后续解密响应\n\t */\n\tstatic encryptRequest(params) {\n\t\ttry {\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('开始混合加密，参数:', JSON.stringify(params))\n\t\t\t}\n\n\t\t\t// 1. 生成AES密钥\n\t\t\tconst aesKey = this.generateAESKey()\r\n\t\t\tparams.aesKey = aesKey\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('生成AES密钥:', aesKey.substring(0, 8) + '...')\n\t\t\t}\n\n\t\t\t// 2. 将参数转换为JSON字符串并用AES加密\n\t\t\tconst paramsJson = JSON.stringify(params)\n\t\t\t// const encryptedParams = this.aesEncrypt(paramsJson, aesKey)\r\n\t\t\t// 先采用直接使用RSA加密整个前端传参\r\n\t\t\tconst encryptedParams = this.rsaEncrypt(paramsJson)\n\n\t\t\t// 3. 创建包含AES密钥的对象\r\n\t\t\t// 先采用直接使用RSA加密整个前端传参\n\t\t\t// const keyObject = { aesKey: aesKey }\n\t\t\t// const keyJson = JSON.stringify(keyObject)\n\n\t\t\t// 4. 用RSA加密AES密钥\r\n\t\t\t// 先采用直接使用RSA加密整个前端传参\n\t\t\t// const encryptedKey = this.rsaEncrypt(keyJson)\n\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('混合加密完成')\n\t\t\t\tconsole.log('- 加密数据长度:', encryptedParams.length)\n\t\t\t\t// console.log('- 加密密钥长度:', encryptedKey.length)// 先采用直接使用RSA加密整个前端传参\n\t\t\t}\n\n\t\t\t// 5. 返回加密后的请求对象，同时返回AES密钥用于解密响应\n\t\t\treturn {\n\t\t\t\tencryptedData: encryptedParams,\n\t\t\t\t// encryptedKey: encryptedKey,// 先采用直接使用RSA加密整个前端传参\n\t\t\t\taesKey: aesKey // 保存AES密钥用于解密响应\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('请求加密失败:', error)\n\t\t\tthrow new Error('请求加密失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t/**\n\t * 解密响应数据\n\t * @param {string} encryptedResponse - 加密的响应数据\n\t * @param {string} aesKey - AES密钥\n\t * @returns {Object} 解密后的响应对象\n\t */\n\tstatic decryptResponse(encryptedResponse, aesKey) {\n\t\ttry {\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('开始解密响应数据')\n\t\t\t\tconsole.log('- 密钥:', aesKey.substring(0, 8) + '...')\n\t\t\t\tconsole.log('- 加密数据长度:', encryptedResponse.length)\n\t\t\t}\n\n\t\t\tconst decryptedJson = this.aesDecrypt(encryptedResponse, aesKey)\n\t\t\tconst responseData = JSON.parse(decryptedJson)\n\n\t\t\tif (isDebugEnabled()) {\n\t\t\t\tconsole.log('响应解密成功')\n\t\t\t\tconsole.log('- 解密数据长度:', decryptedJson.length)\n\t\t\t}\n\n\t\t\treturn responseData\n\t\t} catch (error) {\n\t\t\tconsole.error('响应解密失败:', error)\n\t\t\tthrow new Error('响应解密失败: ' + error.message)\n\t\t}\n\t}\n\n\t/**\n\t * 验证RSA公钥格式\n\t * @param {string} publicKey - RSA公钥\n\t * @returns {boolean} 是否为有效格式\n\t */\n\tstatic validateRSAPublicKey(publicKey) {\n\t\ttry {\n\t\t\tconst keyRegex = /^-----BEGIN PUBLIC KEY-----[\\s\\S]*-----END PUBLIC KEY-----$/\n\t\t\treturn keyRegex.test(publicKey.trim())\n\t\t} catch (error) {\n\t\t\treturn false\n\t\t}\n\t}\n\n\t/**\n\t * 测试加密解密功能\n\t * @returns {boolean} 测试是否通过\n\t */\n\tstatic testEncryption() {\n\t\ttry {\n\t\t\tconsole.log('开始加密解密测试...')\n\n\t\t\t// 测试AES加密解密\n\t\t\tconst testData = 'Hello, World! 测试数据 123'\n\t\t\tconst testKey = this.generateAESKey()\n\n\t\t\tconst encrypted = this.aesEncrypt(testData, testKey)\n\t\t\tconst decrypted = this.aesDecrypt(encrypted, testKey)\n\n\t\t\tif (decrypted !== testData) {\n\t\t\t\tthrow new Error('AES加密解密测试失败')\n\t\t\t}\n\n\t\t\tconsole.log('AES加密解密测试通过')\n\n\t\t\t// 测试RSA加密（只测试加密，解密需要私钥）\n\t\t\tconst testKeyObject = { aesKey: testKey }\n\t\t\tconst rsaEncrypted = this.rsaEncrypt(JSON.stringify(testKeyObject))\n\n\t\t\tif (!rsaEncrypted || rsaEncrypted.length === 0) {\n\t\t\t\tthrow new Error('RSA加密测试失败')\n\t\t\t}\n\n\t\t\tconsole.log('RSA加密测试通过')\n\n\t\t\t// 测试混合加密\n\t\t\tconst testParams = { message: 'test', timestamp: Date.now() }\n\t\t\tconst encryptedRequest = this.encryptRequest(testParams)\n\n\t\t\tif (!encryptedRequest.encryptedData || !encryptedRequest.encryptedKey) {\n\t\t\t\tthrow new Error('混合加密测试失败')\n\t\t\t}\n\n\t\t\tconsole.log('混合加密测试通过')\n\t\t\tconsole.log('所有加密测试通过！')\n\n\t\t\treturn true\n\t\t} catch (error) {\n\t\t\tconsole.error('加密测试失败:', error)\n\t\t\treturn false\n\t\t}\n\t}\n}\n\n/**\n * 简化的加密工具函数\n */\nexport const crypto = {\n\t/**\n\t * 加密请求\n\t * @param {Object} data - 请求数据\n\t * @returns {Object} 加密后的请求\n\t */\n\tencryptRequest: (data) => CryptoUtil.encryptRequest(data),\n\t\n\t/**\n\t * 解密响应\n\t * @param {string} encryptedData - 加密的响应数据\n\t * @param {string} aesKey - AES密钥\n\t * @returns {Object} 解密后的数据\n\t */\n\tdecryptResponse: (encryptedData, aesKey) => CryptoUtil.decryptResponse(encryptedData, aesKey),\n\t\n\t/**\n\t * 生成AES密钥\n\t * @returns {string} AES密钥\n\t */\n\tgenerateAESKey: () => CryptoUtil.generateAESKey(),\r\n\t/**\r\n\t * 加密请求\r\n\t * @param {Object} data - 请求数据\r\n\t * @returns {Object} 加密后的请求\r\n\t */\r\n\trsaEncrypt: ()=>{CryptoUtil.rsaEncrypt(data)}\n}\n"], "names": ["getRSAPublicKey", "uni", "getAESConfig", "data", "isDebugEnabled", "CryptoJS", "JSEncrypt"], "mappings": ";;;AASO,MAAM,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,OAAO,kBAAkB;AACxB,WAAOA,8BAAiB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,gBAAgB,WAAW;AACjC,QAAI,KAAK,qBAAqB,SAAS,GAAG;AAEzCC,oBAAAA,MAAY,MAAA,OAAA,yBAAA,UAAU;AAAA,IACzB,OAAS;AACN,YAAM,IAAI,MAAM,YAAY;AAAA,IAC5B;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,iBAAiB;AACvB,UAAM,SAASC,cAAAA,aAAc;AAC7B,UAAM,QAAQ;AACd,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,OAAO,YAAY,KAAK;AAC3C,gBAAU,MAAM,OAAO,KAAK,MAAM,KAAK,OAAQ,IAAG,MAAM,MAAM,CAAC;AAAA,IAC/D;AAMD,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,WAAWC,OAAM,KAAK;AAC5B,QAAI;AACH,YAAM,SAASD,cAAAA,aAAc;AAE7B,UAAIE,cAAAA,eAAgB;AAAE;AAKtB,YAAM,YAAYC,cAAAA,SAAS,IAAI,QAAQF,OAAM,KAAK;AAAA,QACjD;AAAA,QACA,MAAME,cAAQ,SAAC,KAAK,OAAO,IAAI;AAAA,QAC/B,SAASA,cAAQ,SAAC,IAAI,OAAO,OAAO;AAAA,MACxC,CAAI;AAED,YAAM,SAAS,UAAU,SAAU;AAEnC,UAAID,cAAAA,eAAgB;AAAE;AAItB,aAAO;AAAA,IACP,SAAQ,OAAO;AACfH,oBAAAA,MAAc,MAAA,SAAA,yBAAA,YAAY,KAAK;AAC/B,YAAM,IAAI,MAAM,aAAa,MAAM,OAAO;AAAA,IAC1C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,WAAW,eAAe,KAAK;AACrC,QAAI;AACH,YAAM,SAASC,cAAAA,aAAc;AAE7B,UAAIE,cAAAA,eAAgB;AAAE;AAKtB,YAAM,YAAYC,cAAAA,SAAS,IAAI,QAAQ,eAAe,KAAK;AAAA,QAC1D;AAAA,QACA,MAAMA,cAAQ,SAAC,KAAK,OAAO,IAAI;AAAA,QAC/B,SAASA,cAAQ,SAAC,IAAI,OAAO,OAAO;AAAA,MACxC,CAAI;AAED,YAAM,gBAAgB,UAAU,SAASA,cAAAA,SAAS,IAAI,IAAI;AAE1D,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI,MAAM,qBAAqB;AAAA,MACrC;AAED,UAAID,cAAAA,eAAgB;AAAE;AAItB,aAAO;AAAA,IACP,SAAQ,OAAO;AACfH,oBAAAA,MAAc,MAAA,SAAA,0BAAA,YAAY,KAAK;AAC/B,YAAM,IAAI,MAAM,aAAa,MAAM,OAAO;AAAA,IAC1C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,WAAWE,OAAM;AACvB,QAAI;AACH,YAAM,YAAY,KAAK,gBAAiB;AAExC,UAAIC,cAAAA,eAAgB;AAAE;AAMtB,UAAID,MAAK,SAAS,KAAK;AACtBF,sBAAAA,MAAA,MAAA,QAAA,0BAAa,8BAA8B;AAAA,MAC3C;AAGD,YAAM,UAAU,IAAIK,wBAAW;AAC/B,cAAQ,aAAa,SAAS;AAE9B,YAAM,YAAY,QAAQ,QAAQH,KAAI;AACtC,UAAI,CAAC,WAAW;AACf,cAAM,IAAI,MAAM,wBAAwB;AAAA,MACxC;AAED,UAAIC,cAAAA,eAAgB;AAAE;AAItB,aAAO;AAAA,IACP,SAAQ,OAAO;AACfH,oBAAAA,MAAc,MAAA,SAAA,0BAAA,YAAY,KAAK;AAC/B,YAAM,IAAI,MAAM,aAAa,MAAM,OAAO;AAAA,IAC1C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,WAAW,eAAe,YAAY;AAC5C,QAAI;AAEH,YAAM,UAAU,IAAIK,wBAAW;AAC/B,cAAQ,cAAc,UAAU;AAEhC,YAAM,YAAY,QAAQ,QAAQ,aAAa;AAC/C,UAAI,CAAC,WAAW;AACf,cAAM,IAAI,MAAM,sBAAsB;AAAA,MACtC;AAED,aAAO;AAAA,IACP,SAAQ,OAAO;AACfL,oBAAAA,MAAc,MAAA,SAAA,0BAAA,YAAY,KAAK;AAC/B,YAAM,IAAI,MAAM,QAAQ;AAAA,IACxB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,eAAe,QAAQ;AAC7B,QAAI;AACH,UAAIG,cAAAA,eAAgB;AAAE;AAKtB,YAAM,SAAS,KAAK,eAAgB;AACpC,aAAO,SAAS;AAChB,UAAIA,cAAAA,eAAgB;AAAE;AAKtB,YAAM,aAAa,KAAK,UAAU,MAAM;AAGxC,YAAM,kBAAkB,KAAK,WAAW,UAAU;AAWlD,UAAIA,cAAAA,eAAgB;AAAE;AAOtB,aAAO;AAAA,QACN,eAAe;AAAA;AAAA,QAEf;AAAA;AAAA,MACA;AAAA,IACD,SAAQ,OAAO;AACfH,oBAAAA,MAAA,MAAA,SAAA,0BAAc,WAAW,KAAK;AAC9B,YAAM,IAAI,MAAM,aAAa,MAAM,OAAO;AAAA,IAC1C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,gBAAgB,mBAAmB,QAAQ;AACjD,QAAI;AACH,UAAIG,cAAAA,eAAgB;AAAE;AAMtB,YAAM,gBAAgB,KAAK,WAAW,mBAAmB,MAAM;AAC/D,YAAM,eAAe,KAAK,MAAM,aAAa;AAE7C,UAAIA,cAAAA,eAAgB;AAAE;AAKtB,aAAO;AAAA,IACP,SAAQ,OAAO;AACfH,oBAAAA,MAAA,MAAA,SAAA,0BAAc,WAAW,KAAK;AAC9B,YAAM,IAAI,MAAM,aAAa,MAAM,OAAO;AAAA,IAC1C;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,qBAAqB,WAAW;AACtC,QAAI;AACH,YAAM,WAAW;AACjB,aAAO,SAAS,KAAK,UAAU,KAAI,CAAE;AAAA,IACrC,SAAQ,OAAO;AACf,aAAO;AAAA,IACP;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,iBAAiB;AACvB,QAAI;AACHA,oBAAAA,MAAA,MAAA,OAAA,0BAAY,aAAa;AAGzB,YAAM,WAAW;AACjB,YAAM,UAAU,KAAK,eAAgB;AAErC,YAAM,YAAY,KAAK,WAAW,UAAU,OAAO;AACnD,YAAM,YAAY,KAAK,WAAW,WAAW,OAAO;AAEpD,UAAI,cAAc,UAAU;AAC3B,cAAM,IAAI,MAAM,aAAa;AAAA,MAC7B;AAEDA,oBAAAA,MAAA,MAAA,OAAA,0BAAY,aAAa;AAGzB,YAAM,gBAAgB,EAAE,QAAQ,QAAS;AACzC,YAAM,eAAe,KAAK,WAAW,KAAK,UAAU,aAAa,CAAC;AAElE,UAAI,CAAC,gBAAgB,aAAa,WAAW,GAAG;AAC/C,cAAM,IAAI,MAAM,WAAW;AAAA,MAC3B;AAEDA,oBAAAA,MAAY,MAAA,OAAA,0BAAA,WAAW;AAGvB,YAAM,aAAa,EAAE,SAAS,QAAQ,WAAW,KAAK,MAAO;AAC7D,YAAM,mBAAmB,KAAK,eAAe,UAAU;AAEvD,UAAI,CAAC,iBAAiB,iBAAiB,CAAC,iBAAiB,cAAc;AACtE,cAAM,IAAI,MAAM,UAAU;AAAA,MAC1B;AAEDA,oBAAAA,MAAY,MAAA,OAAA,0BAAA,UAAU;AACtBA,oBAAAA,MAAY,MAAA,OAAA,0BAAA,WAAW;AAEvB,aAAO;AAAA,IACP,SAAQ,OAAO;AACfA,oBAAAA,MAAA,MAAA,SAAA,0BAAc,WAAW,KAAK;AAC9B,aAAO;AAAA,IACP;AAAA,EACD;AACF;AAKY,MAAC,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,gBAAgB,CAACE,UAAS,WAAW,eAAeA,KAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxD,iBAAiB,CAAC,eAAe,WAAW,WAAW,gBAAgB,eAAe,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5F,gBAAgB,MAAM,WAAW,eAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjD,YAAY,MAAI;AAAC,eAAW,WAAW,IAAI;AAAA,EAAC;AAC7C;;;"}