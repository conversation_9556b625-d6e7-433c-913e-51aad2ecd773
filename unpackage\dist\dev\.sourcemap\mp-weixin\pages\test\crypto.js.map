{"version": 3, "file": "crypto.js", "sources": ["pages/test/crypto.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC9jcnlwdG8udnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"title\">加密功能测试</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"content\">\n\t\t\t<!-- 测试控制区域 -->\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-text\">加密解密测试</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"test-controls\">\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testAESEncryption\">测试AES加密</button>\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testRSAEncryption\">测试RSA加密</button>\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testHybridEncryption\">测试混合加密</button>\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testFullProcess\">测试完整流程</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 测试输入区域 -->\n\t\t\t<view class=\"input-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-text\">测试数据</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<textarea \n\t\t\t\t\tclass=\"test-input\" \n\t\t\t\t\tv-model=\"testData\" \n\t\t\t\t\tplaceholder=\"输入要测试的数据...\"\n\t\t\t\t></textarea>\n\t\t\t\t\n\t\t\t\t<view class=\"input-controls\">\n\t\t\t\t\t<button class=\"control-btn\" @click=\"generateTestData\">生成测试数据</button>\n\t\t\t\t\t<button class=\"control-btn\" @click=\"clearTestData\">清空数据</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 测试结果区域 -->\n\t\t\t<view class=\"result-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-text\">测试结果</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"result-item\" v-for=\"(result, index) in testResults\" :key=\"index\">\n\t\t\t\t\t<view class=\"result-header\">\n\t\t\t\t\t\t<text class=\"result-title\">{{ result.title }}</text>\n\t\t\t\t\t\t<text class=\"result-status\" :class=\"result.success ? 'success' : 'error'\">\n\t\t\t\t\t\t\t{{ result.success ? '✓ 成功' : '✗ 失败' }}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"result-content\">\n\t\t\t\t\t\t<text class=\"result-text\">{{ result.message }}</text>\n\t\t\t\t\t\t<view class=\"result-data\" v-if=\"result.data\">\n\t\t\t\t\t\t\t<text class=\"data-label\">数据:</text>\n\t\t\t\t\t\t\t<text class=\"data-content\">{{ result.data }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 密钥信息区域 -->\n\t\t\t<view class=\"key-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-text\">密钥信息</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"key-item\">\n\t\t\t\t\t<text class=\"key-label\">RSA公钥状态:</text>\n\t\t\t\t\t<text class=\"key-status\" :class=\"rsaKeyValid ? 'valid' : 'invalid'\">\n\t\t\t\t\t\t{{ rsaKeyValid ? '有效' : '无效' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"key-item\">\n\t\t\t\t\t<text class=\"key-label\">当前AES密钥:</text>\n\t\t\t\t\t<text class=\"key-value\">{{ currentAESKey || '未生成' }}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<button class=\"key-btn\" @click=\"generateNewKeys\">生成新密钥</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\n\timport { ref, onMounted } from 'vue'\n\timport { CryptoUtil } from '@/utils/crypto.js'\n\timport { keyManager } from '@/utils/keyGenerator.js'\n\t\n\t// 响应式数据\n\tconst testData = ref('Hello, World! 这是一个测试数据 123456')\n\tconst testResults = ref([])\n\tconst rsaKeyValid = ref(false)\n\tconst currentAESKey = ref('')\n\t\n\t// 页面加载时初始化\n\tonMounted(() => {\n\t\tinitializeTest()\n\t})\n\t\n\t// 初始化测试环境\n\tconst initializeTest = () => {\n\t\ttry {\n\t\t\t// 验证RSA公钥\n\t\t\trsaKeyValid.value = CryptoUtil.validateRSAPublicKey(CryptoUtil.getRSAPublicKey())\n\t\t\t\n\t\t\t// 生成AES密钥\n\t\t\tcurrentAESKey.value = CryptoUtil.generateAESKey()\n\t\t\t\n\t\t\taddTestResult('初始化', true, '测试环境初始化成功')\n\t\t} catch (error) {\n\t\t\taddTestResult('初始化', false, '测试环境初始化失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 测试AES加密\n\tconst testAESEncryption = async () => {\n\t\ttry {\n\t\t\tconst data = testData.value || '默认测试数据'\n\t\t\tconst key = CryptoUtil.generateAESKey()\n\t\t\t\n\t\t\t// 加密\n\t\t\tconst encrypted = CryptoUtil.aesEncrypt(data, key)\n\t\t\taddTestResult('AES加密', true, '数据加密成功', encrypted.substring(0, 50) + '...')\n\t\t\t\n\t\t\t// 解密\n\t\t\tconst decrypted = CryptoUtil.aesDecrypt(encrypted, key)\n\t\t\tconst success = decrypted === data\n\t\t\t\n\t\t\taddTestResult('AES解密', success, \n\t\t\t\tsuccess ? '数据解密成功，与原数据一致' : '数据解密失败，与原数据不一致',\n\t\t\t\tdecrypted\n\t\t\t)\n\t\t} catch (error) {\n\t\t\taddTestResult('AES加密解密', false, '测试失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 测试RSA加密\n\tconst testRSAEncryption = async () => {\n\t\ttry {\n\t\t\tconst data = JSON.stringify({ aesKey: currentAESKey.value })\n\t\t\t\n\t\t\t// RSA加密\n\t\t\tconst encrypted = CryptoUtil.rsaEncrypt(data)\n\t\t\taddTestResult('RSA加密', true, 'RSA加密成功', encrypted.substring(0, 50) + '...')\n\t\t\t\n\t\t} catch (error) {\n\t\t\taddTestResult('RSA加密', false, '测试失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 测试混合加密\n\tconst testHybridEncryption = async () => {\n\t\ttry {\n\t\t\tconst data = {\n\t\t\t\tmessage: testData.value || '默认测试数据',\n\t\t\t\ttimestamp: Date.now(),\n\t\t\t\tuserId: 'test-user-123'\n\t\t\t}\n\t\t\t\n\t\t\t// 混合加密\n\t\t\tconst encryptedRequest = CryptoUtil.encryptRequest(data)\n\t\t\t\n\t\t\taddTestResult('混合加密', true, '混合加密成功', \n\t\t\t\t`加密数据长度: ${encryptedRequest.encryptedData.length}, 密钥长度: ${encryptedRequest.encryptedKey.length}`\n\t\t\t)\n\t\t\t\n\t\t\t// 模拟解密响应\n\t\t\tconst mockResponse = JSON.stringify({\n\t\t\t\tsuccess: true,\n\t\t\t\tmessage: '这是服务器返回的加密响应',\n\t\t\t\tdata: { result: 'test-result' }\n\t\t\t})\n\t\t\t\n\t\t\tconst encryptedResponse = CryptoUtil.aesEncrypt(mockResponse, encryptedRequest.aesKey)\n\t\t\tconst decryptedResponse = CryptoUtil.decryptResponse(encryptedResponse, encryptedRequest.aesKey)\n\t\t\t\n\t\t\taddTestResult('响应解密', true, '响应解密成功', JSON.stringify(decryptedResponse))\n\t\t\t\n\t\t} catch (error) {\n\t\t\taddTestResult('混合加密', false, '测试失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 测试完整流程\n\tconst testFullProcess = async () => {\n\t\ttry {\n\t\t\t// 运行完整的加密测试\n\t\t\tconst success = CryptoUtil.testEncryption()\n\t\t\taddTestResult('完整流程测试', success, \n\t\t\t\tsuccess ? '所有加密功能测试通过' : '加密功能测试失败'\n\t\t\t)\n\t\t} catch (error) {\n\t\t\taddTestResult('完整流程测试', false, '测试失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 生成测试数据\n\tconst generateTestData = () => {\n\t\tconst samples = [\n\t\t\t'Hello, World! 测试数据',\n\t\t\t'{\"name\":\"张三\",\"age\":25,\"city\":\"北京\"}',\n\t\t\t'这是一个包含中文的测试字符串，用于验证加密功能是否正常工作。',\n\t\t\t'1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',\n\t\t\t'特殊字符测试: !@#$%^&*()_+-=[]{}|;:,.<>?'\n\t\t]\n\t\t\n\t\ttestData.value = samples[Math.floor(Math.random() * samples.length)]\n\t}\n\t\n\t// 清空测试数据\n\tconst clearTestData = () => {\n\t\ttestData.value = ''\n\t}\n\t\n\t// 生成新密钥\n\tconst generateNewKeys = async () => {\n\t\ttry {\n\t\t\t// 生成新的AES密钥\n\t\t\tcurrentAESKey.value = CryptoUtil.generateAESKey()\n\t\t\t\n\t\t\t// 可以选择生成新的RSA密钥对（需要时间较长）\n\t\t\t// const keyPair = await keyManager.generateKeyPair()\n\t\t\t// CryptoUtil.setRSAPublicKey(keyPair.publicKey)\n\t\t\t// rsaKeyValid.value = true\n\t\t\t\n\t\t\taddTestResult('密钥生成', true, '新密钥生成成功')\n\t\t} catch (error) {\n\t\t\taddTestResult('密钥生成', false, '密钥生成失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 添加测试结果\n\tconst addTestResult = (title, success, message, data = null) => {\n\t\ttestResults.value.unshift({\n\t\t\ttitle,\n\t\t\tsuccess,\n\t\t\tmessage,\n\t\t\tdata,\n\t\t\ttimestamp: new Date().toLocaleTimeString()\n\t\t})\n\t\t\n\t\t// 限制结果数量\n\t\tif (testResults.value.length > 10) {\n\t\t\ttestResults.value = testResults.value.slice(0, 10)\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tpadding: 30rpx;\n\t\tbackground-color: #f5f5f5;\n\t\tmin-height: 100vh;\n\t}\n\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.title {\n\t\tfont-size: 40rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 30rpx;\n\t}\n\n\t/* 通用区域样式 */\n\t.test-section,\n\t.input-section,\n\t.result-section,\n\t.key-section {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.section-title {\n\t\tmargin-bottom: 20rpx;\n\t\tborder-bottom: 2rpx solid #e0e0e0;\n\t\tpadding-bottom: 15rpx;\n\t}\n\n\t.title-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t/* 测试控制区域 */\n\t.test-controls {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 15rpx;\n\t}\n\n\t.test-btn {\n\t\tflex: 1;\n\t\tmin-width: 200rpx;\n\t\theight: 70rpx;\n\t\tbackground-color: #4CAF50;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t}\n\n\t/* 输入区域 */\n\t.test-input {\n\t\twidth: 100%;\n\t\tmin-height: 150rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t\tfont-size: 28rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.input-controls {\n\t\tdisplay: flex;\n\t\tgap: 15rpx;\n\t}\n\n\t.control-btn {\n\t\tflex: 1;\n\t\theight: 60rpx;\n\t\tbackground-color: #2196F3;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 26rpx;\n\t}\n\n\t/* 结果区域 */\n\t.result-item {\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t\tmargin-bottom: 15rpx;\n\t}\n\n\t.result-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.result-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.result-status {\n\t\tfont-size: 24rpx;\n\t\tpadding: 5rpx 15rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\n\t.result-status.success {\n\t\tbackground-color: #e8f5e8;\n\t\tcolor: #4CAF50;\n\t}\n\n\t.result-status.error {\n\t\tbackground-color: #ffeaea;\n\t\tcolor: #f44336;\n\t}\n\n\t.result-content {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666666;\n\t\tline-height: 1.4;\n\t}\n\n\t.result-data {\n\t\tmargin-top: 10rpx;\n\t\tpadding: 15rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 6rpx;\n\t}\n\n\t.data-label {\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.data-content {\n\t\tdisplay: block;\n\t\tmargin-top: 8rpx;\n\t\tword-break: break-all;\n\t\tfont-family: monospace;\n\t}\n\n\t/* 密钥区域 */\n\t.key-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 15rpx;\n\t\tpadding: 15rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 8rpx;\n\t}\n\n\t.key-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.key-status.valid {\n\t\tcolor: #4CAF50;\n\t\tfont-weight: bold;\n\t}\n\n\t.key-status.invalid {\n\t\tcolor: #f44336;\n\t\tfont-weight: bold;\n\t}\n\n\t.key-value {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t\tfont-family: monospace;\n\t\tmax-width: 300rpx;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t.key-btn {\n\t\twidth: 100%;\n\t\theight: 70rpx;\n\t\tbackground-color: #FF9800;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t\tmargin-top: 20rpx;\n\t}\n</style>\n", "import MiniProgramPage from 'E:/FM93交通之声/2025Project/BankCodeSearch/pages/test/crypto.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "CryptoUtil", "MiniProgramPage"], "mappings": ";;;;;;AA6FC,UAAM,WAAWA,cAAG,IAAC,+BAA+B;AACpD,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,cAAcA,cAAG,IAAC,KAAK;AAC7B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAG5BC,kBAAAA,UAAU,MAAM;AACf,qBAAgB;AAAA,IAClB,CAAE;AAGD,UAAM,iBAAiB,MAAM;AAC5B,UAAI;AAEH,oBAAY,QAAQC,aAAU,WAAC,qBAAqBA,aAAU,WAAC,gBAAe,CAAE;AAGhF,sBAAc,QAAQA,aAAU,WAAC,eAAgB;AAEjD,sBAAc,OAAO,MAAM,WAAW;AAAA,MACtC,SAAQ,OAAO;AACf,sBAAc,OAAO,OAAO,gBAAgB,MAAM,OAAO;AAAA,MACzD;AAAA,IACD;AAGD,UAAM,oBAAoB,YAAY;AACrC,UAAI;AACH,cAAM,OAAO,SAAS,SAAS;AAC/B,cAAM,MAAMA,aAAU,WAAC,eAAgB;AAGvC,cAAM,YAAYA,aAAU,WAAC,WAAW,MAAM,GAAG;AACjD,sBAAc,SAAS,MAAM,UAAU,UAAU,UAAU,GAAG,EAAE,IAAI,KAAK;AAGzE,cAAM,YAAYA,aAAU,WAAC,WAAW,WAAW,GAAG;AACtD,cAAM,UAAU,cAAc;AAE9B;AAAA,UAAc;AAAA,UAAS;AAAA,UACtB,UAAU,kBAAkB;AAAA,UAC5B;AAAA,QACA;AAAA,MACD,SAAQ,OAAO;AACf,sBAAc,WAAW,OAAO,WAAW,MAAM,OAAO;AAAA,MACxD;AAAA,IACD;AAGD,UAAM,oBAAoB,YAAY;AACrC,UAAI;AACH,cAAM,OAAO,KAAK,UAAU,EAAE,QAAQ,cAAc,OAAO;AAG3D,cAAM,YAAYA,aAAAA,WAAW,WAAW,IAAI;AAC5C,sBAAc,SAAS,MAAM,WAAW,UAAU,UAAU,GAAG,EAAE,IAAI,KAAK;AAAA,MAE1E,SAAQ,OAAO;AACf,sBAAc,SAAS,OAAO,WAAW,MAAM,OAAO;AAAA,MACtD;AAAA,IACD;AAGD,UAAM,uBAAuB,YAAY;AACxC,UAAI;AACH,cAAM,OAAO;AAAA,UACZ,SAAS,SAAS,SAAS;AAAA,UAC3B,WAAW,KAAK,IAAK;AAAA,UACrB,QAAQ;AAAA,QACR;AAGD,cAAM,mBAAmBA,aAAAA,WAAW,eAAe,IAAI;AAEvD;AAAA,UAAc;AAAA,UAAQ;AAAA,UAAM;AAAA,UAC3B,WAAW,iBAAiB,cAAc,MAAM,WAAW,iBAAiB,aAAa,MAAM;AAAA,QAC/F;AAGD,cAAM,eAAe,KAAK,UAAU;AAAA,UACnC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM,EAAE,QAAQ,cAAe;AAAA,QACnC,CAAI;AAED,cAAM,oBAAoBA,aAAAA,WAAW,WAAW,cAAc,iBAAiB,MAAM;AACrF,cAAM,oBAAoBA,aAAAA,WAAW,gBAAgB,mBAAmB,iBAAiB,MAAM;AAE/F,sBAAc,QAAQ,MAAM,UAAU,KAAK,UAAU,iBAAiB,CAAC;AAAA,MAEvE,SAAQ,OAAO;AACf,sBAAc,QAAQ,OAAO,WAAW,MAAM,OAAO;AAAA,MACrD;AAAA,IACD;AAGD,UAAM,kBAAkB,YAAY;AACnC,UAAI;AAEH,cAAM,UAAUA,aAAU,WAAC,eAAgB;AAC3C;AAAA,UAAc;AAAA,UAAU;AAAA,UACvB,UAAU,eAAe;AAAA,QACzB;AAAA,MACD,SAAQ,OAAO;AACf,sBAAc,UAAU,OAAO,WAAW,MAAM,OAAO;AAAA,MACvD;AAAA,IACD;AAGD,UAAM,mBAAmB,MAAM;AAC9B,YAAM,UAAU;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACA;AAED,eAAS,QAAQ,QAAQ,KAAK,MAAM,KAAK,OAAQ,IAAG,QAAQ,MAAM,CAAC;AAAA,IACnE;AAGD,UAAM,gBAAgB,MAAM;AAC3B,eAAS,QAAQ;AAAA,IACjB;AAGD,UAAM,kBAAkB,YAAY;AACnC,UAAI;AAEH,sBAAc,QAAQA,aAAU,WAAC,eAAgB;AAOjD,sBAAc,QAAQ,MAAM,SAAS;AAAA,MACrC,SAAQ,OAAO;AACf,sBAAc,QAAQ,OAAO,aAAa,MAAM,OAAO;AAAA,MACvD;AAAA,IACD;AAGD,UAAM,gBAAgB,CAAC,OAAO,SAAS,SAAS,OAAO,SAAS;AAC/D,kBAAY,MAAM,QAAQ;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAW,oBAAI,KAAM,GAAC,mBAAoB;AAAA,MAC7C,CAAG;AAGD,UAAI,YAAY,MAAM,SAAS,IAAI;AAClC,oBAAY,QAAQ,YAAY,MAAM,MAAM,GAAG,EAAE;AAAA,MACjD;AAAA,IACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzPF,GAAG,WAAWC,SAAe;"}