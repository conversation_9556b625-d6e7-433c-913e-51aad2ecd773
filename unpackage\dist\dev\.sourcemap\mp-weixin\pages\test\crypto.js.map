{"version": 3, "file": "crypto.js", "sources": ["pages/test/crypto.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdGVzdC9jcnlwdG8udnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"title\">加密功能测试</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"content\">\n\t\t\t<!-- 测试控制区域 -->\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-text\">加密解密测试</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"test-controls\">\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testAESEncryption\">测试AES加密</button>\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testIVExtraction\">测试IV提取</button>\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testRSAEncryption\">测试RSA加密</button>\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testHybridEncryption\">测试混合加密</button>\n\t\t\t\t\t<button class=\"test-btn\" @click=\"testFullProcess\">测试完整流程</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 测试输入区域 -->\n\t\t\t<view class=\"input-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-text\">测试数据</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<textarea \n\t\t\t\t\tclass=\"test-input\" \n\t\t\t\t\tv-model=\"testData\" \n\t\t\t\t\tplaceholder=\"输入要测试的数据...\"\n\t\t\t\t></textarea>\n\t\t\t\t\n\t\t\t\t<view class=\"input-controls\">\n\t\t\t\t\t<button class=\"control-btn\" @click=\"generateTestData\">生成测试数据</button>\n\t\t\t\t\t<button class=\"control-btn\" @click=\"clearTestData\">清空数据</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 测试结果区域 -->\n\t\t\t<view class=\"result-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-text\">测试结果</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"result-item\" v-for=\"(result, index) in testResults\" :key=\"index\">\n\t\t\t\t\t<view class=\"result-header\">\n\t\t\t\t\t\t<text class=\"result-title\">{{ result.title }}</text>\n\t\t\t\t\t\t<text class=\"result-status\" :class=\"result.success ? 'success' : 'error'\">\n\t\t\t\t\t\t\t{{ result.success ? '✓ 成功' : '✗ 失败' }}\n\t\t\t\t\t\t</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"result-content\">\n\t\t\t\t\t\t<text class=\"result-text\">{{ result.message }}</text>\n\t\t\t\t\t\t<view class=\"result-data\" v-if=\"result.data\">\n\t\t\t\t\t\t\t<text class=\"data-label\">数据:</text>\n\t\t\t\t\t\t\t<text class=\"data-content\">{{ result.data }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<!-- 密钥信息区域 -->\n\t\t\t<view class=\"key-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-text\">密钥信息</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"key-item\">\n\t\t\t\t\t<text class=\"key-label\">RSA公钥状态:</text>\n\t\t\t\t\t<text class=\"key-status\" :class=\"rsaKeyValid ? 'valid' : 'invalid'\">\n\t\t\t\t\t\t{{ rsaKeyValid ? '有效' : '无效' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"key-item\">\n\t\t\t\t\t<text class=\"key-label\">当前AES密钥:</text>\n\t\t\t\t\t<text class=\"key-value\">{{ currentAESKey || '未生成' }}</text>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<button class=\"key-btn\" @click=\"generateNewKeys\">生成新密钥</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\n\timport { ref, onMounted } from 'vue'\n\timport { CryptoUtil } from '@/utils/crypto.js'\n\timport { keyManager } from '@/utils/keyGenerator.js'\n\timport CryptoJS from 'crypto-js'\n\t\n\t// 响应式数据\n\tconst testData = ref('Hello, World! 这是一个测试数据 123456')\n\tconst testResults = ref([])\n\tconst rsaKeyValid = ref(false)\n\tconst currentAESKey = ref('')\n\t\n\t// 页面加载时初始化\n\tonMounted(() => {\n\t\tinitializeTest()\n\t})\n\t\n\t// 初始化测试环境\n\tconst initializeTest = () => {\n\t\ttry {\n\t\t\t// 验证RSA公钥\n\t\t\trsaKeyValid.value = CryptoUtil.validateRSAPublicKey(CryptoUtil.getRSAPublicKey())\n\t\t\t\n\t\t\t// 生成AES密钥\n\t\t\tcurrentAESKey.value = CryptoUtil.generateAESKey()\n\t\t\t\n\t\t\taddTestResult('初始化', true, '测试环境初始化成功')\n\t\t} catch (error) {\n\t\t\taddTestResult('初始化', false, '测试环境初始化失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 测试AES加密\n\tconst testAESEncryption = async () => {\n\t\ttry {\n\t\t\tconst data = testData.value || '默认测试数据'\n\t\t\tconst key = CryptoUtil.generateAESKey()\n\n\t\t\t// 加密\n\t\t\tconst encrypted = CryptoUtil.aesEncrypt(data, key)\n\t\t\taddTestResult('AES加密', true,\n\t\t\t\t'数据加密成功（包含IV）',\n\t\t\t\t`加密结果: ${encrypted.substring(0, 50)}...`\n\t\t\t)\n\n\t\t\t// 验证加密结果是Base64格式\n\t\t\tconst isBase64 = CryptoUtil.isBase64(encrypted)\n\t\t\taddTestResult('Base64格式验证', isBase64,\n\t\t\t\tisBase64 ? '加密结果为有效的Base64格式' : '加密结果不是有效的Base64格式'\n\t\t\t)\n\n\t\t\t// 验证IV提取\n\t\t\tconst encryptedBytes = CryptoJS.enc.Base64.parse(encrypted)\n\t\t\tconst hasValidLength = encryptedBytes.sigBytes >= 16\n\t\t\taddTestResult('IV长度验证', hasValidLength,\n\t\t\t\thasValidLength ? `数据长度${encryptedBytes.sigBytes}字节，包含16字节IV` : '数据长度不足，无法包含IV'\n\t\t\t)\n\n\t\t\t// 解密\n\t\t\tconst decrypted = CryptoUtil.aesDecrypt(encrypted, key)\n\t\t\tconst success = decrypted === data\n\n\t\t\taddTestResult('AES解密', success,\n\t\t\t\tsuccess ? '数据解密成功，与原数据一致' : '数据解密失败，与原数据不一致',\n\t\t\t\tsuccess ? `原数据: ${data}` : `解密结果: ${decrypted}`\n\t\t\t)\n\n\t\t\t// 测试IV提取详情\n\t\t\tif (success) {\n\t\t\t\tconst ivHex = encryptedBytes.toString(CryptoJS.enc.Hex).substring(0, 32)\n\t\t\t\taddTestResult('IV提取详情', true,\n\t\t\t\t\t'成功提取IV并解密',\n\t\t\t\t\t`提取的IV (前16字节): ${ivHex}`\n\t\t\t\t)\n\t\t\t}\n\n\t\t} catch (error) {\n\t\t\taddTestResult('AES加密解密', false, '测试失败: ' + error.message)\n\t\t}\n\t}\n\n\t// 测试IV提取功能\n\tconst testIVExtraction = async () => {\n\t\ttry {\n\t\t\tconst testData = 'Hello, World! 这是IV提取测试数据 🔐'\n\t\t\tconst key = CryptoUtil.generateAESKey()\n\n\t\t\taddTestResult('IV提取测试开始', true, `测试数据: ${testData}`)\n\n\t\t\t// 1. 加密数据\n\t\t\tconst encrypted = CryptoUtil.aesEncrypt(testData, key)\n\t\t\taddTestResult('步骤1: 加密', true,\n\t\t\t\t'数据加密完成',\n\t\t\t\t`加密结果长度: ${encrypted.length} 字符`\n\t\t\t)\n\n\t\t\t// 2. 手动解析加密数据\n\t\t\tconst encryptedBytes = CryptoJS.enc.Base64.parse(encrypted)\n\t\t\taddTestResult('步骤2: Base64解析', true,\n\t\t\t\t'成功解析Base64数据',\n\t\t\t\t`总字节数: ${encryptedBytes.sigBytes}`\n\t\t\t)\n\n\t\t\t// 3. 提取IV（前16字节）\n\t\t\tconst ivBytes = CryptoJS.lib.WordArray.create(encryptedBytes.words.slice(0, 4))\n\t\t\tivBytes.sigBytes = 16\n\t\t\tconst ivHex = ivBytes.toString(CryptoJS.enc.Hex)\n\t\t\taddTestResult('步骤3: IV提取', true,\n\t\t\t\t'成功提取IV',\n\t\t\t\t`IV (Hex): ${ivHex}`\n\t\t\t)\n\n\t\t\t// 4. 提取实际加密数据（剩余字节）\n\t\t\tconst actualDataBytes = CryptoJS.lib.WordArray.create(\n\t\t\t\tencryptedBytes.words.slice(4),\n\t\t\t\tencryptedBytes.sigBytes - 16\n\t\t\t)\n\t\t\taddTestResult('步骤4: 数据提取', true,\n\t\t\t\t'成功提取加密数据',\n\t\t\t\t`数据字节数: ${actualDataBytes.sigBytes}`\n\t\t\t)\n\n\t\t\t// 5. 使用提取的IV和数据进行解密\n\t\t\tconst decrypted = CryptoJS.AES.decrypt(\n\t\t\t\t{ ciphertext: actualDataBytes },\n\t\t\t\tCryptoJS.enc.Utf8.parse(key),\n\t\t\t\t{\n\t\t\t\t\tiv: ivBytes,\n\t\t\t\t\tmode: CryptoJS.mode.CBC,\n\t\t\t\t\tpadding: CryptoJS.pad.Pkcs7\n\t\t\t\t}\n\t\t\t)\n\n\t\t\tconst decryptedText = decrypted.toString(CryptoJS.enc.Utf8)\n\t\t\tconst success = decryptedText === testData\n\n\t\t\taddTestResult('步骤5: 手动解密', success,\n\t\t\t\tsuccess ? '手动解密成功！' : '手动解密失败',\n\t\t\t\t`解密结果: ${decryptedText}`\n\t\t\t)\n\n\t\t\t// 6. 使用工具类方法解密（验证一致性）\n\t\t\tconst utilDecrypted = CryptoUtil.aesDecrypt(encrypted, key)\n\t\t\tconst utilSuccess = utilDecrypted === testData\n\n\t\t\taddTestResult('步骤6: 工具类解密', utilSuccess,\n\t\t\t\tutilSuccess ? '工具类解密成功！' : '工具类解密失败',\n\t\t\t\t`解密结果: ${utilDecrypted}`\n\t\t\t)\n\n\t\t\t// 7. 验证两种方法结果一致\n\t\t\tconst consistent = decryptedText === utilDecrypted\n\t\t\taddTestResult('步骤7: 一致性验证', consistent,\n\t\t\t\tconsistent ? '手动解密与工具类解密结果一致' : '两种解密方法结果不一致',\n\t\t\t\tconsistent ? '✅ 测试通过' : '❌ 测试失败'\n\t\t\t)\n\n\t\t} catch (error) {\n\t\t\taddTestResult('IV提取测试', false, '测试失败: ' + error.message)\n\t\t}\n\t}\n\n\t// 测试RSA加密\n\tconst testRSAEncryption = async () => {\n\t\ttry {\n\t\t\tconst data = JSON.stringify({ aesKey: currentAESKey.value })\n\t\t\t\n\t\t\t// RSA加密\n\t\t\tconst encrypted = CryptoUtil.rsaEncrypt(data)\n\t\t\taddTestResult('RSA加密', true, 'RSA加密成功', encrypted.substring(0, 50) + '...')\n\t\t\t\n\t\t} catch (error) {\n\t\t\taddTestResult('RSA加密', false, '测试失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 测试混合加密\n\tconst testHybridEncryption = async () => {\n\t\ttry {\n\t\t\tconst data = {\n\t\t\t\tmessage: testData.value || '默认测试数据',\n\t\t\t\ttimestamp: Date.now(),\n\t\t\t\tuserId: 'test-user-123'\n\t\t\t}\n\t\t\t\n\t\t\t// 混合加密\n\t\t\tconst encryptedRequest = CryptoUtil.encryptRequest(data)\n\t\t\t\n\t\t\taddTestResult('混合加密', true, '混合加密成功', \n\t\t\t\t`加密数据长度: ${encryptedRequest.encryptedData.length}, 密钥长度: ${encryptedRequest.encryptedKey.length}`\n\t\t\t)\n\t\t\t\n\t\t\t// 模拟解密响应\n\t\t\tconst mockResponse = JSON.stringify({\n\t\t\t\tsuccess: true,\n\t\t\t\tmessage: '这是服务器返回的加密响应',\n\t\t\t\tdata: { result: 'test-result' }\n\t\t\t})\n\t\t\t\n\t\t\tconst encryptedResponse = CryptoUtil.aesEncrypt(mockResponse, encryptedRequest.aesKey)\n\t\t\tconst decryptedResponse = CryptoUtil.decryptResponse(encryptedResponse, encryptedRequest.aesKey)\n\t\t\t\n\t\t\taddTestResult('响应解密', true, '响应解密成功', JSON.stringify(decryptedResponse))\n\t\t\t\n\t\t} catch (error) {\n\t\t\taddTestResult('混合加密', false, '测试失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 测试完整流程\n\tconst testFullProcess = async () => {\n\t\ttry {\n\t\t\t// 运行完整的加密测试\n\t\t\tconst success = CryptoUtil.testEncryption()\n\t\t\taddTestResult('完整流程测试', success, \n\t\t\t\tsuccess ? '所有加密功能测试通过' : '加密功能测试失败'\n\t\t\t)\n\t\t} catch (error) {\n\t\t\taddTestResult('完整流程测试', false, '测试失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 生成测试数据\n\tconst generateTestData = () => {\n\t\tconst samples = [\n\t\t\t'Hello, World! 测试数据',\n\t\t\t'{\"name\":\"张三\",\"age\":25,\"city\":\"北京\"}',\n\t\t\t'这是一个包含中文的测试字符串，用于验证加密功能是否正常工作。',\n\t\t\t'1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',\n\t\t\t'特殊字符测试: !@#$%^&*()_+-=[]{}|;:,.<>?'\n\t\t]\n\t\t\n\t\ttestData.value = samples[Math.floor(Math.random() * samples.length)]\n\t}\n\t\n\t// 清空测试数据\n\tconst clearTestData = () => {\n\t\ttestData.value = ''\n\t}\n\t\n\t// 生成新密钥\n\tconst generateNewKeys = async () => {\n\t\ttry {\n\t\t\t// 生成新的AES密钥\n\t\t\tcurrentAESKey.value = CryptoUtil.generateAESKey()\n\t\t\t\n\t\t\t// 可以选择生成新的RSA密钥对（需要时间较长）\n\t\t\t// const keyPair = await keyManager.generateKeyPair()\n\t\t\t// CryptoUtil.setRSAPublicKey(keyPair.publicKey)\n\t\t\t// rsaKeyValid.value = true\n\t\t\t\n\t\t\taddTestResult('密钥生成', true, '新密钥生成成功')\n\t\t} catch (error) {\n\t\t\taddTestResult('密钥生成', false, '密钥生成失败: ' + error.message)\n\t\t}\n\t}\n\t\n\t// 添加测试结果\n\tconst addTestResult = (title, success, message, data = null) => {\n\t\ttestResults.value.unshift({\n\t\t\ttitle,\n\t\t\tsuccess,\n\t\t\tmessage,\n\t\t\tdata,\n\t\t\ttimestamp: new Date().toLocaleTimeString()\n\t\t})\n\t\t\n\t\t// 限制结果数量\n\t\tif (testResults.value.length > 10) {\n\t\t\ttestResults.value = testResults.value.slice(0, 10)\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tpadding: 30rpx;\n\t\tbackground-color: #f5f5f5;\n\t\tmin-height: 100vh;\n\t}\n\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 40rpx;\n\t}\n\n\t.title {\n\t\tfont-size: 40rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.content {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 30rpx;\n\t}\n\n\t/* 通用区域样式 */\n\t.test-section,\n\t.input-section,\n\t.result-section,\n\t.key-section {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.section-title {\n\t\tmargin-bottom: 20rpx;\n\t\tborder-bottom: 2rpx solid #e0e0e0;\n\t\tpadding-bottom: 15rpx;\n\t}\n\n\t.title-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t/* 测试控制区域 */\n\t.test-controls {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tgap: 15rpx;\n\t}\n\n\t.test-btn {\n\t\tflex: 1;\n\t\tmin-width: 200rpx;\n\t\theight: 70rpx;\n\t\tbackground-color: #4CAF50;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t}\n\n\t/* 输入区域 */\n\t.test-input {\n\t\twidth: 100%;\n\t\tmin-height: 150rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t\tfont-size: 28rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-sizing: border-box;\n\t}\n\n\t.input-controls {\n\t\tdisplay: flex;\n\t\tgap: 15rpx;\n\t}\n\n\t.control-btn {\n\t\tflex: 1;\n\t\theight: 60rpx;\n\t\tbackground-color: #2196F3;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 26rpx;\n\t}\n\n\t/* 结果区域 */\n\t.result-item {\n\t\tborder: 1rpx solid #e0e0e0;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t\tmargin-bottom: 15rpx;\n\t}\n\n\t.result-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.result-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.result-status {\n\t\tfont-size: 24rpx;\n\t\tpadding: 5rpx 15rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\n\t.result-status.success {\n\t\tbackground-color: #e8f5e8;\n\t\tcolor: #4CAF50;\n\t}\n\n\t.result-status.error {\n\t\tbackground-color: #ffeaea;\n\t\tcolor: #f44336;\n\t}\n\n\t.result-content {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666666;\n\t\tline-height: 1.4;\n\t}\n\n\t.result-data {\n\t\tmargin-top: 10rpx;\n\t\tpadding: 15rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 6rpx;\n\t}\n\n\t.data-label {\n\t\tfont-weight: bold;\n\t\tcolor: #333333;\n\t}\n\n\t.data-content {\n\t\tdisplay: block;\n\t\tmargin-top: 8rpx;\n\t\tword-break: break-all;\n\t\tfont-family: monospace;\n\t}\n\n\t/* 密钥区域 */\n\t.key-item {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 15rpx;\n\t\tpadding: 15rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 8rpx;\n\t}\n\n\t.key-label {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.key-status.valid {\n\t\tcolor: #4CAF50;\n\t\tfont-weight: bold;\n\t}\n\n\t.key-status.invalid {\n\t\tcolor: #f44336;\n\t\tfont-weight: bold;\n\t}\n\n\t.key-value {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t\tfont-family: monospace;\n\t\tmax-width: 300rpx;\n\t\toverflow: hidden;\n\t\ttext-overflow: ellipsis;\n\t}\n\n\t.key-btn {\n\t\twidth: 100%;\n\t\theight: 70rpx;\n\t\tbackground-color: #FF9800;\n\t\tcolor: #ffffff;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 28rpx;\n\t\tmargin-top: 20rpx;\n\t}\n</style>\n", "import MiniProgramPage from 'E:/FM93交通之声/2025Project/BankCodeSearch/pages/test/crypto.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "CryptoUtil", "CryptoJS", "testData", "MiniProgramPage"], "mappings": ";;;;;;AA+FC,UAAM,WAAWA,cAAG,IAAC,+BAA+B;AACpD,UAAM,cAAcA,cAAG,IAAC,EAAE;AAC1B,UAAM,cAAcA,cAAG,IAAC,KAAK;AAC7B,UAAM,gBAAgBA,cAAG,IAAC,EAAE;AAG5BC,kBAAAA,UAAU,MAAM;AACf,qBAAgB;AAAA,IAClB,CAAE;AAGD,UAAM,iBAAiB,MAAM;AAC5B,UAAI;AAEH,oBAAY,QAAQC,aAAU,WAAC,qBAAqBA,aAAU,WAAC,gBAAe,CAAE;AAGhF,sBAAc,QAAQA,aAAU,WAAC,eAAgB;AAEjD,sBAAc,OAAO,MAAM,WAAW;AAAA,MACtC,SAAQ,OAAO;AACf,sBAAc,OAAO,OAAO,gBAAgB,MAAM,OAAO;AAAA,MACzD;AAAA,IACD;AAGD,UAAM,oBAAoB,YAAY;AACrC,UAAI;AACH,cAAM,OAAO,SAAS,SAAS;AAC/B,cAAM,MAAMA,aAAU,WAAC,eAAgB;AAGvC,cAAM,YAAYA,aAAU,WAAC,WAAW,MAAM,GAAG;AACjD;AAAA,UAAc;AAAA,UAAS;AAAA,UACtB;AAAA,UACA,SAAS,UAAU,UAAU,GAAG,EAAE,CAAC;AAAA,QACnC;AAGD,cAAM,WAAWA,aAAAA,WAAW,SAAS,SAAS;AAC9C;AAAA,UAAc;AAAA,UAAc;AAAA,UAC3B,WAAW,qBAAqB;AAAA,QAChC;AAGD,cAAM,iBAAiBC,cAAAA,SAAS,IAAI,OAAO,MAAM,SAAS;AAC1D,cAAM,iBAAiB,eAAe,YAAY;AAClD;AAAA,UAAc;AAAA,UAAU;AAAA,UACvB,iBAAiB,OAAO,eAAe,QAAQ,gBAAgB;AAAA,QAC/D;AAGD,cAAM,YAAYD,aAAU,WAAC,WAAW,WAAW,GAAG;AACtD,cAAM,UAAU,cAAc;AAE9B;AAAA,UAAc;AAAA,UAAS;AAAA,UACtB,UAAU,kBAAkB;AAAA,UAC5B,UAAU,QAAQ,IAAI,KAAK,SAAS,SAAS;AAAA,QAC7C;AAGD,YAAI,SAAS;AACZ,gBAAM,QAAQ,eAAe,SAASC,cAAQ,SAAC,IAAI,GAAG,EAAE,UAAU,GAAG,EAAE;AACvE;AAAA,YAAc;AAAA,YAAU;AAAA,YACvB;AAAA,YACA,kBAAkB,KAAK;AAAA,UACvB;AAAA,QACD;AAAA,MAED,SAAQ,OAAO;AACf,sBAAc,WAAW,OAAO,WAAW,MAAM,OAAO;AAAA,MACxD;AAAA,IACD;AAGD,UAAM,mBAAmB,YAAY;AACpC,UAAI;AACH,cAAMC,YAAW;AACjB,cAAM,MAAMF,aAAU,WAAC,eAAgB;AAEvC,sBAAc,YAAY,MAAM,SAASE,SAAQ,EAAE;AAGnD,cAAM,YAAYF,aAAU,WAAC,WAAWE,WAAU,GAAG;AACrD;AAAA,UAAc;AAAA,UAAW;AAAA,UACxB;AAAA,UACA,WAAW,UAAU,MAAM;AAAA,QAC3B;AAGD,cAAM,iBAAiBD,cAAAA,SAAS,IAAI,OAAO,MAAM,SAAS;AAC1D;AAAA,UAAc;AAAA,UAAiB;AAAA,UAC9B;AAAA,UACA,SAAS,eAAe,QAAQ;AAAA,QAChC;AAGD,cAAM,UAAUA,cAAAA,SAAS,IAAI,UAAU,OAAO,eAAe,MAAM,MAAM,GAAG,CAAC,CAAC;AAC9E,gBAAQ,WAAW;AACnB,cAAM,QAAQ,QAAQ,SAASA,cAAAA,SAAS,IAAI,GAAG;AAC/C;AAAA,UAAc;AAAA,UAAa;AAAA,UAC1B;AAAA,UACA,aAAa,KAAK;AAAA,QAClB;AAGD,cAAM,kBAAkBA,cAAAA,SAAS,IAAI,UAAU;AAAA,UAC9C,eAAe,MAAM,MAAM,CAAC;AAAA,UAC5B,eAAe,WAAW;AAAA,QAC1B;AACD;AAAA,UAAc;AAAA,UAAa;AAAA,UAC1B;AAAA,UACA,UAAU,gBAAgB,QAAQ;AAAA,QAClC;AAGD,cAAM,YAAYA,uBAAS,IAAI;AAAA,UAC9B,EAAE,YAAY,gBAAiB;AAAA,UAC/BA,cAAAA,SAAS,IAAI,KAAK,MAAM,GAAG;AAAA,UAC3B;AAAA,YACC,IAAI;AAAA,YACJ,MAAMA,cAAAA,SAAS,KAAK;AAAA,YACpB,SAASA,cAAAA,SAAS,IAAI;AAAA,UACtB;AAAA,QACD;AAED,cAAM,gBAAgB,UAAU,SAASA,cAAAA,SAAS,IAAI,IAAI;AAC1D,cAAM,UAAU,kBAAkBC;AAElC;AAAA,UAAc;AAAA,UAAa;AAAA,UAC1B,UAAU,YAAY;AAAA,UACtB,SAAS,aAAa;AAAA,QACtB;AAGD,cAAM,gBAAgBF,aAAU,WAAC,WAAW,WAAW,GAAG;AAC1D,cAAM,cAAc,kBAAkBE;AAEtC;AAAA,UAAc;AAAA,UAAc;AAAA,UAC3B,cAAc,aAAa;AAAA,UAC3B,SAAS,aAAa;AAAA,QACtB;AAGD,cAAM,aAAa,kBAAkB;AACrC;AAAA,UAAc;AAAA,UAAc;AAAA,UAC3B,aAAa,mBAAmB;AAAA,UAChC,aAAa,WAAW;AAAA,QACxB;AAAA,MAED,SAAQ,OAAO;AACf,sBAAc,UAAU,OAAO,WAAW,MAAM,OAAO;AAAA,MACvD;AAAA,IACD;AAGD,UAAM,oBAAoB,YAAY;AACrC,UAAI;AACH,cAAM,OAAO,KAAK,UAAU,EAAE,QAAQ,cAAc,OAAO;AAG3D,cAAM,YAAYF,aAAAA,WAAW,WAAW,IAAI;AAC5C,sBAAc,SAAS,MAAM,WAAW,UAAU,UAAU,GAAG,EAAE,IAAI,KAAK;AAAA,MAE1E,SAAQ,OAAO;AACf,sBAAc,SAAS,OAAO,WAAW,MAAM,OAAO;AAAA,MACtD;AAAA,IACD;AAGD,UAAM,uBAAuB,YAAY;AACxC,UAAI;AACH,cAAM,OAAO;AAAA,UACZ,SAAS,SAAS,SAAS;AAAA,UAC3B,WAAW,KAAK,IAAK;AAAA,UACrB,QAAQ;AAAA,QACR;AAGD,cAAM,mBAAmBA,aAAAA,WAAW,eAAe,IAAI;AAEvD;AAAA,UAAc;AAAA,UAAQ;AAAA,UAAM;AAAA,UAC3B,WAAW,iBAAiB,cAAc,MAAM,WAAW,iBAAiB,aAAa,MAAM;AAAA,QAC/F;AAGD,cAAM,eAAe,KAAK,UAAU;AAAA,UACnC,SAAS;AAAA,UACT,SAAS;AAAA,UACT,MAAM,EAAE,QAAQ,cAAe;AAAA,QACnC,CAAI;AAED,cAAM,oBAAoBA,aAAAA,WAAW,WAAW,cAAc,iBAAiB,MAAM;AACrF,cAAM,oBAAoBA,aAAAA,WAAW,gBAAgB,mBAAmB,iBAAiB,MAAM;AAE/F,sBAAc,QAAQ,MAAM,UAAU,KAAK,UAAU,iBAAiB,CAAC;AAAA,MAEvE,SAAQ,OAAO;AACf,sBAAc,QAAQ,OAAO,WAAW,MAAM,OAAO;AAAA,MACrD;AAAA,IACD;AAGD,UAAM,kBAAkB,YAAY;AACnC,UAAI;AAEH,cAAM,UAAUA,aAAU,WAAC,eAAgB;AAC3C;AAAA,UAAc;AAAA,UAAU;AAAA,UACvB,UAAU,eAAe;AAAA,QACzB;AAAA,MACD,SAAQ,OAAO;AACf,sBAAc,UAAU,OAAO,WAAW,MAAM,OAAO;AAAA,MACvD;AAAA,IACD;AAGD,UAAM,mBAAmB,MAAM;AAC9B,YAAM,UAAU;AAAA,QACf;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACA;AAED,eAAS,QAAQ,QAAQ,KAAK,MAAM,KAAK,OAAQ,IAAG,QAAQ,MAAM,CAAC;AAAA,IACnE;AAGD,UAAM,gBAAgB,MAAM;AAC3B,eAAS,QAAQ;AAAA,IACjB;AAGD,UAAM,kBAAkB,YAAY;AACnC,UAAI;AAEH,sBAAc,QAAQA,aAAU,WAAC,eAAgB;AAOjD,sBAAc,QAAQ,MAAM,SAAS;AAAA,MACrC,SAAQ,OAAO;AACf,sBAAc,QAAQ,OAAO,aAAa,MAAM,OAAO;AAAA,MACvD;AAAA,IACD;AAGD,UAAM,gBAAgB,CAAC,OAAO,SAAS,SAAS,OAAO,SAAS;AAC/D,kBAAY,MAAM,QAAQ;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAW,oBAAI,KAAM,GAAC,mBAAoB;AAAA,MAC7C,CAAG;AAGD,UAAI,YAAY,MAAM,SAAS,IAAI;AAClC,oBAAY,QAAQ,YAAY,MAAM,MAAM,GAAG,EAAE;AAAA,MACjD;AAAA,IACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtWF,GAAG,WAAWG,SAAe;"}