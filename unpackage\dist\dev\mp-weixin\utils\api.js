"use strict";
const common_vendor = require("../common/vendor.js");
const types_bank = require("../types/bank.js");
const utils_crypto = require("./crypto.js");
const config_api = require("../config/api.js");
class BankCodeAPI {
  /**
   * 搜索银行代码
   * @param {string} keyword - 搜索关键字（银行名称、地区、联行号等）
   * @returns {Promise<Array>} 查询结果数组
   */
  static async searchBankCode(keyword) {
    if (!config_api.ApiUtils.validateSearchParams({ keyword })) {
      throw new Error("搜索关键字不能为空");
    }
    const searchType = types_bank.detectSearchType(keyword);
    common_vendor.index.__f__("log", "at utils/api.js:24", `搜索类型: ${searchType}, 关键字: ${keyword}`);
    const requestData = {
      openId: common_vendor.index.getStorageSync("user_openid"),
      query: keyword
    };
    return await this.requestWithFallback(requestData, keyword, searchType);
  }
  /**
   * 带降级的API请求
   * @param {Object} requestData - 请求参数
   * @param {string} keyword - 搜索关键字
   * @param {string} searchType - 搜索类型
   * @returns {Promise<Array>} 查询结果
   */
  static async requestWithFallback(requestData, keyword, searchType) {
    let lastError = null;
    for (let attempt = 0; attempt < 3; attempt++) {
      try {
        common_vendor.index.__f__("log", "at utils/api.js:50", `银行查询尝试 ${attempt + 1}/${config_api.RETRY_CONFIG.getRetryDelay.length}`);
        const encryptedRequest = utils_crypto.crypto.encryptRequest(requestData);
        common_vendor.index.__f__("log", "at utils/api.js:54", "银行查询请求已加密");
        const response = await common_vendor.index.request({
          url: config_api.getApiUrl("BANK_SEARCH"),
          method: "POST",
          data: {
            parameter: encryptedRequest.encryptedData
          },
          header: {
            "Content-Type": "application/json"
          },
          timeout: 1e4
        });
        common_vendor.index.__f__("log", "at utils/api.js:69", "银行查询响应:", response);
        const results = config_api.ResponseHandler.handleBankSearchResponse(response);
        if (response.statusCode == 200) {
          if (response.data.code == 200) {
            const decryptedData = utils_crypto.crypto.decryptResponse(response.data.data, encryptedRequest.aesKey);
            common_vendor.index.__f__("log", "at utils/api.js:78", "银行查询解密成功:", decryptedData);
            const formattedResults = Array.isArray(decryptedData.bankInfoList) ? decryptedData.bankInfoList.map((item) => config_api.ApiUtils.formatBankInfo(item)) : [config_api.ApiUtils.formatBankInfo(decryptedData.bankInfoList)];
            common_vendor.index.__f__("log", "at utils/api.js:84", "银行查询结果已格式化:", formattedResults);
            return formattedResults.filter((item) => item.code);
          } else {
            common_vendor.index.showToast({
              title: response.data.msg,
              icon: "none",
              duration: 2e3
            });
            return [];
          }
        }
        return results;
      } catch (error) {
        lastError = error;
        common_vendor.index.__f__("error", "at utils/api.js:101", `银行查询尝试 ${attempt + 1} 失败:`, error);
        if (!config_api.RETRY_CONFIG.shouldRetry(error, attempt)) {
          break;
        }
        if (attempt < 2) {
          const delay = config_api.RETRY_CONFIG.getRetryDelay(attempt);
          common_vendor.index.__f__("log", "at utils/api.js:111", `等待 ${delay}ms 后重试...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }
    common_vendor.index.__f__("log", "at utils/api.js:118", "API请求失败，使用模拟数据:", lastError == null ? void 0 : lastError.message);
    return this.getMockData(keyword, searchType);
  }
  /**
   * 获取模拟数据（用于演示）
   * @param {string} keyword - 搜索关键字
   * @param {string} searchType - 搜索类型
   * @returns {Array} 模拟的查询结果
   */
  static getMockData(keyword, searchType = "keyword") {
    const allMockData = [
      {
        code: "************",
        bankName: "中国工商银行股份有限公司北京国家文化与金融合作示范区支行",
        branchName: "营业部：中国工商银行股份有限公司北京王府井支行",
        distance: "100",
        address: "北京市东城区王府井大街",
        phone: "010-********"
      },
      {
        code: "************",
        bankName: "中国工商银行股份有限公司北京分行",
        branchName: "营业部：中国工商银行股份有限公司北京分行营业部",
        distance: "200",
        address: "北京市西城区复兴门内大街",
        phone: "010-********"
      },
      {
        code: "************",
        bankName: "中国农业银行股份有限公司北京分行",
        branchName: "营业部：中国农业银行股份有限公司北京分行营业部",
        distance: "150",
        address: "北京市东城区建国门内大街",
        phone: "010-********"
      },
      {
        code: "************",
        bankName: "中国银行股份有限公司北京分行",
        branchName: "营业部：中国银行股份有限公司北京分行营业部",
        distance: "180",
        address: "北京市西城区复兴门外大街",
        phone: "010-********"
      },
      {
        code: "************",
        bankName: "中国建设银行股份有限公司北京分行",
        branchName: "营业部：中国建设银行股份有限公司北京分行营业部",
        distance: "120",
        address: "北京市西城区金融大街",
        phone: "010-********"
      }
    ];
    const keyword_lower = keyword.toLowerCase();
    return allMockData.filter((item) => {
      if (searchType === types_bank.SEARCH_TYPES.BANK_CODE) {
        return item.code === keyword || item.code.includes(keyword);
      }
      if (searchType === types_bank.SEARCH_TYPES.BANK_NAME) {
        return item.bankName.includes(keyword) || item.branchName.includes(keyword) || keyword_lower.includes("工行") && item.bankName.includes("工商银行") || keyword_lower.includes("农行") && item.bankName.includes("农业银行") || keyword_lower.includes("中行") && item.bankName.includes("中国银行") || keyword_lower.includes("建行") && item.bankName.includes("建设银行");
      }
      if (searchType === types_bank.SEARCH_TYPES.REGION) {
        return item.address && item.address.includes(keyword);
      }
      return item.bankName.includes(keyword) || item.branchName.includes(keyword) || item.code.includes(keyword) || item.address && item.address.includes(keyword) || keyword_lower.includes("工行") && item.bankName.includes("工商银行") || keyword_lower.includes("农行") && item.bankName.includes("农业银行") || keyword_lower.includes("中行") && item.bankName.includes("中国银行") || keyword_lower.includes("建行") && item.bankName.includes("建设银行") || keyword_lower.includes("北京") && item.address && item.address.includes("北京");
    });
  }
  /**
   * 根据联行号获取详细信息
   * @param {string} bankCode - 联行号
   * @returns {Promise<Object>} 银行详细信息
   */
  static async getBankDetail(bankCode) {
    try {
      if (!bankCode || typeof bankCode !== "string" || bankCode.length !== 12) {
        throw new Error("联行号格式错误");
      }
      const requestData = {
        bankCode,
        timestamp: Date.now()
      };
      const encryptedRequest = utils_crypto.crypto.encryptRequest(requestData);
      common_vendor.index.__f__("log", "at utils/api.js:229", "银行详情查询请求已加密");
      const response = await common_vendor.index.request({
        url: config_api.getApiUrl("BANK_DETAIL"),
        method: "POST",
        data: {
          parameter: encryptedRequest.encryptedData
        },
        header: {
          "Content-Type": "application/json"
        },
        timeout: 1e4
      });
      common_vendor.index.__f__("log", "at utils/api.js:244", "银行详情查询响应:", response);
      if (response.statusCode === 200 && response.data.code == 200) {
        const decryptedData = utils_crypto.crypto.decryptResponse(response.data.data, encryptedRequest.aesKey);
        common_vendor.index.__f__("log", "at utils/api.js:250", "银行详情解密成功:", decryptedData);
        return config_api.ApiUtils.formatBankInfo(decryptedData);
      }
      common_vendor.index.__f__("log", "at utils/api.js:257", "API请求失败，使用模拟数据");
      const mockData = this.getMockData(bankCode, types_bank.SEARCH_TYPES.BANK_CODE);
      return mockData.length > 0 ? mockData[0] : null;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/api.js:262", "获取银行详情失败:", error);
      if (error.errMsg && error.errMsg.includes("request:fail")) {
        common_vendor.index.__f__("log", "at utils/api.js:266", "网络请求失败，使用模拟数据");
        const mockData = this.getMockData(bankCode, types_bank.SEARCH_TYPES.BANK_CODE);
        return mockData.length > 0 ? mockData[0] : null;
      }
      throw new Error("获取详情失败: " + error.message);
    }
  }
}
exports.BankCodeAPI = BankCodeAPI;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/api.js.map
