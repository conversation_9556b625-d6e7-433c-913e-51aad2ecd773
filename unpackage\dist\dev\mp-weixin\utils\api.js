"use strict";
const common_vendor = require("../common/vendor.js");
const types_bank = require("../types/bank.js");
class BankCodeAPI {
  /**
   * 搜索银行代码
   * @param {string} keyword - 搜索关键字（银行名称、地区、联行号等）
   * @returns {Promise<Array>} 查询结果数组
   */
  static async searchBankCode(keyword) {
    try {
      const searchType = types_bank.detectSearchType(keyword);
      common_vendor.index.__f__("log", "at utils/api.js:20", `搜索类型: ${searchType}, 关键字: ${keyword}`);
      return new Promise((resolve) => {
        setTimeout(() => {
          const mockData = this.getMockData(keyword, searchType);
          resolve(mockData);
        }, 800);
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/api.js:46", "银行代码查询失败:", error);
      throw new Error("查询失败，请检查网络连接");
    }
  }
  /**
   * 获取模拟数据（用于演示）
   * @param {string} keyword - 搜索关键字
   * @param {string} searchType - 搜索类型
   * @returns {Array} 模拟的查询结果
   */
  static getMockData(keyword, searchType = "keyword") {
    const allMockData = [
      {
        code: "************",
        bankName: "中国工商银行股份有限公司北京国家文化与金融合作示范区支行",
        branchName: "营业部：中国工商银行股份有限公司北京王府井支行",
        distance: "100",
        address: "北京市东城区王府井大街",
        phone: "010-********"
      },
      {
        code: "************",
        bankName: "中国工商银行股份有限公司北京分行",
        branchName: "营业部：中国工商银行股份有限公司北京分行营业部",
        distance: "200",
        address: "北京市西城区复兴门内大街",
        phone: "010-********"
      },
      {
        code: "************",
        bankName: "中国农业银行股份有限公司北京分行",
        branchName: "营业部：中国农业银行股份有限公司北京分行营业部",
        distance: "150",
        address: "北京市东城区建国门内大街",
        phone: "010-********"
      },
      {
        code: "************",
        bankName: "中国银行股份有限公司北京分行",
        branchName: "营业部：中国银行股份有限公司北京分行营业部",
        distance: "180",
        address: "北京市西城区复兴门外大街",
        phone: "010-********"
      },
      {
        code: "************",
        bankName: "中国建设银行股份有限公司北京分行",
        branchName: "营业部：中国建设银行股份有限公司北京分行营业部",
        distance: "120",
        address: "北京市西城区金融大街",
        phone: "010-********"
      }
    ];
    const keyword_lower = keyword.toLowerCase();
    return allMockData.filter((item) => {
      if (searchType === types_bank.SEARCH_TYPES.BANK_CODE) {
        return item.code === keyword || item.code.includes(keyword);
      }
      if (searchType === types_bank.SEARCH_TYPES.BANK_NAME) {
        return item.bankName.includes(keyword) || item.branchName.includes(keyword) || keyword_lower.includes("工行") && item.bankName.includes("工商银行") || keyword_lower.includes("农行") && item.bankName.includes("农业银行") || keyword_lower.includes("中行") && item.bankName.includes("中国银行") || keyword_lower.includes("建行") && item.bankName.includes("建设银行");
      }
      if (searchType === types_bank.SEARCH_TYPES.REGION) {
        return item.address && item.address.includes(keyword);
      }
      return item.bankName.includes(keyword) || item.branchName.includes(keyword) || item.code.includes(keyword) || item.address && item.address.includes(keyword) || keyword_lower.includes("工行") && item.bankName.includes("工商银行") || keyword_lower.includes("农行") && item.bankName.includes("农业银行") || keyword_lower.includes("中行") && item.bankName.includes("中国银行") || keyword_lower.includes("建行") && item.bankName.includes("建设银行") || keyword_lower.includes("北京") && item.address && item.address.includes("北京");
    });
  }
  /**
   * 根据联行号获取详细信息
   * @param {string} bankCode - 联行号
   * @returns {Promise<Object>} 银行详细信息
   */
  static async getBankDetail(bankCode) {
    try {
      const mockData = this.getMockData(bankCode, types_bank.SEARCH_TYPES.BANK_CODE);
      return mockData.length > 0 ? mockData[0] : null;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/api.js:160", "获取银行详情失败:", error);
      throw new Error("获取详情失败");
    }
  }
}
exports.BankCodeAPI = BankCodeAPI;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/api.js.map
