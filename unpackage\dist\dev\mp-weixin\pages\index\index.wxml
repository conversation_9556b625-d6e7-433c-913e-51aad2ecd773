<view class="container"><view class="content"><view class="qr-section"><view class="qr-code"><image class="qr-image" src="{{a}}" mode="aspectFit"></image></view><view class="website-info"><text class="website-title">AI联行助手</text><text class="website-url">依托AI大模型能力，对各大银行主页公开的联行号提供查询，精确到支行点</text><text class="website-url"></text></view></view><view class="search-section"><view class="search-input-wrapper"><input class="search-input" placeholder="工行北京王府井支行" placeholder-style="color: #999;" value="{{b}}" bindinput="{{c}}"/></view><text class="search-tip">请输入银行/地区名/关键字，例如：工行北京</text><text class="search-tip">也可以输入12位联行号，例如：102100000072</text></view><view class="button-section"><button disabled="{{e}}" class="{{['action-button', 'search-btn', f && 'disabled']}}" bindtap="{{g}}">{{d}}</button><button disabled="{{h}}" class="{{['action-button', 'paste-btn', i && 'disabled']}}" bindtap="{{j}}"> 粘贴 </button><button class="action-button clear-btn" bindtap="{{k}}"> 清除 </button></view><view wx:if="{{l}}" class="result-section"><view class="result-header"><text class="result-title">查询结果</text></view><view class="result-list"><view wx:for="{{m}}" wx:for-item="item" wx:key="f" class="result-item" bindtap="{{item.g}}"><view class="result-code">{{item.a}}</view><view class="result-info"><text class="bank-name">{{item.b}}</text><text class="branch-name">{{item.c}}</text></view><view wx:if="{{item.d}}" class="result-distance"><text class="distance-text">距离{{item.e}}</text></view></view></view></view><view wx:if="{{n}}" class="loading"><text class="loading-text">查询中...</text></view><view wx:if="{{o}}" class="no-result"><text class="no-result-text">未找到相关结果</text></view></view></view>