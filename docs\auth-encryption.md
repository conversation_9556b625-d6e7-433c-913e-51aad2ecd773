# 登录验证与混合加密功能说明

本项目已实现完整的微信小程序登录验证和混合加密通信机制。

## 功能概述

### 🔐 登录验证流程

1. **自动检查登录状态**: 页面加载时自动检查本地存储的登录信息
2. **微信授权登录**: 调用`uni.login()`获取code
3. **后台验证**: 将code发送到后台获取openid
4. **本地存储**: 保存用户信息到本地存储
5. **状态管理**: 使用Vue 3 Composition API管理登录状态

### 🔒 混合加密机制

1. **RSA + AES混合加密**: RSA加密AES密钥，AES加密实际数据
2. **请求加密**: 所有敏感请求都使用混合加密
3. **响应解密**: 自动解密服务器返回的加密数据
4. **密钥管理**: 动态生成AES密钥，确保每次请求的安全性

## 技术实现

### 登录验证组合式函数

```javascript
// composables/useAuth.js
export function useAuth() {
  const isLoggedIn = ref(false)
  const userInfo = ref(null)
  const openid = ref('')
  
  // 检查登录状态
  const checkLoginStatus = () => {
    const storedOpenid = uni.getStorageSync('user_openid')
    if (storedOpenid) {
      isLoggedIn.value = true
      openid.value = storedOpenid
      return true
    }
    return false
  }
  
  // 微信登录
  const wxLogin = async () => {
    const loginResult = await uni.login({ provider: 'weixin' })
    const openidResult = await getOpenidFromServer(loginResult.code)
    // 保存登录信息...
  }
  
  return { isLoggedIn, wxLogin, checkLoginStatus, ... }
}
```

### 混合加密工具类

```javascript
// utils/crypto.js
export class CryptoUtil {
  // 生成AES密钥
  static generateAESKey() {
    // 生成32位随机字符串
  }
  
  // 混合加密请求
  static encryptRequest(params) {
    const aesKey = this.generateAESKey()
    const encryptedParams = this.aesEncrypt(JSON.stringify(params), aesKey)
    const encryptedKey = this.rsaEncrypt(JSON.stringify({ aesKey }))
    
    return {
      encryptedData: encryptedParams,
      encryptedKey: encryptedKey
    }
  }
}
```

### 加密HTTP请求

```javascript
// utils/request.js
export class EncryptedRequest {
  static async post(url, data, options = {}) {
    // 加密请求数据
    const encryptedRequest = crypto.encryptRequest(data)
    
    // 发送请求
    const response = await uni.request({
      url: `${API_BASE_URL}${url}`,
      method: 'POST',
      data: encryptedRequest
    })
    
    // 解密响应
    return crypto.decryptResponse(response.data.encryptedData, aesKey)
  }
}
```

## 使用方式

### 在页面中使用

```vue
<script setup>
import { useAuth } from '@/composables/useAuth.js'
import { http } from '@/utils/request.js'

const { isLoggedIn, ensureLogin } = useAuth()

// 确保登录后执行操作
const handleSearch = async () => {
  const loginSuccess = await ensureLogin()
  if (!loginSuccess) {
    uni.showToast({ title: '请先登录', icon: 'none' })
    return
  }
  
  // 发送加密请求
  const result = await http.post('/bank/search', {
    keyword: searchKeyword.value,
    timestamp: Date.now()
  })
}
</script>

<template>
  <!-- 登录弹窗 -->
  <view class="login-modal" v-if="showLoginModal">
    <view class="login-content">
      <button @click="handleLogin">微信授权登录</button>
    </view>
  </view>
  
  <!-- 用户信息显示 -->
  <view class="user-info" v-if="isLoggedIn">
    <text>欢迎，{{ userInfo.nickname }}</text>
  </view>
</template>
```

## 安全特性

### 1. 登录安全

- **微信官方授权**: 使用微信官方登录API
- **服务器验证**: code在服务器端验证，获取真实openid
- **本地存储加密**: 敏感信息加密存储
- **自动过期**: 登录状态自动过期机制

### 2. 通信安全

- **混合加密**: RSA + AES双重加密
- **动态密钥**: 每次请求生成新的AES密钥
- **防重放攻击**: 请求包含时间戳
- **数据完整性**: 加密确保数据不被篡改

### 3. 密钥管理

```javascript
// 加密流程
const requestData = { keyword: "工商银行", timestamp: Date.now() }

// 1. 生成AES密钥
const aesKey = generateAESKey() // "Kj8n2Lm9Qp3Rt6Uw1Zx4Cv7Bn0Mp5Hs"

// 2. AES加密请求数据
const encryptedData = aesEncrypt(JSON.stringify(requestData), aesKey)

// 3. RSA加密AES密钥
const keyObject = { aesKey: aesKey }
const encryptedKey = rsaEncrypt(JSON.stringify(keyObject))

// 4. 发送加密请求
const request = {
  encryptedData: encryptedData,
  encryptedKey: encryptedKey
}
```

## 配置说明

### 1. 小程序配置

```json
// manifest.json
{
  "mp-weixin": {
    "appid": "wxa63dc46f6edb0c17",
    "setting": {
      "urlCheck": false,
      "minified": true
    }
  }
}
```

### 2. API配置

```javascript
// utils/request.js
const API_CONFIG = {
  baseURL: 'https://your-api-domain.com/api',
  timeout: 10000
}
```

### 3. RSA公钥配置

```javascript
// utils/crypto.js
static RSA_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----`
```

## 后台接口规范

### 登录接口

```
POST /api/auth/login
Content-Type: application/json

Request:
{
  "encryptedData": "AES_encrypted_data",
  "encryptedKey": "RSA_encrypted_aes_key"
}

Response:
{
  "encryptedData": "AES_encrypted_response"
}

Decrypted Response:
{
  "success": true,
  "openid": "user_openid_string",
  "userInfo": {
    "nickname": "用户昵称",
    "avatar": "头像URL"
  }
}
```

### 查询接口

```
POST /api/bank/search
Authorization: Bearer {openid}
Content-Type: application/json

Request:
{
  "encryptedData": "AES_encrypted_search_params",
  "encryptedKey": "RSA_encrypted_aes_key"
}

Decrypted Request:
{
  "keyword": "工商银行",
  "type": "bank_name",
  "limit": 20,
  "timestamp": *************
}
```

## 开发调试

### 1. 模拟模式

当网络请求失败时，自动切换到模拟数据模式：

```javascript
// 开发环境下的模拟响应
if (error.errMsg && error.errMsg.includes('request:fail')) {
  return {
    success: true,
    openid: `mock_openid_${Date.now()}`,
    userInfo: { nickname: '测试用户' }
  }
}
```

### 2. 调试日志

```javascript
console.log('搜索类型:', searchType, '关键字:', keyword)
console.log('发送加密请求到服务器...')
console.log('用户已登录:', openid.value)
```

## 注意事项

1. **生产环境**: 请替换为真实的RSA公钥和API地址
2. **加密库**: 建议使用crypto-js等成熟的加密库
3. **密钥安全**: RSA私钥只能存储在服务器端
4. **网络安全**: 建议使用HTTPS协议
5. **用户隐私**: 遵守相关隐私保护法规

## 测试验证

1. **登录流程测试**: 清除本地存储，重新进入小程序
2. **加密通信测试**: 查看网络请求，确认数据已加密
3. **异常处理测试**: 模拟网络异常，验证错误处理
4. **安全性测试**: 验证加密数据无法直接解读
