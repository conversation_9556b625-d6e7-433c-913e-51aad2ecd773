"use strict";
const common_vendor = require("../common/vendor.js");
const utils_crypto = require("../utils/crypto.js");
function useAuth() {
  const isLoggedIn = common_vendor.ref(false);
  const userInfo = common_vendor.ref(null);
  const openid = common_vendor.ref("");
  const isLogging = common_vendor.ref(false);
  const loginError = common_vendor.ref(null);
  const isAuthenticated = common_vendor.computed(() => isLoggedIn.value && openid.value);
  const needLogin = common_vendor.computed(() => !isAuthenticated.value);
  const checkLoginStatus = () => {
    try {
      const storedOpenid = common_vendor.index.getStorageSync("user_openid");
      const storedUserInfo = common_vendor.index.getStorageSync("user_info");
      if (storedOpenid) {
        openid.value = storedOpenid;
        userInfo.value = JSON.parse(storedUserInfo);
        isLoggedIn.value = true;
        common_vendor.index.__f__("log", "at composables/useAuth.js:33", "用户已登录:", openid.value);
        return true;
      }
      common_vendor.index.__f__("log", "at composables/useAuth.js:37", "用户未登录");
      return false;
    } catch (error) {
      common_vendor.index.__f__("error", "at composables/useAuth.js:40", "检查登录状态失败:", error);
      return false;
    }
  };
  const wxLogin = async () => {
    if (isLogging.value)
      return;
    isLogging.value = true;
    loginError.value = null;
    try {
      common_vendor.index.__f__("log", "at composables/useAuth.js:55", "开始微信登录...");
      const loginResult = await new Promise((resolve, reject) => {
        common_vendor.index.login({
          provider: "weixin",
          success: resolve,
          fail: reject
        });
      });
      if (!loginResult.code) {
        throw new Error("获取登录凭证失败");
      }
      common_vendor.index.__f__("log", "at composables/useAuth.js:70", "获取到登录code:", loginResult.code);
      const openidResult = await getOpenidFromServer(loginResult.code);
      if (openidResult.openId) {
        openid.value = openidResult.openId;
        userInfo.value = openidResult.userInfo || {};
        isLoggedIn.value = true;
        common_vendor.index.setStorageSync("user_openid", openid.value);
        common_vendor.index.setStorageSync("user_info", JSON.stringify(userInfo.value));
        common_vendor.index.__f__("log", "at composables/useAuth.js:85", "登录成功:", openid.value);
        common_vendor.index.showToast({
          title: "登录成功",
          icon: "success"
        });
        return true;
      } else {
        throw new Error(openidResult.message || "登录失败");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at composables/useAuth.js:98", "登录失败:", error);
      loginError.value = error.message || "登录失败";
      common_vendor.index.showToast({
        title: loginError.value,
        icon: "none"
      });
      return false;
    } finally {
      isLogging.value = false;
    }
  };
  const getOpenidFromServer = async (code) => {
    try {
      const requestData = {
        weChatCode: code
      };
      const encryptedRequest = utils_crypto.crypto.encryptRequest(requestData);
      common_vendor.index.__f__("log", "at composables/useAuth.js:127", "发送登录请求到服务器...");
      const response = await common_vendor.index.request({
        url: "http://hd.peixue100.cn/ylhapp/bankNum/jscode2session",
        // 替换为实际的API地址
        method: "POST",
        data: {
          parameter: encryptedRequest.encryptedData
        },
        header: {
          "Content-Type": "application/json"
        }
      });
      if (response.statusCode === 200 && response.data.code == 200) {
        const decryptedData = utils_crypto.crypto.decryptResponse(response.data.data, encryptedRequest.aesKey);
        return decryptedData;
      } else {
        throw new Error("服务器响应异常");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at composables/useAuth.js:151", "获取openid失败:", error);
      if (error.errMsg && error.errMsg.includes("request:fail")) {
        common_vendor.index.__f__("log", "at composables/useAuth.js:155", "网络请求失败，使用模拟数据");
        return {
          success: true,
          openid: `mock_openid_${Date.now()}`,
          userInfo: {
            nickname: "测试用户",
            avatar: ""
          },
          message: "登录成功（模拟）"
        };
      }
      throw error;
    }
  };
  const logout = () => {
    try {
      isLoggedIn.value = false;
      userInfo.value = null;
      openid.value = "";
      loginError.value = null;
      common_vendor.index.removeStorageSync("user_openid");
      common_vendor.index.removeStorageSync("user_info");
      common_vendor.index.__f__("log", "at composables/useAuth.js:186", "用户已退出登录");
      common_vendor.index.showToast({
        title: "已退出登录",
        icon: "success"
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at composables/useAuth.js:193", "退出登录失败:", error);
    }
  };
  const ensureLogin = async () => {
    if (checkLoginStatus()) {
      return true;
    }
    return await wxLogin();
  };
  const getUserProfile = async () => {
    try {
      const userProfile = await new Promise((resolve, reject) => {
        common_vendor.index.getUserProfile({
          desc: "用于完善用户资料",
          success: resolve,
          fail: reject
        });
      });
      if (userProfile.userInfo) {
        userInfo.value = {
          ...userInfo.value,
          ...userProfile.userInfo
        };
        common_vendor.index.setStorageSync("user_info", JSON.stringify(userInfo.value));
      }
      return userProfile.userInfo;
    } catch (error) {
      common_vendor.index.__f__("error", "at composables/useAuth.js:237", "获取用户信息失败:", error);
      throw error;
    }
  };
  return {
    // 响应式状态
    isLoggedIn,
    userInfo,
    openid,
    isLogging,
    loginError,
    // 计算属性
    isAuthenticated,
    needLogin,
    // 方法
    checkLoginStatus,
    wxLogin,
    logout,
    ensureLogin,
    getUserProfile
  };
}
exports.useAuth = useAuth;
//# sourceMappingURL=../../.sourcemap/mp-weixin/composables/useAuth.js.map
