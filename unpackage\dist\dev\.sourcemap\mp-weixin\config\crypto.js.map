{"version": 3, "file": "crypto.js", "sources": ["config/crypto.js"], "sourcesContent": ["// 加密配置文件\n// 生产环境中应该从安全的配置服务获取这些配置\n\n/**\n * 加密配置\n */\nexport const CRYPTO_CONFIG = {\n\t// RSA配置\n\tRSA: {\n\t\t// 密钥长度\n\t\tKEY_SIZE: 2048,\n\t\t\n\t\t// 公钥 - 生产环境应该从服务器获取\n\t\tPUBLIC_KEY: `-----BEGIN PUBLIC KEY-----\r\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmWPDFsI9N+4Hsmo2\r\nkwtPENGaJ+9Kiq4Nqscdac0LHfHLTEhPuPnnfYZ+g050Ag4IoS+C6U3MQM1P\r\ngi4IuhL4Ef2awrMJxf4HwuVxtIZy+oiT8SRQU84/jgcpi9Omoi2wKcRc5Bpr\r\nfp4wI2nzb9bS397KsPA0b/HSix+xlCkBCqTyL3HjFOBNSxsU3o0F0DyesomK\r\nG5B6J9Csj8ALWLs/JUsZNfO1tMyHN2invduNPwvn7dETAGCeQ1t6Of986twv\r\nEE8jDxEPQ35mZVF2cDkcvSSb8aJLzlyZS6zzmYF1UsfT+Z5j3lYUIIbVfacR\r\n3V/FYL4qZx+0eYY2VrgWhoEZqQIDAQAB\n-----END PUBLIC KEY-----`,\n\t\t\n\t\t// 算法配置\n\t\tALGORITHM: 'RSA-OAEP',\n\t\tHASH: 'SHA-256'\n\t},\n\t\n\t// AES配置\n\tAES: {\n\t\t// 密钥长度（字符数）\n\t\tKEY_LENGTH: 32,\n\t\t\n\t\t// 加密模式\n\t\tMODE: 'CBC',\n\t\t\n\t\t// 填充方式\n\t\tPADDING: 'Pkcs5',\n\t\t\n\t\t// 字符集\n\t\tCHARSET: 'UTF-8'\n\t},\n\t\n\t// 请求配置\n\tREQUEST: {\n\t\t// 是否启用加密\n\t\tENABLE_ENCRYPTION: true,\n\t\t\n\t\t// 加密的请求方法\n\t\tENCRYPTED_METHODS: ['POST', 'PUT', 'PATCH'],\n\t\t\n\t\t// 需要加密的接口路径\n\t\tENCRYPTED_PATHS: [\n\t\t\t'/api/auth/login',\n\t\t\t'/api/bank/search',\n\t\t\t'/api/user/profile'\n\t\t],\n\t\t\n\t\t// 请求超时时间\n\t\tTIMEOUT: 10000\n\t},\n\t\n\t// 开发配置\n\tDEVELOPMENT: {\n\t\t// 是否启用调试日志\n\t\tENABLE_DEBUG_LOG: false,\n\t\t\n\t\t// 是否启用加密测试\n\t\tENABLE_CRYPTO_TEST: false,\n\t\t\n\t\t// 是否使用模拟数据\n\t\tUSE_MOCK_DATA: false,\n\t\t\n\t\t// 测试模式下的密钥\n\t\tTEST_AES_KEY: 'test-aes-key-32-characters-long',\n\t\t\n\t\t// 是否跳过RSA验证\n\t\tSKIP_RSA_VALIDATION: false\n\t}\n}\n\n/**\n * 环境配置\n */\nexport const ENV_CONFIG = {\n\t// 开发环境\n\tdevelopment: {\n\t\tAPI_BASE_URL: 'http://hd.peixue100.cn/ylhapp',\n\t\tENABLE_ENCRYPTION: true,\n\t\tDEBUG_MODE: true\n\t},\n\t\n\t// 测试环境\n\ttesting: {\n\t\tAPI_BASE_URL: 'https://test-api.example.com',\n\t\tENABLE_ENCRYPTION: true,\n\t\tDEBUG_MODE: true\n\t},\n\t\n\t// 生产环境\n\tproduction: {\n\t\tAPI_BASE_URL: 'http://hd.peixue100.cn/ylhapp',\n\t\tENABLE_ENCRYPTION: true,\n\t\tDEBUG_MODE: false\n\t}\n}\n\n/**\n * 获取当前环境配置\n * @returns {Object} 当前环境的配置\n */\nexport function getCurrentEnvConfig() {\n\t// 根据实际情况判断当前环境\n\t// 这里简化为开发环境\n\tconst env = process.env.NODE_ENV || 'development'\n\treturn ENV_CONFIG[env] || ENV_CONFIG.development\n}\n\n/**\n * 获取RSA公钥\n * @returns {string} RSA公钥\n */\nexport function getRSAPublicKey() {\n\treturn CRYPTO_CONFIG.RSA.PUBLIC_KEY\n}\n\n/**\n * 检查是否需要加密\n * @param {string} method - 请求方法\n * @param {string} path - 请求路径\n * @returns {boolean} 是否需要加密\n */\nexport function shouldEncrypt(method, path) {\n\tif (!CRYPTO_CONFIG.REQUEST.ENABLE_ENCRYPTION) {\n\t\treturn false\n\t}\n\t\n\t// 检查请求方法\n\tif (!CRYPTO_CONFIG.REQUEST.ENCRYPTED_METHODS.includes(method.toUpperCase())) {\n\t\treturn false\n\t}\n\t\n\t// 检查请求路径\n\treturn CRYPTO_CONFIG.REQUEST.ENCRYPTED_PATHS.some(encryptedPath => \n\t\tpath.includes(encryptedPath)\n\t)\n}\n\n/**\n * 获取AES配置\n * @returns {Object} AES配置\n */\nexport function getAESConfig() {\n\treturn CRYPTO_CONFIG.AES\n}\n\n/**\n * 获取RSA配置\n * @returns {Object} RSA配置\n */\nexport function getRSAConfig() {\n\treturn CRYPTO_CONFIG.RSA\n}\n\n/**\n * 是否启用调试日志\n * @returns {boolean} 是否启用\n */\nexport function isDebugEnabled() {\n\treturn CRYPTO_CONFIG.DEVELOPMENT.ENABLE_DEBUG_LOG\n}\n\n/**\n * 是否启用加密测试\n * @returns {boolean} 是否启用\n */\nexport function isCryptoTestEnabled() {\n\treturn CRYPTO_CONFIG.DEVELOPMENT.ENABLE_CRYPTO_TEST\n}\n\n/**\n * 获取完整的加密配置\n * @returns {Object} 完整配置\n */\nexport function getCryptoConfig() {\n\treturn {\n\t\t...CRYPTO_CONFIG,\n\t\tENV: getCurrentEnvConfig()\n\t}\n}\n\n/**\n * 更新RSA公钥\n * @param {string} publicKey - 新的公钥\n */\nexport function updateRSAPublicKey(publicKey) {\n\tCRYPTO_CONFIG.RSA.PUBLIC_KEY = publicKey\n\tconsole.log('RSA公钥已更新')\n}\n\n/**\n * 验证配置完整性\n * @returns {Object} 验证结果\n */\nexport function validateConfig() {\n\tconst errors = []\n\tconst warnings = []\n\t\n\t// 验证RSA公钥\n\tif (!CRYPTO_CONFIG.RSA.PUBLIC_KEY || CRYPTO_CONFIG.RSA.PUBLIC_KEY.length < 100) {\n\t\terrors.push('RSA公钥无效或过短')\n\t}\n\t\n\t// 验证AES密钥长度\n\tif (CRYPTO_CONFIG.AES.KEY_LENGTH < 16) {\n\t\twarnings.push('AES密钥长度建议至少16位')\n\t}\n\t\n\t// 验证API地址\n\tconst envConfig = getCurrentEnvConfig()\n\tif (!envConfig.API_BASE_URL || !envConfig.API_BASE_URL.startsWith('https://')) {\n\t\twarnings.push('建议使用HTTPS协议')\n\t}\n\t\n\treturn {\n\t\tvalid: errors.length === 0,\n\t\terrors,\n\t\twarnings\n\t}\n}\n"], "names": [], "mappings": ";;AAMO,MAAM,gBAAgB;AAAA;AAAA,EAE5B,KAAK;AAAA;AAAA,IAEJ,UAAU;AAAA;AAAA,IAGV,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAWZ,WAAW;AAAA,IACX,MAAM;AAAA,EACP;AAAA;AAAA,EAGA,KAAK;AAAA;AAAA,IAEJ,YAAY;AAAA;AAAA,IAGZ,MAAM;AAAA;AAAA,IAGN,SAAS;AAAA;AAAA,IAGT,SAAS;AAAA,EACV;AAAA;AAAA,EAGA,SAAS;AAAA;AAAA,IAER,mBAAmB;AAAA;AAAA,IAGnB,mBAAmB,CAAC,QAAQ,OAAO,OAAO;AAAA;AAAA,IAG1C,iBAAiB;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA;AAAA,IAGA,SAAS;AAAA,EACV;AAAA;AAAA,EAGA,aAAa;AAAA;AAAA,IAEZ,kBAAkB;AAAA;AAAA,IAGlB,oBAAoB;AAAA;AAAA,IAGpB,eAAe;AAAA;AAAA,IAGf,cAAc;AAAA;AAAA,IAGd,qBAAqB;AAAA,EACtB;AACD;AA2CO,SAAS,kBAAkB;AACjC,SAAO,cAAc,IAAI;AAC1B;AA4BO,SAAS,eAAe;AAC9B,SAAO,cAAc;AACtB;AAcO,SAAS,iBAAiB;AAChC,SAAO,cAAc,YAAY;AAClC;;;;"}