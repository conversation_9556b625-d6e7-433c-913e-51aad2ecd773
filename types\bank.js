// 银行相关的类型定义和常量

/**
 * 银行信息接口定义
 * @typedef {Object} BankInfo
 * @property {string} code - 联行号
 * @property {string} bankName - 银行名称
 * @property {string} branchName - 支行名称
 * @property {string} [address] - 地址
 * @property {string} [phone] - 电话
 * @property {string} [distance] - 距离
 */

/**
 * API响应接口定义
 * @typedef {Object} ApiResponse
 * @property {boolean} success - 是否成功
 * @property {string} [message] - 消息
 * @property {BankInfo[]} [data] - 数据
 * @property {number} [total] - 总数
 */

/**
 * 搜索参数接口定义
 * @typedef {Object} SearchParams
 * @property {string} keyword - 搜索关键字
 * @property {number} [limit] - 限制数量
 * @property {number} [offset] - 偏移量
 * @property {string} [type] - 搜索类型
 */

/**
 * 银行类型常量
 */
export const BANK_TYPES = {
	ICBC: 'icbc',        // 工商银行
	ABC: 'abc',          // 农业银行
	BOC: 'boc',          // 中国银行
	CCB: 'ccb',          // 建设银行
	BOCOM: 'bocom',      // 交通银行
	CMB: 'cmb',          // 招商银行
	CITIC: 'citic',      // 中信银行
	CEB: 'ceb',          // 光大银行
	CMBC: 'cmbc',        // 民生银行
	CIB: 'cib',          // 兴业银行
	SPDB: 'spdb',        // 浦发银行
	PAB: 'pab',          // 平安银行
	HXB: 'hxb',          // 华夏银行
	GDB: 'gdb',          // 广发银行
	PSBC: 'psbc'         // 邮储银行
}

/**
 * 银行名称映射
 */
export const BANK_NAMES = {
	[BANK_TYPES.ICBC]: '中国工商银行',
	[BANK_TYPES.ABC]: '中国农业银行',
	[BANK_TYPES.BOC]: '中国银行',
	[BANK_TYPES.CCB]: '中国建设银行',
	[BANK_TYPES.BOCOM]: '交通银行',
	[BANK_TYPES.CMB]: '招商银行',
	[BANK_TYPES.CITIC]: '中信银行',
	[BANK_TYPES.CEB]: '中国光大银行',
	[BANK_TYPES.CMBC]: '中国民生银行',
	[BANK_TYPES.CIB]: '兴业银行',
	[BANK_TYPES.SPDB]: '上海浦东发展银行',
	[BANK_TYPES.PAB]: '平安银行',
	[BANK_TYPES.HXB]: '华夏银行',
	[BANK_TYPES.GDB]: '广发银行',
	[BANK_TYPES.PSBC]: '中国邮政储蓄银行'
}

/**
 * 银行简称映射
 */
export const BANK_SHORT_NAMES = {
	'工行': BANK_TYPES.ICBC,
	'农行': BANK_TYPES.ABC,
	'中行': BANK_TYPES.BOC,
	'建行': BANK_TYPES.CCB,
	'交行': BANK_TYPES.BOCOM,
	'招行': BANK_TYPES.CMB,
	'中信': BANK_TYPES.CITIC,
	'光大': BANK_TYPES.CEB,
	'民生': BANK_TYPES.CMBC,
	'兴业': BANK_TYPES.CIB,
	'浦发': BANK_TYPES.SPDB,
	'平安': BANK_TYPES.PAB,
	'华夏': BANK_TYPES.HXB,
	'广发': BANK_TYPES.GDB,
	'邮储': BANK_TYPES.PSBC
}

/**
 * 联行号前缀映射（前3位）
 */
export const BANK_CODE_PREFIXES = {
	'102': BANK_TYPES.ICBC,    // 工商银行
	'103': BANK_TYPES.ABC,     // 农业银行
	'104': BANK_TYPES.BOC,     // 中国银行
	'105': BANK_TYPES.CCB,     // 建设银行
	'301': BANK_TYPES.BOCOM,   // 交通银行
	'308': BANK_TYPES.CMB,     // 招商银行
	'302': BANK_TYPES.CITIC,   // 中信银行
	'303': BANK_TYPES.CEB,     // 光大银行
	'305': BANK_TYPES.CMBC,    // 民生银行
	'309': BANK_TYPES.CIB,     // 兴业银行
	'310': BANK_TYPES.SPDB,    // 浦发银行
	'307': BANK_TYPES.PAB,     // 平安银行
	'304': BANK_TYPES.HXB,     // 华夏银行
	'306': BANK_TYPES.GDB,     // 广发银行
	'403': BANK_TYPES.PSBC     // 邮储银行
}

/**
 * 搜索类型常量
 */
export const SEARCH_TYPES = {
	KEYWORD: 'keyword',      // 关键字搜索
	BANK_CODE: 'bank_code',  // 联行号搜索
	BANK_NAME: 'bank_name',  // 银行名称搜索
	REGION: 'region'         // 地区搜索
}

/**
 * 工具函数：根据联行号获取银行类型
 * @param {string} bankCode - 联行号
 * @returns {string|null} 银行类型
 */
export function getBankTypeByCode(bankCode) {
	if (!bankCode || bankCode.length < 3) return null
	const prefix = bankCode.substring(0, 3)
	return BANK_CODE_PREFIXES[prefix] || null
}

/**
 * 工具函数：根据银行类型获取银行名称
 * @param {string} bankType - 银行类型
 * @returns {string} 银行名称
 */
export function getBankNameByType(bankType) {
	return BANK_NAMES[bankType] || '未知银行'
}

/**
 * 工具函数：根据简称获取银行类型
 * @param {string} shortName - 银行简称
 * @returns {string|null} 银行类型
 */
export function getBankTypeByShortName(shortName) {
	return BANK_SHORT_NAMES[shortName] || null
}

/**
 * 工具函数：判断搜索类型
 * @param {string} keyword - 搜索关键字
 * @returns {string} 搜索类型
 */
export function detectSearchType(keyword) {
	if (!keyword) return SEARCH_TYPES.KEYWORD
	
	// 纯数字且长度为12，判断为联行号
	if (/^\d{12}$/.test(keyword)) {
		return SEARCH_TYPES.BANK_CODE
	}
	
	// 包含银行关键字
	if (Object.keys(BANK_SHORT_NAMES).some(name => keyword.includes(name))) {
		return SEARCH_TYPES.BANK_NAME
	}
	
	// 包含地区关键字（简单判断）
	const regionKeywords = ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '重庆', '西安']
	if (regionKeywords.some(region => keyword.includes(region))) {
		return SEARCH_TYPES.REGION
	}
	
	return SEARCH_TYPES.KEYWORD
}
