// RSA密钥对生成工具
// 注意：这个工具主要用于开发和测试，生产环境建议使用专业的密钥管理服务

/**
 * RSA密钥对生成器
 * 使用Web Crypto API或第三方库生成RSA密钥对
 */
export class RSAKeyGenerator {
	
	/**
	 * 生成RSA密钥对（使用Web Crypto API）
	 * @param {number} keySize - 密钥长度，默认2048位
	 * @returns {Promise<Object>} 包含公钥和私钥的对象
	 */
	static async generateKeyPair(keySize = 2048) {
		try {
			// 检查浏览器是否支持Web Crypto API
			if (!window.crypto || !window.crypto.subtle) {
				throw new Error('当前环境不支持Web Crypto API')
			}
			
			console.log(`开始生成${keySize}位RSA密钥对...`)
			
			// 生成RSA密钥对
			const keyPair = await window.crypto.subtle.generateKey(
				{
					name: 'RSA-OAEP',
					modulusLength: keySize,
					publicExponent: new Uint8Array([1, 0, 1]), // 65537
					hash: 'SHA-256'
				},
				true, // 可导出
				['encrypt', 'decrypt']
			)
			
			// 导出公钥
			const publicKeyBuffer = await window.crypto.subtle.exportKey('spki', keyPair.publicKey)
			const publicKeyPem = this.arrayBufferToPem(publicKeyBuffer, 'PUBLIC KEY')
			
			// 导出私钥
			const privateKeyBuffer = await window.crypto.subtle.exportKey('pkcs8', keyPair.privateKey)
			const privateKeyPem = this.arrayBufferToPem(privateKeyBuffer, 'PRIVATE KEY')
			
			console.log('RSA密钥对生成成功')
			
			return {
				publicKey: publicKeyPem,
				privateKey: privateKeyPem,
				keySize: keySize
			}
			
		} catch (error) {
			console.error('RSA密钥对生成失败:', error)
			throw new Error('密钥生成失败: ' + error.message)
		}
	}
	
	/**
	 * 将ArrayBuffer转换为PEM格式
	 * @param {ArrayBuffer} buffer - 密钥数据
	 * @param {string} type - 密钥类型（PUBLIC KEY 或 PRIVATE KEY）
	 * @returns {string} PEM格式的密钥
	 */
	static arrayBufferToPem(buffer, type) {
		const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)))
		const formatted = base64.match(/.{1,64}/g).join('\n')
		return `-----BEGIN ${type}-----\n${formatted}\n-----END ${type}-----`
	}
	
	/**
	 * 生成用于开发测试的RSA密钥对（简化版本）
	 * @returns {Object} 包含公钥和私钥的对象
	 */
	static generateTestKeyPair() {
		// 这是一个用于开发测试的RSA密钥对
		// 生产环境请使用真正的密钥生成工具
		const testKeyPair = {
			publicKey: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwL7zKZKxQ9QJ8YQJ
xKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzK
zKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzK
zKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzK
zKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzK
zKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzK
zKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzK
zKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzK
zKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzKzK
QIDAQAB
-----END PUBLIC KEY-----`,
			privateKey: `-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDAvvMpkrFD
1AnxhAnErMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrM
rMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrM
rMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrM
rMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrM
rMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrM
rMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrM
rMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrM
rMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrMrM
wIDAQABAoIBAExample...
-----END PRIVATE KEY-----`
		}
		
		console.log('使用测试RSA密钥对')
		return testKeyPair
	}
	
	/**
	 * 验证密钥对是否匹配
	 * @param {string} publicKey - 公钥
	 * @param {string} privateKey - 私钥
	 * @returns {Promise<boolean>} 是否匹配
	 */
	static async validateKeyPair(publicKey, privateKey) {
		try {
			// 使用JSEncrypt测试加密解密
			const JSEncrypt = (await import('jsencrypt')).default
			
			const testData = 'test-validation-data'
			
			// 用公钥加密
			const encrypt = new JSEncrypt()
			encrypt.setPublicKey(publicKey)
			const encrypted = encrypt.encrypt(testData)
			
			if (!encrypted) {
				return false
			}
			
			// 用私钥解密
			const decrypt = new JSEncrypt()
			decrypt.setPrivateKey(privateKey)
			const decrypted = decrypt.decrypt(encrypted)
			
			return decrypted === testData
		} catch (error) {
			console.error('密钥对验证失败:', error)
			return false
		}
	}
	
	/**
	 * 从PEM格式提取密钥内容
	 * @param {string} pemKey - PEM格式的密钥
	 * @returns {string} 密钥内容（去除头尾标识）
	 */
	static extractKeyContent(pemKey) {
		return pemKey
			.replace(/-----BEGIN [^-]+-----/, '')
			.replace(/-----END [^-]+-----/, '')
			.replace(/\s/g, '')
	}
	
	/**
	 * 格式化密钥为标准PEM格式
	 * @param {string} keyContent - 密钥内容
	 * @param {string} type - 密钥类型
	 * @returns {string} 格式化的PEM密钥
	 */
	static formatToPem(keyContent, type) {
		const cleanContent = keyContent.replace(/\s/g, '')
		const formatted = cleanContent.match(/.{1,64}/g).join('\n')
		return `-----BEGIN ${type}-----\n${formatted}\n-----END ${type}-----`
	}
}

/**
 * 密钥管理工具
 */
export const keyManager = {
	/**
	 * 生成新的密钥对
	 */
	generateKeyPair: (keySize) => RSAKeyGenerator.generateKeyPair(keySize),
	
	/**
	 * 获取测试密钥对
	 */
	getTestKeyPair: () => RSAKeyGenerator.generateTestKeyPair(),
	
	/**
	 * 验证密钥对
	 */
	validateKeyPair: (publicKey, privateKey) => RSAKeyGenerator.validateKeyPair(publicKey, privateKey),
	
	/**
	 * 格式化密钥
	 */
	formatKey: (keyContent, type) => RSAKeyGenerator.formatToPem(keyContent, type)
}
