{"version": 3, "file": "api.js", "sources": ["config/api.js"], "sourcesContent": ["// API接口配置文件\n\n/**\n * API基础配置\n */\nexport const API_CONFIG = {\n\t// 基础URL\n\tBASE_URL: 'https://hd.peixue100.cn/ylhapp',\n\t\n\t// 接口路径\n\tENDPOINTS: {\n\t\t// 用户认证相关\n\t\tLOGIN: '/bankNum/jscode2session',\n\t\t\n\t\t// 银行查询相关\n\t\tBANK_SEARCH: '/bankNum/queryBankInfo',\n\t\tBANK_DETAIL: '/bankNum/detail',\n\t\tBANK_LIST: '/bankNum/list',\n\t\t\n\t\t// 其他接口\n\t\tUSER_PROFILE: '/bankNum/user/profile',\n\t\tFEEDBACK: '/bankNum/feedback'\n\t},\n\t\n\t// 请求配置\n\tREQUEST: {\n\t\tTIMEOUT: 10000,\n\t\tRETRY_COUNT: 3,\n\t\tRETRY_DELAY: 1000\n\t},\n\t\n\t// 响应状态码\n\tSTATUS_CODES: {\n\t\tSUCCESS: 200,\n\t\tUNAUTHORIZED: 401,\n\t\tFORBIDDEN: 403,\n\t\tNOT_FOUND: 404,\n\t\tSERVER_ERROR: 500\n\t},\n\t\n\t// 业务状态码\n\tBUSINESS_CODES: {\n\t\tSUCCESS: 200,\n\t\tPARAM_ERROR: 400,\n\t\tAUTH_FAILED: 401,\n\t\tNO_DATA: 404,\n\t\tSERVER_ERROR: 500\n\t}\n}\n\n/**\n * 获取完整的API URL\n * @param {string} endpoint - 接口路径\n * @returns {string} 完整的URL\n */\nexport function getApiUrl(endpoint) {\n\tconst baseUrl = API_CONFIG.BASE_URL\n\tconst path = API_CONFIG.ENDPOINTS[endpoint] || endpoint\n\treturn `${baseUrl}${path}`\n}\n\n/**\n * 银行查询相关的API配置\n */\nexport const BANK_API_CONFIG = {\n\t// 查询参数配置\n\tSEARCH_PARAMS: {\n\t\t// 默认查询数量\n\t\tDEFAULT_LIMIT: 20,\n\t\t\n\t\t// 最大查询数量\n\t\tMAX_LIMIT: 100,\n\t\t\n\t\t// 支持的搜索类型\n\t\tSEARCH_TYPES: {\n\t\t\tKEYWORD: 'keyword',      // 关键字搜索\n\t\t\tBANK_CODE: 'bank_code',  // 联行号搜索\n\t\t\tBANK_NAME: 'bank_name',  // 银行名称搜索\n\t\t\tREGION: 'region'         // 地区搜索\n\t\t}\n\t},\n\t\n\t// 响应数据格式\n\tRESPONSE_FORMAT: {\n\t\t// 标准响应格式\n\t\tSTANDARD: {\n\t\t\tcode: 'number',      // 状态码\n\t\t\tmessage: 'string',   // 消息\n\t\t\tdata: 'object',      // 数据\n\t\t\ttimestamp: 'number'  // 时间戳\n\t\t},\n\t\t\n\t\t// 银行信息格式\n\t\tBANK_INFO: {\n\t\t\tcode: 'string',        // 联行号\n\t\t\tbankName: 'string',    // 银行名称\n\t\t\tbranchName: 'string',  // 支行名称\n\t\t\taddress: 'string',     // 地址\n\t\t\tphone: 'string',       // 电话\n\t\t\tdistance: 'string'     // 距离\n\t\t}\n\t}\n}\n\n/**\n * 请求重试配置\n */\nexport const RETRY_CONFIG = {\n\t// 需要重试的错误类型\n\tRETRY_CONDITIONS: [\n\t\t'request:fail',\n\t\t'timeout',\n\t\t'network error',\n\t\t'connection refused'\n\t],\n\t\n\t// 重试延迟策略（指数退避）\n\tgetRetryDelay: (attempt) => {\n\t\treturn Math.min(1000 * Math.pow(2, attempt), 10000)\n\t},\n\t\n\t// 是否应该重试\n\tshouldRetry: (error, attempt) => {\n\t\tif (attempt >= API_CONFIG.REQUEST.RETRY_COUNT) {\n\t\t\treturn false\n\t\t}\n\t\t\n\t\tif (error.errMsg) {\n\t\t\treturn RETRY_CONFIG.RETRY_CONDITIONS.some(condition => \n\t\t\t\terror.errMsg.includes(condition)\n\t\t\t)\n\t\t}\n\t\t\n\t\treturn false\n\t}\n}\n\n/**\n * API响应处理器\n */\nexport const ResponseHandler = {\n\t/**\n\t * 处理标准响应\n\t * @param {Object} response - 原始响应\n\t * @returns {Object} 处理后的响应\n\t */\n\thandleStandardResponse(response) {\n\t\t// 检查HTTP状态码\n\t\tif (response.statusCode !== API_CONFIG.STATUS_CODES.SUCCESS) {\n\t\t\tthrow new Error(`HTTP错误: ${response.statusCode}`)\n\t\t}\n\t\t\n\t\t// // 检查业务状态码\n\t\t// if (response.data.code !== API_CONFIG.BUSINESS_CODES.SUCCESS) {\n\t\t// \tthrow new Error(response.data.message || '业务处理失败')\n\t\t// }\n\t\t\n\t\treturn {\n\t\t\tsuccess: true,\n\t\t\tdata: response.data.data,\n\t\t\tmessage: response.data.message,\n\t\t\ttimestamp: response.data.timestamp || Date.now()\n\t\t}\n\t},\n\t\n\t/**\n\t * 处理银行查询响应\n\t * @param {Object} response - 原始响应\n\t * @returns {Array} 银行信息数组\n\t */\n\thandleBankSearchResponse(response) {\n\t\tconst standardResponse = this.handleStandardResponse(response)\n\t\t\n\t\tif (!standardResponse.data) {\n\t\t\treturn []\n\t\t}\n\t\t\n\t\t// 确保返回数组格式\n\t\tconst results = Array.isArray(standardResponse.data) \n\t\t\t? standardResponse.data \n\t\t\t: [standardResponse.data]\n\t\t\n\t\t// 验证数据格式\n\t\treturn results.filter(item => item && item.code)\n\t},\n\t\n\t/**\n\t * 处理错误响应\n\t * @param {Error} error - 错误对象\n\t * @returns {Object} 标准化的错误信息\n\t */\n\thandleErrorResponse(error) {\n\t\tlet errorMessage = '未知错误'\n\t\tlet errorCode = 'UNKNOWN_ERROR'\n\t\t\n\t\tif (error.errMsg) {\n\t\t\tif (error.errMsg.includes('request:fail')) {\n\t\t\t\terrorMessage = '网络请求失败'\n\t\t\t\terrorCode = 'NETWORK_ERROR'\n\t\t\t} else if (error.errMsg.includes('timeout')) {\n\t\t\t\terrorMessage = '请求超时'\n\t\t\t\terrorCode = 'TIMEOUT_ERROR'\n\t\t\t}\n\t\t} else if (error.message) {\n\t\t\terrorMessage = error.message\n\t\t\terrorCode = 'BUSINESS_ERROR'\n\t\t}\n\t\t\n\t\treturn {\n\t\t\tsuccess: false,\n\t\t\terror: errorMessage,\n\t\t\tcode: errorCode,\n\t\t\ttimestamp: Date.now()\n\t\t}\n\t}\n}\n\n/**\n * API请求工具函数\n */\nexport const ApiUtils = {\n\t/**\n\t * 构建查询参数\n\t * @param {Object} params - 参数对象\n\t * @returns {Object} 标准化的参数\n\t */\n\tbuildSearchParams(params) {\n\t\treturn {\n\t\t\tkeyword: params.keyword || '',\n\t\t\ttype: params.type || BANK_API_CONFIG.SEARCH_PARAMS.SEARCH_TYPES.KEYWORD,\n\t\t\tlimit: Math.min(\n\t\t\t\tparams.limit || BANK_API_CONFIG.SEARCH_PARAMS.DEFAULT_LIMIT,\n\t\t\t\tBANK_API_CONFIG.SEARCH_PARAMS.MAX_LIMIT\n\t\t\t),\n\t\t\ttimestamp: Date.now()\n\t\t}\n\t},\n\t\n\t/**\n\t * 验证搜索参数\n\t * @param {Object} params - 参数对象\n\t * @returns {boolean} 是否有效\n\t */\n\tvalidateSearchParams(params) {\n\t\tif (!params.keyword || typeof params.keyword !== 'string') {\n\t\t\treturn false\n\t\t}\n\t\t\n\t\tif (params.keyword.trim().length === 0) {\n\t\t\treturn false\n\t\t}\n\t\t\n\t\treturn true\n\t},\n\t\n\t/**\n\t * 格式化银行信息\n\t * @param {Object} bankInfo - 原始银行信息\n\t * @returns {Object} 格式化后的银行信息\n\t */\n\tformatBankInfo(bankInfo) {\n\t\treturn {\n\t\t\tcode: bankInfo.bankNum || '',\n\t\t\tbankName: bankInfo.bankDetail || bankInfo.bankDetail || '',\n\t\t\tbranchName: bankInfo.bankDetail || bankInfo.bankDetail || '',\n\t\t\taddress: bankInfo.bankDetail || '',\n\t\t\tphone: bankInfo.phone || '',\n\t\t\tdistance: bankInfo.matching || ''\n\t\t}\n\t}\n}\n\n/**\n * 获取API配置\n * @returns {Object} API配置对象\n */\nexport function getApiConfig() {\n\treturn {\n\t\t...API_CONFIG,\n\t\tBANK_API: BANK_API_CONFIG,\n\t\tRETRY: RETRY_CONFIG\n\t}\n}\n"], "names": [], "mappings": ";AAKO,MAAM,aAAa;AAAA;AAAA,EAEzB,UAAU;AAAA;AAAA,EAGV,WAAW;AAAA;AAAA,IAEV,OAAO;AAAA;AAAA,IAGP,aAAa;AAAA,IACb,aAAa;AAAA,IACb,WAAW;AAAA;AAAA,IAGX,cAAc;AAAA,IACd,UAAU;AAAA,EACV;AAAA;AAAA,EAGD,SAAS;AAAA,IACR,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,EACb;AAAA;AAAA,EAGD,cAAc;AAAA,IACb,SAAS;AAAA,IACT,cAAc;AAAA,IACd,WAAW;AAAA,IACX,WAAW;AAAA,IACX,cAAc;AAAA,EACd;AAAA;AAAA,EAGD,gBAAgB;AAAA,IACf,SAAS;AAAA,IACT,aAAa;AAAA,IACb,aAAa;AAAA,IACb,SAAS;AAAA,IACT,cAAc;AAAA,EACd;AACF;AAOO,SAAS,UAAU,UAAU;AACnC,QAAM,UAAU,WAAW;AAC3B,QAAM,OAAO,WAAW,UAAU,QAAQ,KAAK;AAC/C,SAAO,GAAG,OAAO,GAAG,IAAI;AACzB;AAKO,MAAM,kBAAkB;AAAA;AAAA,EAE9B,eAAe;AAAA;AAAA,IAEd,eAAe;AAAA;AAAA,IAGf,WAAW;AAAA;AAAA,IAGX,cAAc;AAAA,MACb,SAAS;AAAA;AAAA,MACT,WAAW;AAAA;AAAA,MACX,WAAW;AAAA;AAAA,MACX,QAAQ;AAAA;AAAA,IACR;AAAA,EACD;AAAA;AAAA,EAGD,iBAAiB;AAAA;AAAA,IAEhB,UAAU;AAAA,MACT,MAAM;AAAA;AAAA,MACN,SAAS;AAAA;AAAA,MACT,MAAM;AAAA;AAAA,MACN,WAAW;AAAA;AAAA,IACX;AAAA;AAAA,IAGD,WAAW;AAAA,MACV,MAAM;AAAA;AAAA,MACN,UAAU;AAAA;AAAA,MACV,YAAY;AAAA;AAAA,MACZ,SAAS;AAAA;AAAA,MACT,OAAO;AAAA;AAAA,MACP,UAAU;AAAA;AAAA,IACV;AAAA,EACD;AACF;AAKY,MAAC,eAAe;AAAA;AAAA,EAE3B,kBAAkB;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACA;AAAA;AAAA,EAGD,eAAe,CAAC,YAAY;AAC3B,WAAO,KAAK,IAAI,MAAO,KAAK,IAAI,GAAG,OAAO,GAAG,GAAK;AAAA,EAClD;AAAA;AAAA,EAGD,aAAa,CAAC,OAAO,YAAY;AAChC,QAAI,WAAW,WAAW,QAAQ,aAAa;AAC9C,aAAO;AAAA,IACP;AAED,QAAI,MAAM,QAAQ;AACjB,aAAO,aAAa,iBAAiB;AAAA,QAAK,eACzC,MAAM,OAAO,SAAS,SAAS;AAAA,MAC/B;AAAA,IACD;AAED,WAAO;AAAA,EACP;AACF;AAKY,MAAC,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9B,uBAAuB,UAAU;AAEhC,QAAI,SAAS,eAAe,WAAW,aAAa,SAAS;AAC5D,YAAM,IAAI,MAAM,WAAW,SAAS,UAAU,EAAE;AAAA,IAChD;AAOD,WAAO;AAAA,MACN,SAAS;AAAA,MACT,MAAM,SAAS,KAAK;AAAA,MACpB,SAAS,SAAS,KAAK;AAAA,MACvB,WAAW,SAAS,KAAK,aAAa,KAAK,IAAK;AAAA,IAChD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,yBAAyB,UAAU;AAClC,UAAM,mBAAmB,KAAK,uBAAuB,QAAQ;AAE7D,QAAI,CAAC,iBAAiB,MAAM;AAC3B,aAAO,CAAE;AAAA,IACT;AAGD,UAAM,UAAU,MAAM,QAAQ,iBAAiB,IAAI,IAChD,iBAAiB,OACjB,CAAC,iBAAiB,IAAI;AAGzB,WAAO,QAAQ,OAAO,UAAQ,QAAQ,KAAK,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,oBAAoB,OAAO;AAC1B,QAAI,eAAe;AACnB,QAAI,YAAY;AAEhB,QAAI,MAAM,QAAQ;AACjB,UAAI,MAAM,OAAO,SAAS,cAAc,GAAG;AAC1C,uBAAe;AACf,oBAAY;AAAA,MACZ,WAAU,MAAM,OAAO,SAAS,SAAS,GAAG;AAC5C,uBAAe;AACf,oBAAY;AAAA,MACZ;AAAA,IACJ,WAAa,MAAM,SAAS;AACzB,qBAAe,MAAM;AACrB,kBAAY;AAAA,IACZ;AAED,WAAO;AAAA,MACN,SAAS;AAAA,MACT,OAAO;AAAA,MACP,MAAM;AAAA,MACN,WAAW,KAAK,IAAK;AAAA,IACrB;AAAA,EACD;AACF;AAKY,MAAC,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvB,kBAAkB,QAAQ;AACzB,WAAO;AAAA,MACN,SAAS,OAAO,WAAW;AAAA,MAC3B,MAAM,OAAO,QAAQ,gBAAgB,cAAc,aAAa;AAAA,MAChE,OAAO,KAAK;AAAA,QACX,OAAO,SAAS,gBAAgB,cAAc;AAAA,QAC9C,gBAAgB,cAAc;AAAA,MAC9B;AAAA,MACD,WAAW,KAAK,IAAK;AAAA,IACrB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,qBAAqB,QAAQ;AAC5B,QAAI,CAAC,OAAO,WAAW,OAAO,OAAO,YAAY,UAAU;AAC1D,aAAO;AAAA,IACP;AAED,QAAI,OAAO,QAAQ,KAAI,EAAG,WAAW,GAAG;AACvC,aAAO;AAAA,IACP;AAED,WAAO;AAAA,EACP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,eAAe,UAAU;AACxB,WAAO;AAAA,MACN,MAAM,SAAS,WAAW;AAAA,MAC1B,UAAU,SAAS,cAAc,SAAS,cAAc;AAAA,MACxD,YAAY,SAAS,cAAc,SAAS,cAAc;AAAA,MAC1D,SAAS,SAAS,cAAc;AAAA,MAChC,OAAO,SAAS,SAAS;AAAA,MACzB,UAAU,SAAS,YAAY;AAAA,IAC/B;AAAA,EACD;AACF;;;;;"}