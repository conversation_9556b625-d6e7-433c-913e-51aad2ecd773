{"version": 3, "file": "useAuth.js", "sources": ["composables/useAuth.js"], "sourcesContent": ["// 用户认证组合式函数\nimport { ref, computed } from 'vue'\nimport { crypto } from '@/utils/crypto.js'\n\n/**\n * 用户认证状态管理\n */\nexport function useAuth() {\n\t// 响应式状态\n\tconst isLoggedIn = ref(false)\n\tconst userInfo = ref(null)\n\tconst openid = ref('')\n\tconst isLogging = ref(false)\n\tconst loginError = ref(null)\n\t\n\t// 计算属性\n\tconst isAuthenticated = computed(() => isLoggedIn.value && openid.value)\n\tconst needLogin = computed(() => !isAuthenticated.value)\n\t\n\t/**\n\t * 检查登录状态\n\t */\n\tconst checkLoginStatus = () => {\n\t\ttry {\n\t\t\t// 从本地存储获取用户信息\n\t\t\tconst storedOpenid = uni.getStorageSync('user_openid')\n\t\t\tconst storedUserInfo = uni.getStorageSync('user_info')\n\t\t\t\n\t\t\tif (storedOpenid && storedUserInfo) {\n\t\t\t\topenid.value = storedOpenid\n\t\t\t\tuserInfo.value = JSON.parse(storedUserInfo)\n\t\t\t\tisLoggedIn.value = true\n\t\t\t\tconsole.log('用户已登录:', openid.value)\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('用户未登录')\n\t\t\treturn false\n\t\t} catch (error) {\n\t\t\tconsole.error('检查登录状态失败:', error)\n\t\t\treturn false\n\t\t}\n\t}\n\t\n\t/**\n\t * 微信小程序登录\n\t */\n\tconst wxLogin = async () => {\n\t\tif (isLogging.value) return\n\t\t\n\t\tisLogging.value = true\n\t\tloginError.value = null\n\t\t\n\t\ttry {\n\t\t\tconsole.log('开始微信登录...')\n\t\t\t\n\t\t\t// 1. 调用wx.login获取code\n\t\t\tconst loginResult = await new Promise((resolve, reject) => {\n\t\t\t\tuni.login({\n\t\t\t\t\tprovider: 'weixin',\n\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\tfail: reject\n\t\t\t\t})\n\t\t\t})\n\t\t\t\n\t\t\tif (!loginResult.code) {\n\t\t\t\tthrow new Error('获取登录凭证失败')\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('获取到登录code:', loginResult.code)\n\t\t\t\n\t\t\t// 2. 向后台发送code获取openid\n\t\t\tconst openidResult = await getOpenidFromServer(loginResult.code)\n\t\t\t\n\t\t\tif (openidResult.success && openidResult.openid) {\n\t\t\t\t// 3. 保存用户信息\n\t\t\t\topenid.value = openidResult.openid\n\t\t\t\tuserInfo.value = openidResult.userInfo || {}\n\t\t\t\tisLoggedIn.value = true\n\t\t\t\t\n\t\t\t\t// 4. 存储到本地\n\t\t\t\tuni.setStorageSync('user_openid', openid.value)\n\t\t\t\tuni.setStorageSync('user_info', JSON.stringify(userInfo.value))\n\t\t\t\t\n\t\t\t\tconsole.log('登录成功:', openid.value)\n\t\t\t\t\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '登录成功',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\treturn true\n\t\t\t} else {\n\t\t\t\tthrow new Error(openidResult.message || '登录失败')\n\t\t\t}\n\t\t\t\n\t\t} catch (error) {\n\t\t\tconsole.error('登录失败:', error)\n\t\t\tloginError.value = error.message || '登录失败'\n\t\t\t\n\t\t\tuni.showToast({\n\t\t\t\ttitle: loginError.value,\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t\t\n\t\t\treturn false\n\t\t} finally {\n\t\t\tisLogging.value = false\n\t\t}\n\t}\n\t\n\t/**\n\t * 向服务器发送code获取openid\n\t * @param {string} code - 微信登录code\n\t * @returns {Promise<Object>} 服务器响应\n\t */\n\tconst getOpenidFromServer = async (code) => {\n\t\ttry {\n\t\t\t// 准备请求参数\n\t\t\tconst requestData = {\n\t\t\t\tcode: code\n\t\t\t}\n\t\t\t\n\t\t\t// 使用混合加密\n\t\t\tconst encryptedRequest = crypto.encryptRequest(requestData)\n\t\t\t\n\t\t\tconsole.log('发送登录请求到服务器...')\n\t\t\t\n\t\t\t// 发送请求到后台\n\t\t\tconst response = await uni.request({\n\t\t\t\turl: 'http://hd.peixue100.cn/ylhapp/bankNum/jscode2session', // 替换为实际的API地址\n\t\t\t\tmethod: 'POST',\n\t\t\t\tdata: encryptedRequest,\n\t\t\t\theader: {\n\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t}\n\t\t\t})\n\t\t\t\n\t\t\tif (response.statusCode === 200 && response.data) {\n\t\t\t\t// 解密响应数据\n\t\t\t\tconst decryptedData = crypto.decryptResponse(response.data.data, encryptedRequest.aesKey)\n\t\t\t\t\n\t\t\t\treturn decryptedData\n\t\t\t} else {\n\t\t\t\tthrow new Error('服务器响应异常')\n\t\t\t}\n\t\t\t\n\t\t} catch (error) {\n\t\t\tconsole.error('获取openid失败:', error)\n\t\t\t\n\t\t\t// 如果是网络错误，返回模拟数据用于开发测试\n\t\t\tif (error.errMsg && error.errMsg.includes('request:fail')) {\n\t\t\t\tconsole.log('网络请求失败，使用模拟数据')\n\t\t\t\treturn {\n\t\t\t\t\tsuccess: true,\n\t\t\t\t\topenid: `mock_openid_${Date.now()}`,\n\t\t\t\t\tuserInfo: {\n\t\t\t\t\t\tnickname: '测试用户',\n\t\t\t\t\t\tavatar: ''\n\t\t\t\t\t},\n\t\t\t\t\tmessage: '登录成功（模拟）'\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tthrow error\n\t\t}\n\t}\n\t\n\t/**\n\t * 退出登录\n\t */\n\tconst logout = () => {\n\t\ttry {\n\t\t\t// 清除状态\n\t\t\tisLoggedIn.value = false\n\t\t\tuserInfo.value = null\n\t\t\topenid.value = ''\n\t\t\tloginError.value = null\n\t\t\t\n\t\t\t// 清除本地存储\n\t\t\tuni.removeStorageSync('user_openid')\n\t\t\tuni.removeStorageSync('user_info')\n\t\t\t\n\t\t\tconsole.log('用户已退出登录')\n\t\t\t\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '已退出登录',\n\t\t\t\ticon: 'success'\n\t\t\t})\n\t\t} catch (error) {\n\t\t\tconsole.error('退出登录失败:', error)\n\t\t}\n\t}\n\t\n\t/**\n\t * 确保用户已登录\n\t * @returns {Promise<boolean>} 是否已登录\n\t */\n\tconst ensureLogin = async () => {\n\t\t// 先检查本地登录状态\n\t\tif (checkLoginStatus()) {\n\t\t\treturn true\n\t\t}\n\t\t\n\t\t// 如果未登录，尝试自动登录\n\t\treturn await wxLogin()\n\t}\n\t\n\t/**\n\t * 获取用户授权信息\n\t */\n\tconst getUserProfile = async () => {\n\t\ttry {\n\t\t\tconst userProfile = await new Promise((resolve, reject) => {\n\t\t\t\tuni.getUserProfile({\n\t\t\t\t\tdesc: '用于完善用户资料',\n\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\tfail: reject\n\t\t\t\t})\n\t\t\t})\n\t\t\t\n\t\t\t// 更新用户信息\n\t\t\tif (userProfile.userInfo) {\n\t\t\t\tuserInfo.value = {\n\t\t\t\t\t...userInfo.value,\n\t\t\t\t\t...userProfile.userInfo\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 保存到本地\n\t\t\t\tuni.setStorageSync('user_info', JSON.stringify(userInfo.value))\n\t\t\t}\n\t\t\t\n\t\t\treturn userProfile.userInfo\n\t\t} catch (error) {\n\t\t\tconsole.error('获取用户信息失败:', error)\n\t\t\tthrow error\n\t\t}\n\t}\n\t\n\t// 返回响应式数据和方法\n\treturn {\n\t\t// 响应式状态\n\t\tisLoggedIn,\n\t\tuserInfo,\n\t\topenid,\n\t\tisLogging,\n\t\tloginError,\n\t\t\n\t\t// 计算属性\n\t\tisAuthenticated,\n\t\tneedLogin,\n\t\t\n\t\t// 方法\n\t\tcheckLoginStatus,\n\t\twxLogin,\n\t\tlogout,\n\t\tensureLogin,\n\t\tgetUserProfile\n\t}\n}\n"], "names": ["ref", "computed", "uni", "crypto"], "mappings": ";;;AAOO,SAAS,UAAU;AAEzB,QAAM,aAAaA,cAAG,IAAC,KAAK;AAC5B,QAAM,WAAWA,cAAG,IAAC,IAAI;AACzB,QAAM,SAASA,cAAG,IAAC,EAAE;AACrB,QAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,QAAM,aAAaA,cAAG,IAAC,IAAI;AAG3B,QAAM,kBAAkBC,cAAAA,SAAS,MAAM,WAAW,SAAS,OAAO,KAAK;AACvE,QAAM,YAAYA,cAAQ,SAAC,MAAM,CAAC,gBAAgB,KAAK;AAKvD,QAAM,mBAAmB,MAAM;AAC9B,QAAI;AAEH,YAAM,eAAeC,cAAAA,MAAI,eAAe,aAAa;AACrD,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,WAAW;AAErD,UAAI,gBAAgB,gBAAgB;AACnC,eAAO,QAAQ;AACf,iBAAS,QAAQ,KAAK,MAAM,cAAc;AAC1C,mBAAW,QAAQ;AACnBA,sBAAY,MAAA,MAAA,OAAA,gCAAA,UAAU,OAAO,KAAK;AAClC,eAAO;AAAA,MACP;AAEDA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,OAAO;AACnB,aAAO;AAAA,IACP,SAAQ,OAAO;AACfA,oBAAAA,MAAc,MAAA,SAAA,gCAAA,aAAa,KAAK;AAChC,aAAO;AAAA,IACP;AAAA,EACD;AAKD,QAAM,UAAU,YAAY;AAC3B,QAAI,UAAU;AAAO;AAErB,cAAU,QAAQ;AAClB,eAAW,QAAQ;AAEnB,QAAI;AACHA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,WAAW;AAGvB,YAAM,cAAc,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC1DA,sBAAAA,MAAI,MAAM;AAAA,UACT,UAAU;AAAA,UACV,SAAS;AAAA,UACT,MAAM;AAAA,QACX,CAAK;AAAA,MACL,CAAI;AAED,UAAI,CAAC,YAAY,MAAM;AACtB,cAAM,IAAI,MAAM,UAAU;AAAA,MAC1B;AAEDA,oBAAY,MAAA,MAAA,OAAA,gCAAA,cAAc,YAAY,IAAI;AAG1C,YAAM,eAAe,MAAM,oBAAoB,YAAY,IAAI;AAE/D,UAAI,aAAa,WAAW,aAAa,QAAQ;AAEhD,eAAO,QAAQ,aAAa;AAC5B,iBAAS,QAAQ,aAAa,YAAY,CAAE;AAC5C,mBAAW,QAAQ;AAGnBA,sBAAAA,MAAI,eAAe,eAAe,OAAO,KAAK;AAC9CA,sBAAG,MAAC,eAAe,aAAa,KAAK,UAAU,SAAS,KAAK,CAAC;AAE9DA,sBAAY,MAAA,MAAA,OAAA,gCAAA,SAAS,OAAO,KAAK;AAEjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACX,CAAK;AAED,eAAO;AAAA,MACX,OAAU;AACN,cAAM,IAAI,MAAM,aAAa,WAAW,MAAM;AAAA,MAC9C;AAAA,IAED,SAAQ,OAAO;AACfA,oBAAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,KAAK;AAC5B,iBAAW,QAAQ,MAAM,WAAW;AAEpCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,WAAW;AAAA,QAClB,MAAM;AAAA,MACV,CAAI;AAED,aAAO;AAAA,IACV,UAAY;AACT,gBAAU,QAAQ;AAAA,IAClB;AAAA,EACD;AAOD,QAAM,sBAAsB,OAAO,SAAS;AAC3C,QAAI;AAEH,YAAM,cAAc;AAAA,QACnB;AAAA,MACA;AAGD,YAAM,mBAAmBC,aAAAA,OAAO,eAAe,WAAW;AAE1DD,oBAAAA,MAAA,MAAA,OAAA,iCAAY,eAAe;AAG3B,YAAM,WAAW,MAAMA,cAAG,MAAC,QAAQ;AAAA,QAClC,KAAK;AAAA;AAAA,QACL,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,UACP,gBAAgB;AAAA,QAChB;AAAA,MACL,CAAI;AAED,UAAI,SAAS,eAAe,OAAO,SAAS,MAAM;AAEjD,cAAM,gBAAgBC,aAAM,OAAC,gBAAgB,SAAS,KAAK,MAAM,iBAAiB,MAAM;AAExF,eAAO;AAAA,MACX,OAAU;AACN,cAAM,IAAI,MAAM,SAAS;AAAA,MACzB;AAAA,IAED,SAAQ,OAAO;AACfD,oBAAAA,MAAc,MAAA,SAAA,iCAAA,eAAe,KAAK;AAGlC,UAAI,MAAM,UAAU,MAAM,OAAO,SAAS,cAAc,GAAG;AAC1DA,sBAAAA,oDAAY,eAAe;AAC3B,eAAO;AAAA,UACN,SAAS;AAAA,UACT,QAAQ,eAAe,KAAK,IAAK,CAAA;AAAA,UACjC,UAAU;AAAA,YACT,UAAU;AAAA,YACV,QAAQ;AAAA,UACR;AAAA,UACD,SAAS;AAAA,QACT;AAAA,MACD;AAED,YAAM;AAAA,IACN;AAAA,EACD;AAKD,QAAM,SAAS,MAAM;AACpB,QAAI;AAEH,iBAAW,QAAQ;AACnB,eAAS,QAAQ;AACjB,aAAO,QAAQ;AACf,iBAAW,QAAQ;AAGnBA,oBAAG,MAAC,kBAAkB,aAAa;AACnCA,oBAAG,MAAC,kBAAkB,WAAW;AAEjCA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,SAAS;AAErBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAI;AAAA,IACD,SAAQ,OAAO;AACfA,oBAAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAAA,IAC9B;AAAA,EACD;AAMD,QAAM,cAAc,YAAY;AAE/B,QAAI,iBAAgB,GAAI;AACvB,aAAO;AAAA,IACP;AAGD,WAAO,MAAM,QAAS;AAAA,EACtB;AAKD,QAAM,iBAAiB,YAAY;AAClC,QAAI;AACH,YAAM,cAAc,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC1DA,sBAAAA,MAAI,eAAe;AAAA,UAClB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,MAAM;AAAA,QACX,CAAK;AAAA,MACL,CAAI;AAGD,UAAI,YAAY,UAAU;AACzB,iBAAS,QAAQ;AAAA,UAChB,GAAG,SAAS;AAAA,UACZ,GAAG,YAAY;AAAA,QACf;AAGDA,sBAAG,MAAC,eAAe,aAAa,KAAK,UAAU,SAAS,KAAK,CAAC;AAAA,MAC9D;AAED,aAAO,YAAY;AAAA,IACnB,SAAQ,OAAO;AACfA,oBAAAA,MAAc,MAAA,SAAA,iCAAA,aAAa,KAAK;AAChC,YAAM;AAAA,IACN;AAAA,EACD;AAGD,SAAO;AAAA;AAAA,IAEN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACA;AACF;;"}