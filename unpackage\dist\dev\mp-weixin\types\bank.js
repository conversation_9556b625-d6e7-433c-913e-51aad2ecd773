"use strict";
const BANK_TYPES = {
  ICBC: "icbc",
  // 工商银行
  ABC: "abc",
  // 农业银行
  BOC: "boc",
  // 中国银行
  CCB: "ccb",
  // 建设银行
  BOCOM: "bocom",
  // 交通银行
  CMB: "cmb",
  // 招商银行
  CITIC: "citic",
  // 中信银行
  CEB: "ceb",
  // 光大银行
  CMBC: "cmbc",
  // 民生银行
  CIB: "cib",
  // 兴业银行
  SPDB: "spdb",
  // 浦发银行
  PAB: "pab",
  // 平安银行
  HXB: "hxb",
  // 华夏银行
  GDB: "gdb",
  // 广发银行
  PSBC: "psbc"
  // 邮储银行
};
const BANK_SHORT_NAMES = {
  "工行": BANK_TYPES.ICBC,
  "农行": BANK_TYPES.ABC,
  "中行": BANK_TYPES.BOC,
  "建行": BANK_TYPES.CCB,
  "交行": BANK_TYPES.BOCOM,
  "招行": BANK_TYPES.CMB,
  "中信": BANK_TYPES.CITIC,
  "光大": BANK_TYPES.CEB,
  "民生": BANK_TYPES.CMBC,
  "兴业": BANK_TYPES.CIB,
  "浦发": BANK_TYPES.SPDB,
  "平安": BANK_TYPES.PAB,
  "华夏": BANK_TYPES.HXB,
  "广发": BANK_TYPES.GDB,
  "邮储": BANK_TYPES.PSBC
};
const SEARCH_TYPES = {
  KEYWORD: "keyword",
  // 关键字搜索
  BANK_CODE: "bank_code",
  // 联行号搜索
  BANK_NAME: "bank_name",
  // 银行名称搜索
  REGION: "region"
  // 地区搜索
};
function detectSearchType(keyword) {
  if (!keyword)
    return SEARCH_TYPES.KEYWORD;
  if (/^\d{12}$/.test(keyword)) {
    return SEARCH_TYPES.BANK_CODE;
  }
  if (Object.keys(BANK_SHORT_NAMES).some((name) => keyword.includes(name))) {
    return SEARCH_TYPES.BANK_NAME;
  }
  const regionKeywords = ["北京", "上海", "广州", "深圳", "杭州", "南京", "武汉", "成都", "重庆", "西安"];
  if (regionKeywords.some((region) => keyword.includes(region))) {
    return SEARCH_TYPES.REGION;
  }
  return SEARCH_TYPES.KEYWORD;
}
exports.SEARCH_TYPES = SEARCH_TYPES;
exports.detectSearchType = detectSearchType;
//# sourceMappingURL=../../.sourcemap/mp-weixin/types/bank.js.map
