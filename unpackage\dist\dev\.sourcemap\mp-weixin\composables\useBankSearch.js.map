{"version": 3, "file": "useBankSearch.js", "sources": ["composables/useBankSearch.js"], "sourcesContent": ["// Vue 3 组合式函数 - 银行查询逻辑\nimport { ref, computed } from 'vue'\nimport { BankCodeAPI } from '@/utils/api.js'\n\n/**\n * 银行查询组合式函数\n * @returns {Object} 查询相关的响应式数据和方法\n */\nexport function useBankSearch() {\n\t// 响应式状态\n\tconst searchKeyword = ref('')\n\tconst searchResults = ref([])\n\tconst isLoading = ref(false)\n\tconst error = ref(null)\n\t\n\t// 计算属性\n\tconst hasResults = computed(() => searchResults.value.length > 0)\n\tconst showNoResult = computed(() => !isLoading.value && !hasResults.value && searchKeyword.value.trim() !== '')\n\tconst isSearchDisabled = computed(() => isLoading.value || searchKeyword.value.trim() === '')\n\t\n\t/**\n\t * 执行搜索\n\t */\n\tconst executeSearch = async () => {\n\t\tif (!searchKeyword.value.trim()) {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '请输入搜索关键字',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t\treturn\n\t\t}\n\n\t\tisLoading.value = true\n\t\terror.value = null\n\t\tsearchResults.value = []\n\n\t\ttry {\n\t\t\tconst result = await BankCodeAPI.searchBankCode(searchKeyword.value)\n\t\t\tsearchResults.value = result || []\n\t\t\t\n\t\t\tif (result && result.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '未找到相关结果',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t} catch (err) {\n\t\t\terror.value = err.message || '查询失败'\n\t\t\tconsole.error('查询失败:', err)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '查询失败，请重试',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t} finally {\n\t\t\tisLoading.value = false\n\t\t}\n\t}\n\n\t/**\n\t * 从剪贴板粘贴并搜索\n\t */\n\tconst pasteAndSearch = async () => {\n\t\ttry {\n\t\t\tconst clipboardData = await uni.getClipboardData()\n\t\t\tif (clipboardData.data && clipboardData.data.trim()) {\n\t\t\t\tsearchKeyword.value = clipboardData.data.trim()\n\t\t\t\tawait executeSearch()\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '剪贴板为空',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t} catch (err) {\n\t\t\tconsole.error('粘贴失败:', err)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '粘贴失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t}\n\n\t/**\n\t * 清除搜索\n\t */\n\tconst clearSearch = () => {\n\t\tsearchKeyword.value = ''\n\t\tsearchResults.value = []\n\t\terror.value = null\n\t}\n\n\t/**\n\t * 选择搜索结果\n\t * @param {Object} item - 选中的银行信息\n\t */\n\tconst selectResult = (item) => {\n\t\tif (!item.code) return\n\t\t\n\t\tuni.setClipboardData({\n\t\t\tdata: item.code,\n\t\t\tsuccess: () => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '联行号已复制',\n\t\t\t\t\ticon: 'success'\n\t\t\t\t})\n\t\t\t},\n\t\t\tfail: () => {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '复制失败',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t})\n\t\t\t}\n\t\t})\n\t}\n\n\t/**\n\t * 重新搜索（刷新）\n\t */\n\tconst refreshSearch = () => {\n\t\tif (searchKeyword.value.trim()) {\n\t\t\texecuteSearch()\n\t\t}\n\t}\n\n\t// 返回响应式数据和方法\n\treturn {\n\t\t// 响应式状态\n\t\tsearchKeyword,\n\t\tsearchResults,\n\t\tisLoading,\n\t\terror,\n\t\t\n\t\t// 计算属性\n\t\thasResults,\n\t\tshowNoResult,\n\t\tisSearchDisabled,\n\t\t\n\t\t// 方法\n\t\texecuteSearch,\n\t\tpasteAndSearch,\n\t\tclearSearch,\n\t\tselectResult,\n\t\trefreshSearch\n\t}\n}\n\n/**\n * 银行代码格式化组合式函数\n */\nexport function useBankCodeFormatter() {\n\t/**\n\t * 格式化银行代码显示\n\t * @param {string} code - 银行代码\n\t * @returns {string} 格式化后的代码\n\t */\n\tconst formatCode = (code) => {\n\t\tif (!code) return ''\n\t\t// 将12位联行号格式化为 XXX-XXX-XXX-XXX 的形式\n\t\treturn code.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{3})/, '$1-$2-$3-$4')\n\t}\n\n\t/**\n\t * 验证银行代码格式\n\t * @param {string} code - 银行代码\n\t * @returns {boolean} 是否为有效格式\n\t */\n\tconst validateCode = (code) => {\n\t\tif (!code) return false\n\t\t// 联行号通常为12位数字\n\t\tconst pattern = /^\\d{12}$/\n\t\treturn pattern.test(code.replace(/[-\\s]/g, ''))\n\t}\n\n\t/**\n\t * 高亮显示搜索关键字\n\t * @param {string} text - 原始文本\n\t * @param {string} keyword - 搜索关键字\n\t * @returns {string} 高亮后的文本\n\t */\n\tconst highlightKeyword = (text, keyword) => {\n\t\tif (!text || !keyword) return text\n\t\tconst regex = new RegExp(`(${keyword})`, 'gi')\n\t\treturn text.replace(regex, '<span class=\"highlight\">$1</span>')\n\t}\n\n\treturn {\n\t\tformatCode,\n\t\tvalidateCode,\n\t\thighlightKeyword\n\t}\n}\n"], "names": ["ref", "computed", "uni", "BankCodeAPI"], "mappings": ";;;AAQO,SAAS,gBAAgB;AAE/B,QAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,QAAM,gBAAgBA,cAAG,IAAC,EAAE;AAC5B,QAAM,YAAYA,cAAG,IAAC,KAAK;AAC3B,QAAM,QAAQA,cAAG,IAAC,IAAI;AAGtB,QAAM,aAAaC,cAAAA,SAAS,MAAM,cAAc,MAAM,SAAS,CAAC;AAChE,QAAM,eAAeA,cAAQ,SAAC,MAAM,CAAC,UAAU,SAAS,CAAC,WAAW,SAAS,cAAc,MAAM,KAAI,MAAO,EAAE;AAC9G,QAAM,mBAAmBA,uBAAS,MAAM,UAAU,SAAS,cAAc,MAAM,KAAM,MAAK,EAAE;AAK5F,QAAM,gBAAgB,YAAY;AACjC,QAAI,CAAC,cAAc,MAAM,QAAQ;AAChCC,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAI;AACD;AAAA,IACA;AAED,cAAU,QAAQ;AAClB,UAAM,QAAQ;AACd,kBAAc,QAAQ,CAAE;AAExB,QAAI;AACH,YAAM,SAAS,MAAMC,UAAAA,YAAY,eAAe,cAAc,KAAK;AACnE,oBAAc,QAAQ,UAAU,CAAE;AAElC,UAAI,UAAU,OAAO,WAAW,GAAG;AAClCD,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACX,CAAK;AAAA,MACD;AAAA,IACD,SAAQ,KAAK;AACb,YAAM,QAAQ,IAAI,WAAW;AAC7BA,oBAAAA,MAAc,MAAA,SAAA,sCAAA,SAAS,GAAG;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAI;AAAA,IACJ,UAAY;AACT,gBAAU,QAAQ;AAAA,IAClB;AAAA,EACD;AAKD,QAAM,iBAAiB,YAAY;AAClC,QAAI;AACH,YAAM,gBAAgB,MAAMA,cAAG,MAAC,iBAAkB;AAClD,UAAI,cAAc,QAAQ,cAAc,KAAK,KAAI,GAAI;AACpD,sBAAc,QAAQ,cAAc,KAAK,KAAM;AAC/C,cAAM,cAAe;AAAA,MACzB,OAAU;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACX,CAAK;AAAA,MACD;AAAA,IACD,SAAQ,KAAK;AACbA,oBAAAA,MAAc,MAAA,SAAA,sCAAA,SAAS,GAAG;AAC1BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAI;AAAA,IACD;AAAA,EACD;AAKD,QAAM,cAAc,MAAM;AACzB,kBAAc,QAAQ;AACtB,kBAAc,QAAQ,CAAE;AACxB,UAAM,QAAQ;AAAA,EACd;AAMD,QAAM,eAAe,CAAC,SAAS;AAC9B,QAAI,CAAC,KAAK;AAAM;AAEhBA,kBAAAA,MAAI,iBAAiB;AAAA,MACpB,MAAM,KAAK;AAAA,MACX,SAAS,MAAM;AACdA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACX,CAAK;AAAA,MACD;AAAA,MACD,MAAM,MAAM;AACXA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACX,CAAK;AAAA,MACD;AAAA,IACJ,CAAG;AAAA,EACD;AAKD,QAAM,gBAAgB,MAAM;AAC3B,QAAI,cAAc,MAAM,QAAQ;AAC/B,oBAAe;AAAA,IACf;AAAA,EACD;AAGD,SAAO;AAAA;AAAA,IAEN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACA;AACF;AAKO,SAAS,uBAAuB;AAMtC,QAAM,aAAa,CAAC,SAAS;AAC5B,QAAI,CAAC;AAAM,aAAO;AAElB,WAAO,KAAK,QAAQ,gCAAgC,aAAa;AAAA,EACjE;AAOD,QAAM,eAAe,CAAC,SAAS;AAC9B,QAAI,CAAC;AAAM,aAAO;AAElB,UAAM,UAAU;AAChB,WAAO,QAAQ,KAAK,KAAK,QAAQ,UAAU,EAAE,CAAC;AAAA,EAC9C;AAQD,QAAM,mBAAmB,CAAC,MAAM,YAAY;AAC3C,QAAI,CAAC,QAAQ,CAAC;AAAS,aAAO;AAC9B,UAAM,QAAQ,IAAI,OAAO,IAAI,OAAO,KAAK,IAAI;AAC7C,WAAO,KAAK,QAAQ,OAAO,mCAAmC;AAAA,EAC9D;AAED,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACA;AACF;;;"}