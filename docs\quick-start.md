# 快速开始指南

## 🚀 项目启动

### 1. 环境准备

- **HBuilderX**: 最新版本
- **微信开发者工具**: 用于小程序调试
- **Node.js**: 14.0.0 或更高版本（可选）

### 2. 项目配置

#### 小程序配置

1. 打开 `manifest.json`
2. 修改小程序AppID：
```json
{
  "mp-weixin": {
    "appid": "你的小程序AppID"
  }
}
```

#### API配置

1. 打开 `utils/request.js`
2. 修改API基础地址：
```javascript
const API_CONFIG = {
  baseURL: 'https://your-api-domain.com/api'
}
```

#### RSA公钥配置

1. 打开 `utils/crypto.js`
2. 替换RSA公钥：
```javascript
static RSA_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
你的RSA公钥内容
-----END PUBLIC KEY-----`
```

### 3. 运行项目

#### 在HBuilderX中运行

1. 用HBuilderX打开项目文件夹
2. 点击菜单：运行 → 运行到小程序模拟器 → 微信开发者工具
3. 首次运行会自动打开微信开发者工具

#### 手动运行

1. 在微信开发者工具中导入项目
2. 选择 `unpackage/dist/dev/mp-weixin` 目录
3. 设置AppID并开始调试

## 🔧 功能测试

### 登录功能测试

1. **清除本地数据**：
   - 微信开发者工具 → 工具 → 清缓存
   - 或在代码中调用：`uni.clearStorageSync()`

2. **测试登录流程**：
   - 重新进入小程序
   - 应该自动弹出登录弹窗
   - 点击"微信授权登录"按钮
   - 查看控制台日志确认登录成功

3. **验证登录状态**：
   - 登录成功后右上角显示用户信息
   - 本地存储中保存了`user_openid`和`user_info`

### 搜索功能测试

1. **基础搜索**：
   - 输入"工商银行"或"工行"
   - 点击搜索按钮
   - 查看返回结果

2. **联行号搜索**：
   - 输入12位联行号：`102100000072`
   - 验证搜索类型自动识别

3. **粘贴功能**：
   - 复制银行信息到剪贴板
   - 点击"粘贴"按钮
   - 验证自动填入并搜索

### 加密功能测试

1. **查看网络请求**：
   - 微信开发者工具 → Network面板
   - 执行搜索操作
   - 确认请求数据已加密

2. **验证加密格式**：
   ```json
   {
     "encryptedData": "AES_加密的数据",
     "encryptedKey": "RSA_加密的密钥"
   }
   ```

## 🐛 常见问题

### 1. 登录失败

**问题**: 点击登录按钮没有反应或报错

**解决方案**:
- 检查小程序AppID是否正确配置
- 确认网络连接正常
- 查看控制台错误信息
- 验证后台登录接口是否正常

### 2. 搜索无结果

**问题**: 搜索时显示"未找到相关结果"

**解决方案**:
- 当前使用模拟数据，只支持特定关键字
- 支持的测试关键字：工行、农行、中行、建行、北京
- 检查API接口是否正常响应

### 3. 加密请求失败

**问题**: 网络请求报错或数据格式异常

**解决方案**:
- 检查RSA公钥配置是否正确
- 确认后台解密逻辑是否匹配
- 验证API接口地址是否正确
- 查看网络请求的具体错误信息

### 4. 页面样式异常

**问题**: 页面布局错乱或样式不正确

**解决方案**:
- 清除微信开发者工具缓存
- 检查CSS样式是否正确加载
- 验证rpx单位是否正确使用
- 确认Vue 3语法是否正确

## 📱 部署发布

### 1. 小程序发布

1. **代码审核**：
   - 确保所有功能正常
   - 检查用户隐私保护
   - 验证数据安全性

2. **提交审核**：
   - 微信开发者工具 → 上传代码
   - 微信公众平台 → 版本管理 → 提交审核

3. **发布上线**：
   - 审核通过后点击发布
   - 用户可在微信中搜索使用

### 2. H5版本部署

1. **构建项目**：
   ```bash
   # 在HBuilderX中
   发行 → H5-手机版
   ```

2. **部署到服务器**：
   - 将`unpackage/dist/build/h5`目录上传到服务器
   - 配置HTTPS和域名
   - 设置正确的路由规则

## 🔒 安全配置

### 1. 生产环境配置

- 使用真实的RSA密钥对
- 配置HTTPS协议
- 设置正确的域名白名单
- 启用请求频率限制

### 2. 隐私保护

- 遵守《个人信息保护法》
- 明确告知用户数据使用目的
- 提供用户数据删除功能
- 定期清理过期数据

## 📞 技术支持

如果遇到问题，可以：

1. 查看项目文档：`docs/` 目录
2. 检查控制台错误信息
3. 参考uni-app官方文档
4. 联系技术支持团队

## 🎯 下一步计划

- [ ] 集成真实的银行数据API
- [ ] 添加用户收藏功能
- [ ] 实现搜索历史记录
- [ ] 优化搜索算法
- [ ] 添加更多银行支持
- [ ] 实现数据缓存机制
