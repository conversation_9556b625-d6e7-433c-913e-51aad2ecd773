"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_crypto = require("../../utils/crypto.js");
const _sfc_main = {
  __name: "crypto",
  setup(__props) {
    const testData = common_vendor.ref("Hello, World! 这是一个测试数据 123456");
    const testResults = common_vendor.ref([]);
    const rsaKeyValid = common_vendor.ref(false);
    const currentAESKey = common_vendor.ref("");
    common_vendor.onMounted(() => {
      initializeTest();
    });
    const initializeTest = () => {
      try {
        rsaKeyValid.value = utils_crypto.CryptoUtil.validateRSAPublicKey(utils_crypto.CryptoUtil.getRSAPublicKey());
        currentAESKey.value = utils_crypto.CryptoUtil.generateAESKey();
        addTestResult("初始化", true, "测试环境初始化成功");
      } catch (error) {
        addTestResult("初始化", false, "测试环境初始化失败: " + error.message);
      }
    };
    const testAESEncryption = async () => {
      try {
        const data = testData.value || "默认测试数据";
        const key = utils_crypto.CryptoUtil.generateAESKey();
        const encrypted = utils_crypto.CryptoUtil.aesEncrypt(data, key);
        addTestResult(
          "AES加密",
          true,
          "数据加密成功（包含IV）",
          `加密结果: ${encrypted.substring(0, 50)}...`
        );
        const isBase64 = utils_crypto.CryptoUtil.isBase64(encrypted);
        addTestResult(
          "Base64格式验证",
          isBase64,
          isBase64 ? "加密结果为有效的Base64格式" : "加密结果不是有效的Base64格式"
        );
        const encryptedBytes = common_vendor.CryptoJS.enc.Base64.parse(encrypted);
        const hasValidLength = encryptedBytes.sigBytes >= 16;
        addTestResult(
          "IV长度验证",
          hasValidLength,
          hasValidLength ? `数据长度${encryptedBytes.sigBytes}字节，包含16字节IV` : "数据长度不足，无法包含IV"
        );
        const decrypted = utils_crypto.CryptoUtil.aesDecrypt(encrypted, key);
        const success = decrypted === data;
        addTestResult(
          "AES解密",
          success,
          success ? "数据解密成功，与原数据一致" : "数据解密失败，与原数据不一致",
          success ? `原数据: ${data}` : `解密结果: ${decrypted}`
        );
        if (success) {
          const ivHex = encryptedBytes.toString(common_vendor.CryptoJS.enc.Hex).substring(0, 32);
          addTestResult(
            "IV提取详情",
            true,
            "成功提取IV并解密",
            `提取的IV (前16字节): ${ivHex}`
          );
        }
      } catch (error) {
        addTestResult("AES加密解密", false, "测试失败: " + error.message);
      }
    };
    const testIVExtraction = async () => {
      try {
        const testData2 = "Hello, World! 这是IV提取测试数据 🔐";
        const key = utils_crypto.CryptoUtil.generateAESKey();
        addTestResult("IV提取测试开始", true, `测试数据: ${testData2}`);
        const encrypted = utils_crypto.CryptoUtil.aesEncrypt(testData2, key);
        addTestResult(
          "步骤1: 加密",
          true,
          "数据加密完成",
          `加密结果长度: ${encrypted.length} 字符`
        );
        const encryptedBytes = common_vendor.CryptoJS.enc.Base64.parse(encrypted);
        addTestResult(
          "步骤2: Base64解析",
          true,
          "成功解析Base64数据",
          `总字节数: ${encryptedBytes.sigBytes}`
        );
        const ivBytes = common_vendor.CryptoJS.lib.WordArray.create(encryptedBytes.words.slice(0, 4));
        ivBytes.sigBytes = 16;
        const ivHex = ivBytes.toString(common_vendor.CryptoJS.enc.Hex);
        addTestResult(
          "步骤3: IV提取",
          true,
          "成功提取IV",
          `IV (Hex): ${ivHex}`
        );
        const actualDataBytes = common_vendor.CryptoJS.lib.WordArray.create(
          encryptedBytes.words.slice(4),
          encryptedBytes.sigBytes - 16
        );
        addTestResult(
          "步骤4: 数据提取",
          true,
          "成功提取加密数据",
          `数据字节数: ${actualDataBytes.sigBytes}`
        );
        const decrypted = common_vendor.CryptoJS.AES.decrypt(
          { ciphertext: actualDataBytes },
          common_vendor.CryptoJS.enc.Utf8.parse(key),
          {
            iv: ivBytes,
            mode: common_vendor.CryptoJS.mode.CBC,
            padding: common_vendor.CryptoJS.pad.Pkcs7
          }
        );
        const decryptedText = decrypted.toString(common_vendor.CryptoJS.enc.Utf8);
        const success = decryptedText === testData2;
        addTestResult(
          "步骤5: 手动解密",
          success,
          success ? "手动解密成功！" : "手动解密失败",
          `解密结果: ${decryptedText}`
        );
        const utilDecrypted = utils_crypto.CryptoUtil.aesDecrypt(encrypted, key);
        const utilSuccess = utilDecrypted === testData2;
        addTestResult(
          "步骤6: 工具类解密",
          utilSuccess,
          utilSuccess ? "工具类解密成功！" : "工具类解密失败",
          `解密结果: ${utilDecrypted}`
        );
        const consistent = decryptedText === utilDecrypted;
        addTestResult(
          "步骤7: 一致性验证",
          consistent,
          consistent ? "手动解密与工具类解密结果一致" : "两种解密方法结果不一致",
          consistent ? "✅ 测试通过" : "❌ 测试失败"
        );
      } catch (error) {
        addTestResult("IV提取测试", false, "测试失败: " + error.message);
      }
    };
    const testRSAEncryption = async () => {
      try {
        const data = JSON.stringify({ aesKey: currentAESKey.value });
        const encrypted = utils_crypto.CryptoUtil.rsaEncrypt(data);
        addTestResult("RSA加密", true, "RSA加密成功", encrypted.substring(0, 50) + "...");
      } catch (error) {
        addTestResult("RSA加密", false, "测试失败: " + error.message);
      }
    };
    const testHybridEncryption = async () => {
      try {
        const data = {
          message: testData.value || "默认测试数据",
          timestamp: Date.now(),
          userId: "test-user-123"
        };
        const encryptedRequest = utils_crypto.CryptoUtil.encryptRequest(data);
        addTestResult(
          "混合加密",
          true,
          "混合加密成功",
          `加密数据长度: ${encryptedRequest.encryptedData.length}, 密钥长度: ${encryptedRequest.encryptedKey.length}`
        );
        const mockResponse = JSON.stringify({
          success: true,
          message: "这是服务器返回的加密响应",
          data: { result: "test-result" }
        });
        const encryptedResponse = utils_crypto.CryptoUtil.aesEncrypt(mockResponse, encryptedRequest.aesKey);
        const decryptedResponse = utils_crypto.CryptoUtil.decryptResponse(encryptedResponse, encryptedRequest.aesKey);
        addTestResult("响应解密", true, "响应解密成功", JSON.stringify(decryptedResponse));
      } catch (error) {
        addTestResult("混合加密", false, "测试失败: " + error.message);
      }
    };
    const testFullProcess = async () => {
      try {
        const success = utils_crypto.CryptoUtil.testEncryption();
        addTestResult(
          "完整流程测试",
          success,
          success ? "所有加密功能测试通过" : "加密功能测试失败"
        );
      } catch (error) {
        addTestResult("完整流程测试", false, "测试失败: " + error.message);
      }
    };
    const generateTestData = () => {
      const samples = [
        "Hello, World! 测试数据",
        '{"name":"张三","age":25,"city":"北京"}',
        "这是一个包含中文的测试字符串，用于验证加密功能是否正常工作。",
        "1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",
        "特殊字符测试: !@#$%^&*()_+-=[]{}|;:,.<>?"
      ];
      testData.value = samples[Math.floor(Math.random() * samples.length)];
    };
    const clearTestData = () => {
      testData.value = "";
    };
    const generateNewKeys = async () => {
      try {
        currentAESKey.value = utils_crypto.CryptoUtil.generateAESKey();
        addTestResult("密钥生成", true, "新密钥生成成功");
      } catch (error) {
        addTestResult("密钥生成", false, "密钥生成失败: " + error.message);
      }
    };
    const addTestResult = (title, success, message, data = null) => {
      testResults.value.unshift({
        title,
        success,
        message,
        data,
        timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString()
      });
      if (testResults.value.length > 10) {
        testResults.value = testResults.value.slice(0, 10);
      }
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(testAESEncryption),
        b: common_vendor.o(testIVExtraction),
        c: common_vendor.o(testRSAEncryption),
        d: common_vendor.o(testHybridEncryption),
        e: common_vendor.o(testFullProcess),
        f: testData.value,
        g: common_vendor.o(($event) => testData.value = $event.detail.value),
        h: common_vendor.o(generateTestData),
        i: common_vendor.o(clearTestData),
        j: common_vendor.f(testResults.value, (result, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(result.title),
            b: common_vendor.t(result.success ? "✓ 成功" : "✗ 失败"),
            c: common_vendor.n(result.success ? "success" : "error"),
            d: common_vendor.t(result.message),
            e: result.data
          }, result.data ? {
            f: common_vendor.t(result.data)
          } : {}, {
            g: index
          });
        }),
        k: common_vendor.t(rsaKeyValid.value ? "有效" : "无效"),
        l: common_vendor.n(rsaKeyValid.value ? "valid" : "invalid"),
        m: common_vendor.t(currentAESKey.value || "未生成"),
        n: common_vendor.o(generateNewKeys)
      };
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/test/crypto.js.map
