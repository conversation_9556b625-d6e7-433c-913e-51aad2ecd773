{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "D:/Hbuilder/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部标题栏 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<text class=\"header-title\">AI联行助手</text>\r\n\t\t\t<view class=\"header-actions\">\r\n\t\t\t\t<!-- <text class=\"action-btn\">•••</text>\r\n\t\t\t\t<text class=\"action-btn\">—</text>\r\n\t\t\t\t<text class=\"action-btn\">⊙</text> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 主要内容区域 -->\r\n\t\t<view class=\"content\">\r\n\t\t\t<!-- 二维码和网站信息 -->\r\n\t\t\t<view class=\"qr-section\">\r\n\t\t\t\t<view class=\"qr-code\">\r\n\t\t\t\t\t<image class=\"qr-image\" src=\"/static/logo.jpg\" mode=\"aspectFit\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"website-info\">\r\n\t\t\t\t\t<text class=\"website-title\">AI联行助手</text>\r\n\t\t\t\t\t<text class=\"website-url\">依托AI大模型能力，对各大银行主页公开的联行号提供查询，精确到支行点</text>\r\n\t\t\t\t\t<text class=\"website-url\"></text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 搜索区域 -->\r\n\t\t\t<view class=\"search-section\">\r\n\t\t\t\t<view class=\"search-input-wrapper\">\r\n\t\t\t\t\t<input\r\n\t\t\t\t\t\tclass=\"search-input\"\r\n\t\t\t\t\t\tv-model=\"searchKeyword\"\r\n\t\t\t\t\t\tplaceholder=\"工行北京王府井支行\"\r\n\t\t\t\t\t\tplaceholder-style=\"color: #999;\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<text class=\"search-tip\">请输入银行/地区名/关键字，例如：工行北京</text>\r\n\t\t\t\t<text class=\"search-tip\">也可以输入12位联行号，例如：10210002503</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 按钮区域 -->\r\n\t\t\t<view class=\"button-section\">\r\n\t\t\t\t<button\r\n\t\t\t\t\tclass=\"action-button search-btn\"\r\n\t\t\t\t\t:disabled=\"isSearchDisabled\"\r\n\t\t\t\t\t:class=\"{ disabled: isSearchDisabled }\"\r\n\t\t\t\t\t@click=\"handleSearch\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{ isLoading ? '搜索中...' : '搜索' }}\r\n\t\t\t\t</button>\r\n\t\t\t\t<button\r\n\t\t\t\t\tclass=\"action-button paste-btn\"\r\n\t\t\t\t\t:disabled=\"isLoading\"\r\n\t\t\t\t\t:class=\"{ disabled: isLoading }\"\r\n\t\t\t\t\t@click=\"handlePaste\"\r\n\t\t\t\t>\r\n\t\t\t\t\t粘贴\r\n\t\t\t\t</button>\r\n\t\t\t\t<button\r\n\t\t\t\t\tclass=\"action-button clear-btn\"\r\n\t\t\t\t\t@click=\"handleClear\"\r\n\t\t\t\t>\r\n\t\t\t\t\t清除\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 查询结果区域 -->\r\n\t\t\t<view class=\"result-section\" v-if=\"hasResults\">\r\n\t\t\t\t<view class=\"result-header\">\r\n\t\t\t\t\t<text class=\"result-title\">查询结果</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"result-list\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"result-item\"\r\n\t\t\t\t\t\tv-for=\"(item, index) in searchResults\"\r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t@click=\"selectResult(item)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"result-code\">{{formatBankCodeDisplay(item.code)}}</view>\r\n\t\t\t\t\t\t<view class=\"result-info\">\r\n\t\t\t\t\t\t\t<text class=\"bank-name\">{{item.bankName}}</text>\r\n\t\t\t\t\t\t\t<text class=\"branch-name\">{{item.branchName}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"result-distance\" v-if=\"item.distance\">\r\n\t\t\t\t\t\t\t<text class=\"distance-text\">距离{{item.distance}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 加载状态 -->\r\n\t\t\t<view class=\"loading\" v-if=\"isLoading\">\r\n\t\t\t\t<text class=\"loading-text\">查询中...</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 无结果提示 -->\r\n\t\t\t<view class=\"no-result\" v-if=\"showNoResult\">\r\n\t\t\t\t<text class=\"no-result-text\">未找到相关结果</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\n\timport { onMounted } from 'vue'\r\n\timport { useBankSearch, useBankCodeFormatter } from '@/composables/useBankSearch.js'\r\n\r\n\t// 使用组合式函数\r\n\tconst {\r\n\t\tsearchKeyword,\r\n\t\tsearchResults,\r\n\t\tisLoading,\r\n\t\thasResults,\r\n\t\tshowNoResult,\r\n\t\tisSearchDisabled,\r\n\t\texecuteSearch,\r\n\t\tpasteAndSearch,\r\n\t\tclearSearch,\r\n\t\tselectResult\r\n\t} = useBankSearch()\r\n\r\n\tconst { formatCode } = useBankCodeFormatter()\r\n\r\n\t// 页面加载时的初始化操作\r\n\tonMounted(() => {\r\n\t\tconsole.log('银行联行号查询页面已加载')\r\n\t})\r\n\r\n\t// 处理函数（为了保持模板中的命名一致）\r\n\tconst handleSearch = executeSearch\r\n\tconst handlePaste = pasteAndSearch\r\n\tconst handleClear = clearSearch\r\n\tconst formatBankCodeDisplay = formatCode\r\n</script>\r\n\r\n<style>\r\n\t.container {\r\n\t\tmin-height: 100vh;\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n\r\n\t/* 顶部标题栏 */\r\n\t.header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-bottom: 1rpx solid #e0e0e0;\r\n\t}\r\n\r\n\t.header-title {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.header-actions {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t}\r\n\r\n\t.action-btn {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #666666;\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t/* 主要内容区域 */\r\n\t.content {\r\n\t\tpadding: 30rpx;\r\n\t}\r\n\r\n\t/* 二维码和网站信息区域 */\r\n\t.qr-section {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.qr-code {\r\n\t\twidth: 120rpx;\r\n\t\theight: 120rpx;\r\n\t\tmargin-right: 30rpx;\r\n\t}\r\n\r\n\t.qr-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.website-info {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.website-title {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.website-name {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 48rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #4CAF50;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.website-url {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t/* 搜索区域 */\r\n\t.search-section {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.search-input-wrapper {\r\n\t\tposition: relative;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.search-input {\r\n\t\twidth: 100%;\r\n\t\theight: 80rpx;\r\n\t\tborder: 2rpx solid #e0e0e0;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tbackground-color: #fafafa;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.search-input:focus {\r\n\t\tborder-color: #4CAF50;\r\n\t\tbackground-color: #ffffff;\r\n\t}\r\n\r\n\t.search-tip {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\tline-height: 1.4;\r\n\t}\r\n\r\n\t/* 按钮区域 */\r\n\t.button-section {\r\n\t\tdisplay: flex;\r\n\t\tgap: 20rpx;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.action-button {\r\n\t\tflex: 1;\r\n\t\theight: 80rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tborder: none;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.search-btn {\r\n\t\tbackground-color: #4CAF50;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.paste-btn {\r\n\t\tbackground-color: #2196F3;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.clear-btn {\r\n\t\tbackground-color: #FF9800;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\r\n\t.action-button:active {\r\n\t\topacity: 0.8;\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\r\n\t.action-button.disabled {\r\n\t\topacity: 0.5;\r\n\t\tcursor: not-allowed;\r\n\t}\r\n\r\n\t.action-button.disabled:active {\r\n\t\ttransform: none;\r\n\t\topacity: 0.5;\r\n\t}\r\n\r\n\t/* 查询结果区域 */\r\n\t.result-section {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.result-header {\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tborder-bottom: 1rpx solid #e0e0e0;\r\n\t}\r\n\r\n\t.result-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\r\n\r\n\t.result-item {\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1rpx solid #f0f0f0;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 15rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.result-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.result-item:active {\r\n\t\tbackground-color: #f8f9fa;\r\n\t}\r\n\r\n\t.result-code {\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #2196F3;\r\n\t\tfont-family: 'Courier New', monospace;\r\n\t}\r\n\r\n\t.result-info {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 8rpx;\r\n\t}\r\n\r\n\t.bank-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333333;\r\n\t\tfont-weight: 500;\r\n\t\tline-height: 1.4;\r\n\t}\r\n\r\n\t.branch-name {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tline-height: 1.4;\r\n\t}\r\n\r\n\t.result-distance {\r\n\t\tposition: absolute;\r\n\t\ttop: 30rpx;\r\n\t\tright: 30rpx;\r\n\t}\r\n\r\n\t.distance-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999999;\r\n\t\tbackground-color: #f0f0f0;\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t}\r\n\r\n\t/* 加载状态 */\r\n\t.loading {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tpadding: 60rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.loading-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t/* 无结果提示 */\r\n\t.no-result {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tpadding: 60rpx;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.no-result-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t/* 响应式设计 */\r\n\t@media (max-width: 750rpx) {\r\n\t\t.qr-section {\r\n\t\t\tflex-direction: column;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t\t.qr-code {\r\n\t\t\tmargin-right: 0;\r\n\t\t\tmargin-bottom: 20rpx;\r\n\t\t}\r\n\r\n\t\t.button-section {\r\n\t\t\tflex-direction: column;\r\n\t\t}\r\n\r\n\t\t.action-button {\r\n\t\t\tmargin-bottom: 15rpx;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import MiniProgramPage from 'E:/FM93交通之声/2025Project/BankCodeSearch/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useBankSearch", "useBankCodeFormatter", "onMounted", "uni", "MiniProgramPage"], "mappings": ";;;;;;;AA4GC,UAAM;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACA,IAAGA,wCAAe;AAEnB,UAAM,EAAE,WAAY,IAAGC,+CAAsB;AAG7CC,kBAAAA,UAAU,MAAM;AACfC,oBAAAA,MAAY,MAAA,OAAA,gCAAA,cAAc;AAAA,IAC5B,CAAE;AAGD,UAAM,eAAe;AACrB,UAAM,cAAc;AACpB,UAAM,cAAc;AACpB,UAAM,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnI/B,GAAG,WAAWC,SAAe;"}