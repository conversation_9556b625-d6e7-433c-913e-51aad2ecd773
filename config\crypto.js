// 加密配置文件
// 生产环境中应该从安全的配置服务获取这些配置

/**
 * 加密配置
 */
export const CRYPTO_CONFIG = {
	// RSA配置
	RSA: {
		// 密钥长度
		KEY_SIZE: 2048,
		
		// 公钥 - 生产环境应该从服务器获取
		PUBLIC_KEY: `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmWPDFsI9N+4Hsmo2
kwtPENGaJ+9Kiq4Nqscdac0LHfHLTEhPuPnnfYZ+g050Ag4IoS+C6U3MQM1P
gi4IuhL4Ef2awrMJxf4HwuVxtIZy+oiT8SRQU84/jgcpi9Omoi2wKcRc5Bpr
fp4wI2nzb9bS397KsPA0b/HSix+xlCkBCqTyL3HjFOBNSxsU3o0F0DyesomK
G5B6J9Csj8ALWLs/JUsZNfO1tMyHN2invduNPwvn7dETAGCeQ1t6Of986twv
EE8jDxEPQ35mZVF2cDkcvSSb8aJLzlyZS6zzmYF1UsfT+Z5j3lYUIIbVfacR
3V/FYL4qZx+0eYY2VrgWhoEZqQIDAQAB
-----END PUBLIC KEY-----`,
		
		// 算法配置
		ALGORITHM: 'RSA-OAEP',
		HASH: 'SHA-256'
	},
	
	// AES配置
	AES: {
		// 密钥长度（字符数）
		KEY_LENGTH: 32,
		
		// 加密模式
		MODE: 'CBC',
		
		// 填充方式
		PADDING: 'Pkcs5',
		
		// 字符集
		CHARSET: 'UTF-8'
	},
	
	// 请求配置
	REQUEST: {
		// 是否启用加密
		ENABLE_ENCRYPTION: true,
		
		// 加密的请求方法
		ENCRYPTED_METHODS: ['POST', 'PUT', 'PATCH'],
		
		// 需要加密的接口路径
		ENCRYPTED_PATHS: [
			'/api/auth/login',
			'/api/bank/search',
			'/api/user/profile'
		],
		
		// 请求超时时间
		TIMEOUT: 10000
	},
	
	// 开发配置
	DEVELOPMENT: {
		// 是否启用调试日志
		ENABLE_DEBUG_LOG: false,
		
		// 是否启用加密测试
		ENABLE_CRYPTO_TEST: false,
		
		// 是否使用模拟数据
		USE_MOCK_DATA: false,
		
		// 测试模式下的密钥
		TEST_AES_KEY: 'test-aes-key-32-characters-long',
		
		// 是否跳过RSA验证
		SKIP_RSA_VALIDATION: false
	}
}

/**
 * 环境配置
 */
export const ENV_CONFIG = {
	// 开发环境
	development: {
		API_BASE_URL: 'http://hd.peixue100.cn/ylhapp',
		ENABLE_ENCRYPTION: true,
		DEBUG_MODE: true
	},
	
	// 测试环境
	testing: {
		API_BASE_URL: 'https://test-api.example.com',
		ENABLE_ENCRYPTION: true,
		DEBUG_MODE: true
	},
	
	// 生产环境
	production: {
		API_BASE_URL: 'http://hd.peixue100.cn/ylhapp',
		ENABLE_ENCRYPTION: true,
		DEBUG_MODE: false
	}
}

/**
 * 获取当前环境配置
 * @returns {Object} 当前环境的配置
 */
export function getCurrentEnvConfig() {
	// 根据实际情况判断当前环境
	// 这里简化为开发环境
	const env = process.env.NODE_ENV || 'development'
	return ENV_CONFIG[env] || ENV_CONFIG.development
}

/**
 * 获取RSA公钥
 * @returns {string} RSA公钥
 */
export function getRSAPublicKey() {
	return CRYPTO_CONFIG.RSA.PUBLIC_KEY
}

/**
 * 检查是否需要加密
 * @param {string} method - 请求方法
 * @param {string} path - 请求路径
 * @returns {boolean} 是否需要加密
 */
export function shouldEncrypt(method, path) {
	if (!CRYPTO_CONFIG.REQUEST.ENABLE_ENCRYPTION) {
		return false
	}
	
	// 检查请求方法
	if (!CRYPTO_CONFIG.REQUEST.ENCRYPTED_METHODS.includes(method.toUpperCase())) {
		return false
	}
	
	// 检查请求路径
	return CRYPTO_CONFIG.REQUEST.ENCRYPTED_PATHS.some(encryptedPath => 
		path.includes(encryptedPath)
	)
}

/**
 * 获取AES配置
 * @returns {Object} AES配置
 */
export function getAESConfig() {
	return CRYPTO_CONFIG.AES
}

/**
 * 获取RSA配置
 * @returns {Object} RSA配置
 */
export function getRSAConfig() {
	return CRYPTO_CONFIG.RSA
}

/**
 * 是否启用调试日志
 * @returns {boolean} 是否启用
 */
export function isDebugEnabled() {
	return CRYPTO_CONFIG.DEVELOPMENT.ENABLE_DEBUG_LOG
}

/**
 * 是否启用加密测试
 * @returns {boolean} 是否启用
 */
export function isCryptoTestEnabled() {
	return CRYPTO_CONFIG.DEVELOPMENT.ENABLE_CRYPTO_TEST
}

/**
 * 获取完整的加密配置
 * @returns {Object} 完整配置
 */
export function getCryptoConfig() {
	return {
		...CRYPTO_CONFIG,
		ENV: getCurrentEnvConfig()
	}
}

/**
 * 更新RSA公钥
 * @param {string} publicKey - 新的公钥
 */
export function updateRSAPublicKey(publicKey) {
	CRYPTO_CONFIG.RSA.PUBLIC_KEY = publicKey
	console.log('RSA公钥已更新')
}

/**
 * 验证配置完整性
 * @returns {Object} 验证结果
 */
export function validateConfig() {
	const errors = []
	const warnings = []
	
	// 验证RSA公钥
	if (!CRYPTO_CONFIG.RSA.PUBLIC_KEY || CRYPTO_CONFIG.RSA.PUBLIC_KEY.length < 100) {
		errors.push('RSA公钥无效或过短')
	}
	
	// 验证AES密钥长度
	if (CRYPTO_CONFIG.AES.KEY_LENGTH < 16) {
		warnings.push('AES密钥长度建议至少16位')
	}
	
	// 验证API地址
	const envConfig = getCurrentEnvConfig()
	if (!envConfig.API_BASE_URL || !envConfig.API_BASE_URL.startsWith('https://')) {
		warnings.push('建议使用HTTPS协议')
	}
	
	return {
		valid: errors.length === 0,
		errors,
		warnings
	}
}
