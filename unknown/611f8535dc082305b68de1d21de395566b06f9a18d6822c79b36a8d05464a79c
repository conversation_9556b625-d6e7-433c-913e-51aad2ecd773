{"name": "jsencrypt", "version": "3.3.2", "description": "A Javascript library to perform OpenSSL RSA Encryption, Decryption, and Key Generation.", "main": "bin/jsencrypt.js", "module": "lib/index.js", "browser": "lib/index.js", "types": "lib/index.d.ts", "dependencies": {}, "devDependencies": {"@babel/core": "^7.20.12", "@babel/preset-env": "^7.20.2", "@babel/preset-typescript": "^7.18.6", "@babel/register": "^7.18.9", "@types/expect": "^24.3.0", "@types/mocha": "^10.0.0", "@types/node": "^18.11.4", "chai": "^4.3.6", "dirty-chai": "^2.0.1", "fs-jetpack": "^5.1.0", "mocha": "^10.0.0", "process": "^0.11.10", "ts-mocha": "^10.0.0", "ts-node": "^10.9.1", "typescript": "^4.2.4", "url": "^0.11.0", "webpack": "^5.35.1", "webpack-cli": "^4.6.0"}, "files": ["bin", "lib"], "scripts": {"build:dev": "tsc && tsc --project tsconfig-def.json && webpack", "build:test": "tsc && tsc --project tsconfig-def.json && webpack --config webpack.test.js", "build:prod": "tsc && tsc --project tsconfig-def.json && webpack --config webpack.prod.js", "build": "npm run build:dev && npm run build:test && npm run build:prod", "serve": "bundle exec jekyll server --config _config.build.yml", "test": "ts-mocha test/test.rsa.js"}, "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/travist"}, {"name": "Antonio", "url": "https://github.com/zoloft"}, {"name": "<PERSON>", "url": "https://github.com/jmgaya"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "http://www.travistidwell.com/jsencrypt", "repository": {"type": "git", "url": "git://github.com/travist/jsencrypt.git"}, "bugs": {"url": "http://github.com/travist/jsencrypt/issues"}, "license": "MIT"}