{"version": 3, "file": "bank.js", "sources": ["types/bank.js"], "sourcesContent": ["// 银行相关的类型定义和常量\n\n/**\n * 银行信息接口定义\n * @typedef {Object} BankInfo\n * @property {string} code - 联行号\n * @property {string} bankName - 银行名称\n * @property {string} branchName - 支行名称\n * @property {string} [address] - 地址\n * @property {string} [phone] - 电话\n * @property {string} [distance] - 距离\n */\n\n/**\n * API响应接口定义\n * @typedef {Object} ApiResponse\n * @property {boolean} success - 是否成功\n * @property {string} [message] - 消息\n * @property {BankInfo[]} [data] - 数据\n * @property {number} [total] - 总数\n */\n\n/**\n * 搜索参数接口定义\n * @typedef {Object} SearchParams\n * @property {string} keyword - 搜索关键字\n * @property {number} [limit] - 限制数量\n * @property {number} [offset] - 偏移量\n * @property {string} [type] - 搜索类型\n */\n\n/**\n * 银行类型常量\n */\nexport const BANK_TYPES = {\n\tICBC: 'icbc',        // 工商银行\n\tABC: 'abc',          // 农业银行\n\tBOC: 'boc',          // 中国银行\n\tCCB: 'ccb',          // 建设银行\n\tBOCOM: 'bocom',      // 交通银行\n\tCMB: 'cmb',          // 招商银行\n\tCITIC: 'citic',      // 中信银行\n\tCEB: 'ceb',          // 光大银行\n\tCMBC: 'cmbc',        // 民生银行\n\tCIB: 'cib',          // 兴业银行\n\tSPDB: 'spdb',        // 浦发银行\n\tPAB: 'pab',          // 平安银行\n\tHXB: 'hxb',          // 华夏银行\n\tGDB: 'gdb',          // 广发银行\n\tPSBC: 'psbc'         // 邮储银行\n}\n\n/**\n * 银行名称映射\n */\nexport const BANK_NAMES = {\n\t[BANK_TYPES.ICBC]: '中国工商银行',\n\t[BANK_TYPES.ABC]: '中国农业银行',\n\t[BANK_TYPES.BOC]: '中国银行',\n\t[BANK_TYPES.CCB]: '中国建设银行',\n\t[BANK_TYPES.BOCOM]: '交通银行',\n\t[BANK_TYPES.CMB]: '招商银行',\n\t[BANK_TYPES.CITIC]: '中信银行',\n\t[BANK_TYPES.CEB]: '中国光大银行',\n\t[BANK_TYPES.CMBC]: '中国民生银行',\n\t[BANK_TYPES.CIB]: '兴业银行',\n\t[BANK_TYPES.SPDB]: '上海浦东发展银行',\n\t[BANK_TYPES.PAB]: '平安银行',\n\t[BANK_TYPES.HXB]: '华夏银行',\n\t[BANK_TYPES.GDB]: '广发银行',\n\t[BANK_TYPES.PSBC]: '中国邮政储蓄银行'\n}\n\n/**\n * 银行简称映射\n */\nexport const BANK_SHORT_NAMES = {\n\t'工行': BANK_TYPES.ICBC,\n\t'农行': BANK_TYPES.ABC,\n\t'中行': BANK_TYPES.BOC,\n\t'建行': BANK_TYPES.CCB,\n\t'交行': BANK_TYPES.BOCOM,\n\t'招行': BANK_TYPES.CMB,\n\t'中信': BANK_TYPES.CITIC,\n\t'光大': BANK_TYPES.CEB,\n\t'民生': BANK_TYPES.CMBC,\n\t'兴业': BANK_TYPES.CIB,\n\t'浦发': BANK_TYPES.SPDB,\n\t'平安': BANK_TYPES.PAB,\n\t'华夏': BANK_TYPES.HXB,\n\t'广发': BANK_TYPES.GDB,\n\t'邮储': BANK_TYPES.PSBC\n}\n\n/**\n * 联行号前缀映射（前3位）\n */\nexport const BANK_CODE_PREFIXES = {\n\t'102': BANK_TYPES.ICBC,    // 工商银行\n\t'103': BANK_TYPES.ABC,     // 农业银行\n\t'104': BANK_TYPES.BOC,     // 中国银行\n\t'105': BANK_TYPES.CCB,     // 建设银行\n\t'301': BANK_TYPES.BOCOM,   // 交通银行\n\t'308': BANK_TYPES.CMB,     // 招商银行\n\t'302': BANK_TYPES.CITIC,   // 中信银行\n\t'303': BANK_TYPES.CEB,     // 光大银行\n\t'305': BANK_TYPES.CMBC,    // 民生银行\n\t'309': BANK_TYPES.CIB,     // 兴业银行\n\t'310': BANK_TYPES.SPDB,    // 浦发银行\n\t'307': BANK_TYPES.PAB,     // 平安银行\n\t'304': BANK_TYPES.HXB,     // 华夏银行\n\t'306': BANK_TYPES.GDB,     // 广发银行\n\t'403': BANK_TYPES.PSBC     // 邮储银行\n}\n\n/**\n * 搜索类型常量\n */\nexport const SEARCH_TYPES = {\n\tKEYWORD: 'keyword',      // 关键字搜索\n\tBANK_CODE: 'bank_code',  // 联行号搜索\n\tBANK_NAME: 'bank_name',  // 银行名称搜索\n\tREGION: 'region'         // 地区搜索\n}\n\n/**\n * 工具函数：根据联行号获取银行类型\n * @param {string} bankCode - 联行号\n * @returns {string|null} 银行类型\n */\nexport function getBankTypeByCode(bankCode) {\n\tif (!bankCode || bankCode.length < 3) return null\n\tconst prefix = bankCode.substring(0, 3)\n\treturn BANK_CODE_PREFIXES[prefix] || null\n}\n\n/**\n * 工具函数：根据银行类型获取银行名称\n * @param {string} bankType - 银行类型\n * @returns {string} 银行名称\n */\nexport function getBankNameByType(bankType) {\n\treturn BANK_NAMES[bankType] || '未知银行'\n}\n\n/**\n * 工具函数：根据简称获取银行类型\n * @param {string} shortName - 银行简称\n * @returns {string|null} 银行类型\n */\nexport function getBankTypeByShortName(shortName) {\n\treturn BANK_SHORT_NAMES[shortName] || null\n}\n\n/**\n * 工具函数：判断搜索类型\n * @param {string} keyword - 搜索关键字\n * @returns {string} 搜索类型\n */\nexport function detectSearchType(keyword) {\n\tif (!keyword) return SEARCH_TYPES.KEYWORD\n\t\n\t// 纯数字且长度为12，判断为联行号\n\tif (/^\\d{12}$/.test(keyword)) {\n\t\treturn SEARCH_TYPES.BANK_CODE\n\t}\n\t\n\t// 包含银行关键字\n\tif (Object.keys(BANK_SHORT_NAMES).some(name => keyword.includes(name))) {\n\t\treturn SEARCH_TYPES.BANK_NAME\n\t}\n\t\n\t// 包含地区关键字（简单判断）\n\tconst regionKeywords = ['北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '重庆', '西安']\n\tif (regionKeywords.some(region => keyword.includes(region))) {\n\t\treturn SEARCH_TYPES.REGION\n\t}\n\t\n\treturn SEARCH_TYPES.KEYWORD\n}\n"], "names": [], "mappings": ";AAkCO,MAAM,aAAa;AAAA,EACzB,MAAM;AAAA;AAAA,EACN,KAAK;AAAA;AAAA,EACL,KAAK;AAAA;AAAA,EACL,KAAK;AAAA;AAAA,EACL,OAAO;AAAA;AAAA,EACP,KAAK;AAAA;AAAA,EACL,OAAO;AAAA;AAAA,EACP,KAAK;AAAA;AAAA,EACL,MAAM;AAAA;AAAA,EACN,KAAK;AAAA;AAAA,EACL,MAAM;AAAA;AAAA,EACN,KAAK;AAAA;AAAA,EACL,KAAK;AAAA;AAAA,EACL,KAAK;AAAA;AAAA,EACL,MAAM;AAAA;AACP;AA0BO,MAAM,mBAAmB;AAAA,EAC/B,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAAA,EACjB,MAAM,WAAW;AAClB;AA0BY,MAAC,eAAe;AAAA,EAC3B,SAAS;AAAA;AAAA,EACT,WAAW;AAAA;AAAA,EACX,WAAW;AAAA;AAAA,EACX,QAAQ;AAAA;AACT;AAoCO,SAAS,iBAAiB,SAAS;AACzC,MAAI,CAAC;AAAS,WAAO,aAAa;AAGlC,MAAI,WAAW,KAAK,OAAO,GAAG;AAC7B,WAAO,aAAa;AAAA,EACpB;AAGD,MAAI,OAAO,KAAK,gBAAgB,EAAE,KAAK,UAAQ,QAAQ,SAAS,IAAI,CAAC,GAAG;AACvE,WAAO,aAAa;AAAA,EACpB;AAGD,QAAM,iBAAiB,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;AAClF,MAAI,eAAe,KAAK,YAAU,QAAQ,SAAS,MAAM,CAAC,GAAG;AAC5D,WAAO,aAAa;AAAA,EACpB;AAED,SAAO,aAAa;AACrB;;;"}