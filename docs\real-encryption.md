# 真实加密功能实现

本文档详细说明了项目中实现的真实RSA+AES混合加密功能。

## 🔒 加密技术栈

### 核心库
- **crypto-js**: AES加密解密
- **jsencrypt**: RSA加密解密
- **node-rsa**: RSA密钥生成（可选）

### 加密算法
- **RSA-2048**: 用于加密AES密钥
- **AES-256-ECB**: 用于加密实际数据
- **PKCS7**: 填充方式

## 🛠 实现架构

### 1. 配置管理
```javascript
// config/crypto.js
export const CRYPTO_CONFIG = {
  RSA: {
    KEY_SIZE: 2048,
    PUBLIC_KEY: '真实的RSA公钥',
    ALGORITHM: 'RSA-OAEP',
    HASH: 'SHA-256'
  },
  AES: {
    KEY_LENGTH: 32,
    MODE: 'ECB',
    PADDING: 'Pkcs7'
  }
}
```

### 2. 加密工具类
```javascript
// utils/crypto.js
export class CryptoUtil {
  // 真实的AES加密
  static aesEncrypt(data, key) {
    return CryptoJS.AES.encrypt(data, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    }).toString()
  }
  
  // 真实的RSA加密
  static rsaEncrypt(data) {
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(this.getRSAPublicKey())
    return encrypt.encrypt(data)
  }
}
```

## 🔐 加密流程

### 请求加密流程
1. **生成AES密钥**: 32位随机字符串
2. **AES加密数据**: 使用AES-256-ECB加密请求参数
3. **RSA加密密钥**: 使用RSA-2048加密AES密钥
4. **组装请求**: 返回加密数据和加密密钥

```javascript
// 加密请求示例
const params = { keyword: "工商银行", timestamp: Date.now() }

// 1. 生成AES密钥
const aesKey = "Kj8n2Lm9Qp3Rt6Uw1Zx4Cv7Bn0Mp5Hs" // 32位

// 2. AES加密参数
const encryptedData = CryptoJS.AES.encrypt(
  JSON.stringify(params), 
  aesKey
).toString()

// 3. RSA加密AES密钥
const keyObject = { aesKey: aesKey }
const encryptedKey = jsencrypt.encrypt(JSON.stringify(keyObject))

// 4. 发送请求
const request = {
  encryptedData: encryptedData,
  encryptedKey: encryptedKey
}
```

### 响应解密流程
1. **接收加密响应**: 服务器返回AES加密的数据
2. **AES解密**: 使用保存的AES密钥解密响应
3. **JSON解析**: 将解密后的字符串转换为对象

```javascript
// 解密响应示例
const encryptedResponse = "U2FsdGVkX1+..." // 服务器返回的加密数据
const aesKey = "Kj8n2Lm9Qp3Rt6Uw1Zx4Cv7Bn0Mp5Hs" // 之前保存的密钥

// 解密响应
const decryptedJson = CryptoJS.AES.decrypt(encryptedResponse, aesKey)
  .toString(CryptoJS.enc.Utf8)
const responseData = JSON.parse(decryptedJson)
```

## 🔧 配置说明

### RSA密钥配置
```javascript
// 真实的RSA公钥（2048位）
const RSA_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
-----END PUBLIC KEY-----`
```

### AES配置
```javascript
const AES_CONFIG = {
  KEY_LENGTH: 32,        // 密钥长度
  MODE: 'ECB',          // 加密模式
  PADDING: 'Pkcs7',     // 填充方式
  CHARSET: 'UTF-8'      // 字符编码
}
```

## 🧪 测试验证

### 1. 加密测试页面
访问 `/pages/test/crypto` 可以进行完整的加密功能测试：

- **AES加密测试**: 验证AES加密解密的正确性
- **RSA加密测试**: 验证RSA加密功能
- **混合加密测试**: 验证完整的加密流程
- **完整流程测试**: 模拟真实的请求响应流程

### 2. 测试用例
```javascript
// AES加密解密测试
const testData = "Hello, World! 测试数据"
const aesKey = CryptoUtil.generateAESKey()
const encrypted = CryptoUtil.aesEncrypt(testData, aesKey)
const decrypted = CryptoUtil.aesDecrypt(encrypted, aesKey)
console.assert(decrypted === testData, "AES加密解密测试失败")

// RSA加密测试
const keyData = JSON.stringify({ aesKey: aesKey })
const rsaEncrypted = CryptoUtil.rsaEncrypt(keyData)
console.assert(rsaEncrypted.length > 0, "RSA加密测试失败")

// 混合加密测试
const params = { message: "test", timestamp: Date.now() }
const encryptedRequest = CryptoUtil.encryptRequest(params)
console.assert(encryptedRequest.encryptedData && encryptedRequest.encryptedKey, "混合加密测试失败")
```

## 🔍 调试功能

### 调试日志
当启用调试模式时，会输出详细的加密过程日志：

```javascript
// 启用调试日志
const isDebugEnabled = true

// 日志输出示例
console.log('生成AES密钥，长度: 32')
console.log('AES加密开始，数据长度: 156')
console.log('AES加密完成，结果长度: 208')
console.log('RSA加密开始，数据长度: 45')
console.log('RSA加密完成，结果长度: 344')
console.log('混合加密完成')
```

### 错误处理
完善的错误处理机制：

```javascript
try {
  const encrypted = CryptoUtil.aesEncrypt(data, key)
} catch (error) {
  console.error('AES加密失败:', error.message)
  // 具体的错误信息：
  // - "数据加密失败: 密钥格式错误"
  // - "数据加密失败: 数据格式无效"
}
```

## 🚀 性能优化

### 1. 密钥缓存
```javascript
// 缓存AES密钥，避免重复生成
const keyCache = new Map()
const getCachedAESKey = (identifier) => {
  if (!keyCache.has(identifier)) {
    keyCache.set(identifier, CryptoUtil.generateAESKey())
  }
  return keyCache.get(identifier)
}
```

### 2. 批量加密
```javascript
// 批量处理多个请求
const encryptBatch = (requests) => {
  const aesKey = CryptoUtil.generateAESKey()
  return requests.map(request => ({
    ...CryptoUtil.encryptRequest(request),
    batchKey: aesKey
  }))
}
```

## 🔐 安全考虑

### 1. 密钥管理
- RSA私钥只存储在服务器端
- AES密钥动态生成，不重复使用
- 密钥传输使用RSA加密保护

### 2. 数据保护
- 所有敏感数据都经过加密传输
- 本地不存储明文敏感信息
- 加密算法使用业界标准

### 3. 防护措施
- 请求包含时间戳防重放攻击
- 密钥长度符合安全标准
- 错误信息不泄露敏感信息

## 📋 部署清单

### 生产环境配置
1. **替换RSA密钥**: 使用真实的生产环境密钥对
2. **关闭调试日志**: 设置 `ENABLE_DEBUG_LOG: false`
3. **配置HTTPS**: 确保传输层安全
4. **密钥轮换**: 定期更换RSA密钥对

### 服务器端要求
1. **支持RSA解密**: 服务器需要对应的私钥
2. **AES解密能力**: 支持AES-256-ECB解密
3. **JSON处理**: 正确解析加密请求格式

### 兼容性检查
- ✅ 微信小程序
- ✅ H5浏览器
- ✅ uni-app各平台
- ✅ Node.js服务器

## 🔧 故障排除

### 常见问题
1. **RSA加密失败**: 检查公钥格式和数据长度
2. **AES解密失败**: 验证密钥是否正确
3. **JSON解析错误**: 确认解密后的数据格式

### 解决方案
```javascript
// 验证RSA公钥格式
const isValidRSAKey = (key) => {
  return /^-----BEGIN PUBLIC KEY-----[\s\S]*-----END PUBLIC KEY-----$/.test(key)
}

// 验证AES密钥长度
const isValidAESKey = (key) => {
  return typeof key === 'string' && key.length >= 16
}

// 安全的JSON解析
const safeJSONParse = (str) => {
  try {
    return JSON.parse(str)
  } catch (error) {
    throw new Error('JSON解析失败: ' + error.message)
  }
}
```

## 📚 参考资料

- [crypto-js 文档](https://cryptojs.gitbook.io/docs/)
- [JSEncrypt 文档](https://github.com/travist/jsencrypt)
- [RSA加密标准](https://tools.ietf.org/html/rfc3447)
- [AES加密标准](https://nvlpubs.nist.gov/nistpubs/fips/nist.fips.197.pdf)
