
.container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
}
.header {
		text-align: center;
		margin-bottom: 40rpx;
}
.title {
		font-size: 40rpx;
		font-weight: bold;
		color: #333333;
}
.content {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
}

	/* 通用区域样式 */
.test-section,
	.input-section,
	.result-section,
	.key-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.section-title {
		margin-bottom: 20rpx;
		border-bottom: 2rpx solid #e0e0e0;
		padding-bottom: 15rpx;
}
.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
}

	/* 测试控制区域 */
.test-controls {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
}
.test-btn {
		flex: 1;
		min-width: 200rpx;
		height: 70rpx;
		background-color: #4CAF50;
		color: #ffffff;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
}

	/* 输入区域 */
.test-input {
		width: 100%;
		min-height: 150rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		margin-bottom: 20rpx;
		box-sizing: border-box;
}
.input-controls {
		display: flex;
		gap: 15rpx;
}
.control-btn {
		flex: 1;
		height: 60rpx;
		background-color: #2196F3;
		color: #ffffff;
		border: none;
		border-radius: 8rpx;
		font-size: 26rpx;
}

	/* 结果区域 */
.result-item {
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-bottom: 15rpx;
}
.result-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
}
.result-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
}
.result-status {
		font-size: 24rpx;
		padding: 5rpx 15rpx;
		border-radius: 20rpx;
}
.result-status.success {
		background-color: #e8f5e8;
		color: #4CAF50;
}
.result-status.error {
		background-color: #ffeaea;
		color: #f44336;
}
.result-content {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.4;
}
.result-data {
		margin-top: 10rpx;
		padding: 15rpx;
		background-color: #f8f9fa;
		border-radius: 6rpx;
}
.data-label {
		font-weight: bold;
		color: #333333;
}
.data-content {
		display: block;
		margin-top: 8rpx;
		word-break: break-all;
		font-family: monospace;
}

	/* 密钥区域 */
.key-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
		padding: 15rpx;
		background-color: #f8f9fa;
		border-radius: 8rpx;
}
.key-label {
		font-size: 28rpx;
		color: #333333;
}
.key-status.valid {
		color: #4CAF50;
		font-weight: bold;
}
.key-status.invalid {
		color: #f44336;
		font-weight: bold;
}
.key-value {
		font-size: 24rpx;
		color: #666666;
		font-family: monospace;
		max-width: 300rpx;
		overflow: hidden;
		text-overflow: ellipsis;
}
.key-btn {
		width: 100%;
		height: 70rpx;
		background-color: #FF9800;
		color: #ffffff;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		margin-top: 20rpx;
}
