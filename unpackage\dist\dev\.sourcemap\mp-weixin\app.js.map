{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script setup>\r\n\timport { onLaunch, onShow, onHide } from '@dcloudio/uni-app'\r\n\r\n\tonLaunch(() => {\r\n\t\tconsole.log('App Launch')\r\n\t\tuni.setEnableDebug({\r\n\t\t\tenableDebug: false\r\n\t\t})\r\n\t})\r\n\r\n\tonShow(() => {\r\n\t\tconsole.log('App Show')\r\n\t})\r\n\r\n\tonHide(() => {\r\n\t\tconsole.log('App Hide')\r\n\t})\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nexport function createApp() {\n  const app = createSSRApp(App)\n  return {\n    app\n  }\n}\n// #endif"], "names": ["onLaunch", "uni", "onShow", "onHide", "createSSRApp", "App"], "mappings": ";;;;;;;;;AAGCA,kBAAAA,SAAS,MAAM;AACdC,oBAAAA,MAAA,MAAA,OAAA,gBAAY,YAAY;AACxBA,oBAAAA,MAAI,eAAe;AAAA,QAClB,aAAa;AAAA,MAChB,CAAG;AAAA,IACH,CAAE;AAEDC,kBAAAA,OAAO,MAAM;AACZD,oBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,IACxB,CAAE;AAEDE,kBAAAA,OAAO,MAAM;AACZF,oBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,IACxB,CAAE;;;;;ACDK,SAAS,YAAY;AAC1B,QAAM,MAAMG,cAAY,aAACC,SAAG;AAC5B,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}