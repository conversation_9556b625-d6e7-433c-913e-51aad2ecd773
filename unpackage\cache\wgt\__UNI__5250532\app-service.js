if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(t){const e=this.constructor;return this.then((r=>e.resolve(t()).then((()=>r))),(r=>e.resolve(t()).then((()=>{throw r}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const t=uni.requireGlobal();ArrayBuffer=t.<PERSON>y<PERSON>uffer,Int8Array=t.Int8Array,Uint8Array=t.Uint8Array,Uint8ClampedArray=t.Uint8ClampedArray,Int16Array=t.Int16Array,Uint16Array=t.Uint16Array,Int32Array=t.Int32Array,Uint32Array=t.Uint32Array,Float32Array=t.Float32Array,Float64Array=t.Float64Array,BigInt64Array=t.BigInt64Array,BigUint64Array=t.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(t){"use strict";function e(t,e,...r){uni.__log__?uni.__log__(t,e,...r):console[t].apply(console,[...r,e])}const r=e=>(r,i=t.getCurrentInstance())=>{!t.isInSSRComponentSetup&&t.injectHook(e,r,i)},i=r("onShow"),n=r("onHide"),s=r("onLaunch"),o={"工行":"icbc","农行":"abc","中行":"boc","建行":"ccb","交行":"bocom","招行":"cmb","中信":"citic","光大":"ceb","民生":"cmbc","兴业":"cib","浦发":"spdb","平安":"pab","华夏":"hxb","广发":"gdb","邮储":"psbc"},a="keyword",h="bank_code",c="bank_name",u="region";var l="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function f(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function p(t){if(t.__esModule)return t;var e=t.default;if("function"==typeof e){var r=function t(){return this instanceof t?Reflect.construct(e,arguments,this.constructor):e.apply(this,arguments)};r.prototype=e.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(t).forEach((function(e){var i=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(r,e,i.get?i:{enumerable:!0,get:function(){return t[e]}})})),r}var d={exports:{}},g=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,62,0,62,0,63,52,53,54,55,56,57,58,59,60,61,0,0,0,0,0,0,0,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,0,0,0,0,63,0,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51];const v={getRandomValues(t){if(!(t instanceof Int8Array||t instanceof Uint8Array||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Uint8ClampedArray))throw new Error("Expected an integer array");if(t.byteLength>65536)throw new Error("Can only request a maximum of 65536 bytes");var e;return function(t,e){for(var r,i=t.length,n="="===t[i-2]?2:"="===t[i-1]?1:0,s=0,o=i-n&4294967292,a=0;a<o;a+=4)r=g[t.charCodeAt(a)]<<18|g[t.charCodeAt(a+1)]<<12|g[t.charCodeAt(a+2)]<<6|g[t.charCodeAt(a+3)],e[s++]=r>>16&255,e[s++]=r>>8&255,e[s++]=255&r;1===n&&(r=g[t.charCodeAt(a)]<<10|g[t.charCodeAt(a+1)]<<4|g[t.charCodeAt(a+2)]>>2,e[s++]=r>>8&255,e[s++]=255&r),2===n&&(r=g[t.charCodeAt(a)]<<2|g[t.charCodeAt(a+1)]>>4,e[s++]=255&r)}((e="DCloud-Crypto",weex.requireModule(e)).getRandomValues(t.byteLength),new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),t}};var y={exports:{}};const m=p(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));var b;function S(){return b||(b=1,y.exports=(t=t||function(t,e){var r;if("undefined"!=typeof window&&v&&(r=v),"undefined"!=typeof self&&self.crypto&&(r=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(r=globalThis.crypto),!r&&"undefined"!=typeof window&&window.msCrypto&&(r=window.msCrypto),!r&&void 0!==l&&l.crypto&&(r=l.crypto),!r)try{r=m}catch(y){}var i=function(){if(r){if("function"==typeof r.getRandomValues)try{return r.getRandomValues(new Uint32Array(1))[0]}catch(y){}if("function"==typeof r.randomBytes)try{return r.randomBytes(4).readInt32LE()}catch(y){}}throw new Error("Native crypto module could not be used to get secure random number.")},n=Object.create||function(){function t(){}return function(e){var r;return t.prototype=e,r=new t,t.prototype=null,r}}(),s={},o=s.lib={},a=o.Base=function(){return{extend:function(t){var e=n(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),h=o.WordArray=a.extend({init:function(t,r){t=this.words=t||[],this.sigBytes=r!=e?r:4*t.length},toString:function(t){return(t||u).stringify(this)},concat:function(t){var e=this.words,r=t.words,i=this.sigBytes,n=t.sigBytes;if(this.clamp(),i%4)for(var s=0;s<n;s++){var o=r[s>>>2]>>>24-s%4*8&255;e[i+s>>>2]|=o<<24-(i+s)%4*8}else for(var a=0;a<n;a+=4)e[i+a>>>2]=r[a>>>2];return this.sigBytes+=n,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=a.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(i());return new h.init(e,t)}}),c=s.enc={},u=c.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n++){var s=e[n>>>2]>>>24-n%4*8&255;i.push((s>>>4).toString(16)),i.push((15&s).toString(16))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i+=2)r[i>>>3]|=parseInt(t.substr(i,2),16)<<24-i%8*4;return new h.init(r,e/2)}},f=c.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n++){var s=e[n>>>2]>>>24-n%4*8&255;i.push(String.fromCharCode(s))}return i.join("")},parse:function(t){for(var e=t.length,r=[],i=0;i<e;i++)r[i>>>2]|=(255&t.charCodeAt(i))<<24-i%4*8;return new h.init(r,e)}},p=c.Utf8={stringify:function(t){try{return decodeURIComponent(escape(f.stringify(t)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(t){return f.parse(unescape(encodeURIComponent(t)))}},d=o.BufferedBlockAlgorithm=a.extend({reset:function(){this._data=new h.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=p.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r,i=this._data,n=i.words,s=i.sigBytes,o=this.blockSize,a=s/(4*o),c=(a=e?t.ceil(a):t.max((0|a)-this._minBufferSize,0))*o,u=t.min(4*c,s);if(c){for(var l=0;l<c;l+=o)this._doProcessBlock(n,l);r=n.splice(0,c),i.sigBytes-=u}return new h.init(r,u)},clone:function(){var t=a.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0});o.Hasher=d.extend({cfg:a.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new g.HMAC.init(t,r).finalize(e)}}});var g=s.algo={};return s}(Math),t)),y.exports;var t}var w,E={exports:{}};function _(){return w||(w=1,E.exports=(o=S(),r=(e=o).lib,i=r.Base,n=r.WordArray,(s=e.x64={}).Word=i.extend({init:function(t,e){this.high=t,this.low=e}}),s.WordArray=i.extend({init:function(e,r){e=this.words=e||[],this.sigBytes=r!=t?r:8*e.length},toX32:function(){for(var t=this.words,e=t.length,r=[],i=0;i<e;i++){var s=t[i];r.push(s.high),r.push(s.low)}return n.create(r,this.sigBytes)},clone:function(){for(var t=i.clone.call(this),e=t.words=this.words.slice(0),r=e.length,n=0;n<r;n++)e[n]=e[n].clone();return t}}),o)),E.exports;var t,e,r,i,n,s,o}var x,B={exports:{}};function T(){return x||(x=1,B.exports=(t=S(),function(){if("function"==typeof ArrayBuffer){var e=t.lib.WordArray,r=e.init,i=e.init=function(t){if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),(t instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)&&(t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),t instanceof Uint8Array){for(var e=t.byteLength,i=[],n=0;n<e;n++)i[n>>>2]|=t[n]<<24-n%4*8;r.call(this,i,e)}else r.apply(this,arguments)};i.prototype=e}}(),t.lib.WordArray)),B.exports;var t}var A,D={exports:{}};function R(){return A||(A=1,D.exports=(t=S(),function(){var e=t,r=e.lib.WordArray,i=e.enc;function n(t){return t<<8&4278255360|t>>>8&16711935}i.Utf16=i.Utf16BE={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],n=0;n<r;n+=2){var s=e[n>>>2]>>>16-n%4*8&65535;i.push(String.fromCharCode(s))}return i.join("")},parse:function(t){for(var e=t.length,i=[],n=0;n<e;n++)i[n>>>1]|=t.charCodeAt(n)<<16-n%2*16;return r.create(i,2*e)}},i.Utf16LE={stringify:function(t){for(var e=t.words,r=t.sigBytes,i=[],s=0;s<r;s+=2){var o=n(e[s>>>2]>>>16-s%4*8&65535);i.push(String.fromCharCode(o))}return i.join("")},parse:function(t){for(var e=t.length,i=[],s=0;s<e;s++)i[s>>>1]|=n(t.charCodeAt(s)<<16-s%2*16);return r.create(i,2*e)}}}(),t.enc.Utf16)),D.exports;var t}var k,N={exports:{}};function I(){return k||(k=1,N.exports=(t=S(),function(){var e=t,r=e.lib.WordArray;function i(t,e,i){for(var n=[],s=0,o=0;o<e;o++)if(o%4){var a=i[t.charCodeAt(o-1)]<<o%4*2|i[t.charCodeAt(o)]>>>6-o%4*2;n[s>>>2]|=a<<24-s%4*8,s++}return r.create(n,s)}e.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,i=this._map;t.clamp();for(var n=[],s=0;s<r;s+=3)for(var o=(e[s>>>2]>>>24-s%4*8&255)<<16|(e[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|e[s+2>>>2]>>>24-(s+2)%4*8&255,a=0;a<4&&s+.75*a<r;a++)n.push(i.charAt(o>>>6*(3-a)&63));var h=i.charAt(64);if(h)for(;n.length%4;)n.push(h);return n.join("")},parse:function(t){var e=t.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var s=0;s<r.length;s++)n[r.charCodeAt(s)]=s}var o=r.charAt(64);if(o){var a=t.indexOf(o);-1!==a&&(e=a)}return i(t,e,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0********9+/="}}(),t.enc.Base64)),N.exports;var t}var O,C={exports:{}};function P(){return O||(O=1,C.exports=(t=S(),function(){var e=t,r=e.lib.WordArray;function i(t,e,i){for(var n=[],s=0,o=0;o<e;o++)if(o%4){var a=i[t.charCodeAt(o-1)]<<o%4*2|i[t.charCodeAt(o)]>>>6-o%4*2;n[s>>>2]|=a<<24-s%4*8,s++}return r.create(n,s)}e.enc.Base64url={stringify:function(t,e=!0){var r=t.words,i=t.sigBytes,n=e?this._safe_map:this._map;t.clamp();for(var s=[],o=0;o<i;o+=3)for(var a=(r[o>>>2]>>>24-o%4*8&255)<<16|(r[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|r[o+2>>>2]>>>24-(o+2)%4*8&255,h=0;h<4&&o+.75*h<i;h++)s.push(n.charAt(a>>>6*(3-h)&63));var c=n.charAt(64);if(c)for(;s.length%4;)s.push(c);return s.join("")},parse:function(t,e=!0){var r=t.length,n=e?this._safe_map:this._map,s=this._reverseMap;if(!s){s=this._reverseMap=[];for(var o=0;o<n.length;o++)s[n.charCodeAt(o)]=o}var a=n.charAt(64);if(a){var h=t.indexOf(a);-1!==h&&(r=h)}return i(t,r,s)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0********9+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0********9-_"}}(),t.enc.Base64url)),C.exports;var t}var V,H={exports:{}};function M(){return V||(V=1,H.exports=(t=S(),function(e){var r=t,i=r.lib,n=i.WordArray,s=i.Hasher,o=r.algo,a=[];!function(){for(var t=0;t<64;t++)a[t]=4294967296*e.abs(e.sin(t+1))|0}();var h=o.MD5=s.extend({_doReset:function(){this._hash=new n.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var i=e+r,n=t[i];t[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var s=this._hash.words,o=t[e+0],h=t[e+1],p=t[e+2],d=t[e+3],g=t[e+4],v=t[e+5],y=t[e+6],m=t[e+7],b=t[e+8],S=t[e+9],w=t[e+10],E=t[e+11],_=t[e+12],x=t[e+13],B=t[e+14],T=t[e+15],A=s[0],D=s[1],R=s[2],k=s[3];A=c(A,D,R,k,o,7,a[0]),k=c(k,A,D,R,h,12,a[1]),R=c(R,k,A,D,p,17,a[2]),D=c(D,R,k,A,d,22,a[3]),A=c(A,D,R,k,g,7,a[4]),k=c(k,A,D,R,v,12,a[5]),R=c(R,k,A,D,y,17,a[6]),D=c(D,R,k,A,m,22,a[7]),A=c(A,D,R,k,b,7,a[8]),k=c(k,A,D,R,S,12,a[9]),R=c(R,k,A,D,w,17,a[10]),D=c(D,R,k,A,E,22,a[11]),A=c(A,D,R,k,_,7,a[12]),k=c(k,A,D,R,x,12,a[13]),R=c(R,k,A,D,B,17,a[14]),A=u(A,D=c(D,R,k,A,T,22,a[15]),R,k,h,5,a[16]),k=u(k,A,D,R,y,9,a[17]),R=u(R,k,A,D,E,14,a[18]),D=u(D,R,k,A,o,20,a[19]),A=u(A,D,R,k,v,5,a[20]),k=u(k,A,D,R,w,9,a[21]),R=u(R,k,A,D,T,14,a[22]),D=u(D,R,k,A,g,20,a[23]),A=u(A,D,R,k,S,5,a[24]),k=u(k,A,D,R,B,9,a[25]),R=u(R,k,A,D,d,14,a[26]),D=u(D,R,k,A,b,20,a[27]),A=u(A,D,R,k,x,5,a[28]),k=u(k,A,D,R,p,9,a[29]),R=u(R,k,A,D,m,14,a[30]),A=l(A,D=u(D,R,k,A,_,20,a[31]),R,k,v,4,a[32]),k=l(k,A,D,R,b,11,a[33]),R=l(R,k,A,D,E,16,a[34]),D=l(D,R,k,A,B,23,a[35]),A=l(A,D,R,k,h,4,a[36]),k=l(k,A,D,R,g,11,a[37]),R=l(R,k,A,D,m,16,a[38]),D=l(D,R,k,A,w,23,a[39]),A=l(A,D,R,k,x,4,a[40]),k=l(k,A,D,R,o,11,a[41]),R=l(R,k,A,D,d,16,a[42]),D=l(D,R,k,A,y,23,a[43]),A=l(A,D,R,k,S,4,a[44]),k=l(k,A,D,R,_,11,a[45]),R=l(R,k,A,D,T,16,a[46]),A=f(A,D=l(D,R,k,A,p,23,a[47]),R,k,o,6,a[48]),k=f(k,A,D,R,m,10,a[49]),R=f(R,k,A,D,B,15,a[50]),D=f(D,R,k,A,v,21,a[51]),A=f(A,D,R,k,_,6,a[52]),k=f(k,A,D,R,d,10,a[53]),R=f(R,k,A,D,w,15,a[54]),D=f(D,R,k,A,h,21,a[55]),A=f(A,D,R,k,b,6,a[56]),k=f(k,A,D,R,T,10,a[57]),R=f(R,k,A,D,y,15,a[58]),D=f(D,R,k,A,x,21,a[59]),A=f(A,D,R,k,g,6,a[60]),k=f(k,A,D,R,E,10,a[61]),R=f(R,k,A,D,p,15,a[62]),D=f(D,R,k,A,S,21,a[63]),s[0]=s[0]+A|0,s[1]=s[1]+D|0,s[2]=s[2]+R|0,s[3]=s[3]+k|0},_doFinalize:function(){var t=this._data,r=t.words,i=8*this._nDataBytes,n=8*t.sigBytes;r[n>>>5]|=128<<24-n%32;var s=e.floor(i/4294967296),o=i;r[15+(n+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),r[14+(n+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),t.sigBytes=4*(r.length+1),this._process();for(var a=this._hash,h=a.words,c=0;c<4;c++){var u=h[c];h[c]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return a},clone:function(){var t=s.clone.call(this);return t._hash=this._hash.clone(),t}});function c(t,e,r,i,n,s,o){var a=t+(e&r|~e&i)+n+o;return(a<<s|a>>>32-s)+e}function u(t,e,r,i,n,s,o){var a=t+(e&i|r&~i)+n+o;return(a<<s|a>>>32-s)+e}function l(t,e,r,i,n,s,o){var a=t+(e^r^i)+n+o;return(a<<s|a>>>32-s)+e}function f(t,e,r,i,n,s,o){var a=t+(r^(e|~i))+n+o;return(a<<s|a>>>32-s)+e}r.MD5=s._createHelper(h),r.HmacMD5=s._createHmacHelper(h)}(Math),t.MD5)),H.exports;var t}var j,L={exports:{}};function U(){return j||(j=1,L.exports=(a=S(),e=(t=a).lib,r=e.WordArray,i=e.Hasher,n=t.algo,s=[],o=n.SHA1=i.extend({_doReset:function(){this._hash=new r.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],o=r[2],a=r[3],h=r[4],c=0;c<80;c++){if(c<16)s[c]=0|t[e+c];else{var u=s[c-3]^s[c-8]^s[c-14]^s[c-16];s[c]=u<<1|u>>>31}var l=(i<<5|i>>>27)+h+s[c];l+=c<20?1518500249+(n&o|~n&a):c<40?1859775393+(n^o^a):c<60?(n&o|n&a|o&a)-1894007588:(n^o^a)-899497514,h=a,a=o,o=n<<30|n>>>2,n=i,i=l}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+h|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[14+(i+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(i+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=i.clone.call(this);return t._hash=this._hash.clone(),t}}),t.SHA1=i._createHelper(o),t.HmacSHA1=i._createHmacHelper(o),a.SHA1)),L.exports;var t,e,r,i,n,s,o,a}var K,z={exports:{}};function q(){return K||(K=1,z.exports=(t=S(),function(e){var r=t,i=r.lib,n=i.WordArray,s=i.Hasher,o=r.algo,a=[],h=[];!function(){function t(t){for(var r=e.sqrt(t),i=2;i<=r;i++)if(!(t%i))return!1;return!0}function r(t){return 4294967296*(t-(0|t))|0}for(var i=2,n=0;n<64;)t(i)&&(n<8&&(a[n]=r(e.pow(i,.5))),h[n]=r(e.pow(i,1/3)),n++),i++}();var c=[],u=o.SHA256=s.extend({_doReset:function(){this._hash=new n.init(a.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],s=r[2],o=r[3],a=r[4],u=r[5],l=r[6],f=r[7],p=0;p<64;p++){if(p<16)c[p]=0|t[e+p];else{var d=c[p-15],g=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,v=c[p-2],y=(v<<15|v>>>17)^(v<<13|v>>>19)^v>>>10;c[p]=g+c[p-7]+y+c[p-16]}var m=i&n^i&s^n&s,b=(i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22),S=f+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&u^~a&l)+h[p]+c[p];f=l,l=u,u=a,a=o+S|0,o=s,s=n,n=i,i=S+(b+m)|0}r[0]=r[0]+i|0,r[1]=r[1]+n|0,r[2]=r[2]+s|0,r[3]=r[3]+o|0,r[4]=r[4]+a|0,r[5]=r[5]+u|0,r[6]=r[6]+l|0,r[7]=r[7]+f|0},_doFinalize:function(){var t=this._data,r=t.words,i=8*this._nDataBytes,n=8*t.sigBytes;return r[n>>>5]|=128<<24-n%32,r[14+(n+64>>>9<<4)]=e.floor(i/4294967296),r[15+(n+64>>>9<<4)]=i,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var t=s.clone.call(this);return t._hash=this._hash.clone(),t}});r.SHA256=s._createHelper(u),r.HmacSHA256=s._createHmacHelper(u)}(Math),t.SHA256)),z.exports;var t}var F,W={exports:{}};var G,Y={exports:{}};function Z(){return G||(G=1,Y.exports=(t=S(),_(),function(){var e=t,r=e.lib.Hasher,i=e.x64,n=i.Word,s=i.WordArray,o=e.algo;function a(){return n.create.apply(n,arguments)}var h=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],c=[];!function(){for(var t=0;t<80;t++)c[t]=a()}();var u=o.SHA512=r.extend({_doReset:function(){this._hash=new s.init([new n.init(1779033703,4089235720),new n.init(3144134277,2227873595),new n.init(1013904242,4271175723),new n.init(2773480762,1595750129),new n.init(1359893119,2917565137),new n.init(2600822924,725511199),new n.init(528734635,4215389547),new n.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=this._hash.words,i=r[0],n=r[1],s=r[2],o=r[3],a=r[4],u=r[5],l=r[6],f=r[7],p=i.high,d=i.low,g=n.high,v=n.low,y=s.high,m=s.low,b=o.high,S=o.low,w=a.high,E=a.low,_=u.high,x=u.low,B=l.high,T=l.low,A=f.high,D=f.low,R=p,k=d,N=g,I=v,O=y,C=m,P=b,V=S,H=w,M=E,j=_,L=x,U=B,K=T,z=A,q=D,F=0;F<80;F++){var W,G,Y=c[F];if(F<16)G=Y.high=0|t[e+2*F],W=Y.low=0|t[e+2*F+1];else{var Z=c[F-15],$=Z.high,X=Z.low,J=($>>>1|X<<31)^($>>>8|X<<24)^$>>>7,Q=(X>>>1|$<<31)^(X>>>8|$<<24)^(X>>>7|$<<25),tt=c[F-2],et=tt.high,rt=tt.low,it=(et>>>19|rt<<13)^(et<<3|rt>>>29)^et>>>6,nt=(rt>>>19|et<<13)^(rt<<3|et>>>29)^(rt>>>6|et<<26),st=c[F-7],ot=st.high,at=st.low,ht=c[F-16],ct=ht.high,ut=ht.low;G=(G=(G=J+ot+((W=Q+at)>>>0<Q>>>0?1:0))+it+((W+=nt)>>>0<nt>>>0?1:0))+ct+((W+=ut)>>>0<ut>>>0?1:0),Y.high=G,Y.low=W}var lt,ft=H&j^~H&U,pt=M&L^~M&K,dt=R&N^R&O^N&O,gt=k&I^k&C^I&C,vt=(R>>>28|k<<4)^(R<<30|k>>>2)^(R<<25|k>>>7),yt=(k>>>28|R<<4)^(k<<30|R>>>2)^(k<<25|R>>>7),mt=(H>>>14|M<<18)^(H>>>18|M<<14)^(H<<23|M>>>9),bt=(M>>>14|H<<18)^(M>>>18|H<<14)^(M<<23|H>>>9),St=h[F],wt=St.high,Et=St.low,_t=z+mt+((lt=q+bt)>>>0<q>>>0?1:0),xt=yt+gt;z=U,q=K,U=j,K=L,j=H,L=M,H=P+(_t=(_t=(_t=_t+ft+((lt+=pt)>>>0<pt>>>0?1:0))+wt+((lt+=Et)>>>0<Et>>>0?1:0))+G+((lt+=W)>>>0<W>>>0?1:0))+((M=V+lt|0)>>>0<V>>>0?1:0)|0,P=O,V=C,O=N,C=I,N=R,I=k,R=_t+(vt+dt+(xt>>>0<yt>>>0?1:0))+((k=lt+xt|0)>>>0<lt>>>0?1:0)|0}d=i.low=d+k,i.high=p+R+(d>>>0<k>>>0?1:0),v=n.low=v+I,n.high=g+N+(v>>>0<I>>>0?1:0),m=s.low=m+C,s.high=y+O+(m>>>0<C>>>0?1:0),S=o.low=S+V,o.high=b+P+(S>>>0<V>>>0?1:0),E=a.low=E+M,a.high=w+H+(E>>>0<M>>>0?1:0),x=u.low=x+L,u.high=_+j+(x>>>0<L>>>0?1:0),T=l.low=T+K,l.high=B+U+(T>>>0<K>>>0?1:0),D=f.low=D+q,f.high=A+z+(D>>>0<q>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[30+(i+128>>>10<<5)]=Math.floor(r/4294967296),e[31+(i+128>>>10<<5)]=r,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=r.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});e.SHA512=r._createHelper(u),e.HmacSHA512=r._createHmacHelper(u)}(),t.SHA512)),Y.exports;var t}var $,X={exports:{}};var J,Q={exports:{}};function tt(){return J||(J=1,Q.exports=(t=S(),_(),function(e){var r=t,i=r.lib,n=i.WordArray,s=i.Hasher,o=r.x64.Word,a=r.algo,h=[],c=[],u=[];!function(){for(var t=1,e=0,r=0;r<24;r++){h[t+5*e]=(r+1)*(r+2)/2%64;var i=(2*t+3*e)%5;t=e%5,e=i}for(t=0;t<5;t++)for(e=0;e<5;e++)c[t+5*e]=e+(2*t+3*e)%5*5;for(var n=1,s=0;s<24;s++){for(var a=0,l=0,f=0;f<7;f++){if(1&n){var p=(1<<f)-1;p<32?l^=1<<p:a^=1<<p-32}128&n?n=n<<1^113:n<<=1}u[s]=o.create(a,l)}}();var l=[];!function(){for(var t=0;t<25;t++)l[t]=o.create()}();var f=a.SHA3=s.extend({cfg:s.cfg.extend({outputLength:512}),_doReset:function(){for(var t=this._state=[],e=0;e<25;e++)t[e]=new o.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(t,e){for(var r=this._state,i=this.blockSize/2,n=0;n<i;n++){var s=t[e+2*n],o=t[e+2*n+1];s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),(D=r[n]).high^=o,D.low^=s}for(var a=0;a<24;a++){for(var f=0;f<5;f++){for(var p=0,d=0,g=0;g<5;g++)p^=(D=r[f+5*g]).high,d^=D.low;var v=l[f];v.high=p,v.low=d}for(f=0;f<5;f++){var y=l[(f+4)%5],m=l[(f+1)%5],b=m.high,S=m.low;for(p=y.high^(b<<1|S>>>31),d=y.low^(S<<1|b>>>31),g=0;g<5;g++)(D=r[f+5*g]).high^=p,D.low^=d}for(var w=1;w<25;w++){var E=(D=r[w]).high,_=D.low,x=h[w];x<32?(p=E<<x|_>>>32-x,d=_<<x|E>>>32-x):(p=_<<x-32|E>>>64-x,d=E<<x-32|_>>>64-x);var B=l[c[w]];B.high=p,B.low=d}var T=l[0],A=r[0];for(T.high=A.high,T.low=A.low,f=0;f<5;f++)for(g=0;g<5;g++){var D=r[w=f+5*g],R=l[w],k=l[(f+1)%5+5*g],N=l[(f+2)%5+5*g];D.high=R.high^~k.high&N.high,D.low=R.low^~k.low&N.low}D=r[0];var I=u[a];D.high^=I.high,D.low^=I.low}},_doFinalize:function(){var t=this._data,r=t.words;this._nDataBytes;var i=8*t.sigBytes,s=32*this.blockSize;r[i>>>5]|=1<<24-i%32,r[(e.ceil((i+1)/s)*s>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var o=this._state,a=this.cfg.outputLength/8,h=a/8,c=[],u=0;u<h;u++){var l=o[u],f=l.high,p=l.low;f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),p=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8),c.push(p),c.push(f)}return new n.init(c,a)},clone:function(){for(var t=s.clone.call(this),e=t._state=this._state.slice(0),r=0;r<25;r++)e[r]=e[r].clone();return t}});r.SHA3=s._createHelper(f),r.HmacSHA3=s._createHmacHelper(f)}(Math),t.SHA3)),Q.exports;var t}var et,rt={exports:{}};var it,nt={exports:{}};function st(){return it||(it=1,nt.exports=(t=S(),r=(e=t).lib.Base,i=e.enc.Utf8,void(e.algo.HMAC=r.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=i.parse(e));var r=t.blockSize,n=4*r;e.sigBytes>n&&(e=t.finalize(e)),e.clamp();for(var s=this._oKey=e.clone(),o=this._iKey=e.clone(),a=s.words,h=o.words,c=0;c<r;c++)a[c]^=1549556828,h[c]^=909522486;s.sigBytes=o.sigBytes=n,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,r=e.finalize(t);return e.reset(),e.finalize(this._oKey.clone().concat(r))}})))),nt.exports;var t,e,r,i}var ot,at={exports:{}};var ht,ct={exports:{}};function ut(){return ht||(ht=1,ct.exports=(a=S(),U(),st(),e=(t=a).lib,r=e.Base,i=e.WordArray,n=t.algo,s=n.MD5,o=n.EvpKDF=r.extend({cfg:r.extend({keySize:4,hasher:s,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r,n=this.cfg,s=n.hasher.create(),o=i.create(),a=o.words,h=n.keySize,c=n.iterations;a.length<h;){r&&s.update(r),r=s.update(t).finalize(e),s.reset();for(var u=1;u<c;u++)r=s.finalize(r),s.reset();o.concat(r)}return o.sigBytes=4*h,o}}),t.EvpKDF=function(t,e,r){return o.create(r).compute(t,e)},a.EvpKDF)),ct.exports;var t,e,r,i,n,s,o,a}var lt,ft={exports:{}};function pt(){return lt||(lt=1,ft.exports=(t=S(),ut(),void(t.lib.Cipher||function(e){var r=t,i=r.lib,n=i.Base,s=i.WordArray,o=i.BufferedBlockAlgorithm,a=r.enc;a.Utf8;var h=a.Base64,c=r.algo.EvpKDF,u=i.Cipher=o.extend({cfg:n.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){o.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function t(t){return"string"==typeof t?b:y}return function(e){return{encrypt:function(r,i,n){return t(i).encrypt(e,r,i,n)},decrypt:function(r,i,n){return t(i).decrypt(e,r,i,n)}}}}()});i.StreamCipher=u.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var l=r.mode={},f=i.BlockCipherMode=n.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),p=l.CBC=function(){var t=f.extend();function r(t,r,i){var n,s=this._iv;s?(n=s,this._iv=e):n=this._prevBlock;for(var o=0;o<i;o++)t[r+o]^=n[o]}return t.Encryptor=t.extend({processBlock:function(t,e){var i=this._cipher,n=i.blockSize;r.call(this,t,e,n),i.encryptBlock(t,e),this._prevBlock=t.slice(e,e+n)}}),t.Decryptor=t.extend({processBlock:function(t,e){var i=this._cipher,n=i.blockSize,s=t.slice(e,e+n);i.decryptBlock(t,e),r.call(this,t,e,n),this._prevBlock=s}}),t}(),d=(r.pad={}).Pkcs7={pad:function(t,e){for(var r=4*e,i=r-t.sigBytes%r,n=i<<24|i<<16|i<<8|i,o=[],a=0;a<i;a+=4)o.push(n);var h=s.create(o,i);t.concat(h)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}};i.BlockCipher=u.extend({cfg:u.cfg.extend({mode:p,padding:d}),reset:function(){var t;u.reset.call(this);var e=this.cfg,r=e.iv,i=e.mode;this._xformMode==this._ENC_XFORM_MODE?t=i.createEncryptor:(t=i.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,r&&r.words):(this._mode=t.call(i,this,r&&r.words),this._mode.__creator=t)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t,e=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(e.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),e.unpad(t)),t},blockSize:4});var g=i.CipherParams=n.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),v=(r.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext,r=t.salt;return(r?s.create([1398893684,1701076831]).concat(r).concat(e):e).toString(h)},parse:function(t){var e,r=h.parse(t),i=r.words;return 1398893684==i[0]&&1701076831==i[1]&&(e=s.create(i.slice(2,4)),i.splice(0,4),r.sigBytes-=16),g.create({ciphertext:r,salt:e})}},y=i.SerializableCipher=n.extend({cfg:n.extend({format:v}),encrypt:function(t,e,r,i){i=this.cfg.extend(i);var n=t.createEncryptor(r,i),s=n.finalize(e),o=n.cfg;return g.create({ciphertext:s,key:r,iv:o.iv,algorithm:t,mode:o.mode,padding:o.padding,blockSize:t.blockSize,formatter:i.format})},decrypt:function(t,e,r,i){return i=this.cfg.extend(i),e=this._parse(e,i.format),t.createDecryptor(r,i).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),m=(r.kdf={}).OpenSSL={execute:function(t,e,r,i){i||(i=s.random(8));var n=c.create({keySize:e+r}).compute(t,i),o=s.create(n.words.slice(e),4*r);return n.sigBytes=4*e,g.create({key:n,iv:o,salt:i})}},b=i.PasswordBasedCipher=y.extend({cfg:y.cfg.extend({kdf:m}),encrypt:function(t,e,r,i){var n=(i=this.cfg.extend(i)).kdf.execute(r,t.keySize,t.ivSize);i.iv=n.iv;var s=y.encrypt.call(this,t,e,n.key,i);return s.mixIn(n),s},decrypt:function(t,e,r,i){i=this.cfg.extend(i),e=this._parse(e,i.format);var n=i.kdf.execute(r,t.keySize,t.ivSize,e.salt);return i.iv=n.iv,y.decrypt.call(this,t,e,n.key,i)}})}()))),ft.exports;var t}var dt,gt={exports:{}};var vt,yt={exports:{}};var mt,bt={exports:{}};function St(){return mt||(mt=1,bt.exports=(t=S(),pt(),
/** @preserve
         * Counter block mode compatible with  Dr Brian Gladman fileenc.c
         * derived from CryptoJS.mode.CTR
         * <NAME_EMAIL>
         */
t.mode.CTRGladman=function(){var e=t.lib.BlockCipherMode.extend();function r(t){if(255==(t>>24&255)){var e=t>>16&255,r=t>>8&255,i=255&t;255===e?(e=0,255===r?(r=0,255===i?i=0:++i):++r):++e,t=0,t+=e<<16,t+=r<<8,t+=i}else t+=1<<24;return t}function i(t){return 0===(t[0]=r(t[0]))&&(t[1]=r(t[1])),t}var n=e.Encryptor=e.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,s=this._iv,o=this._counter;s&&(o=this._counter=s.slice(0),this._iv=void 0),i(o);var a=o.slice(0);r.encryptBlock(a,0);for(var h=0;h<n;h++)t[e+h]^=a[h]}});return e.Decryptor=n,e}(),t.mode.CTRGladman)),bt.exports;var t}var wt,Et={exports:{}};var _t,xt={exports:{}};var Bt,Tt={exports:{}};var At,Dt={exports:{}};var Rt,kt={exports:{}};var Nt,It={exports:{}};var Ot,Ct={exports:{}};var Pt,Vt={exports:{}};var Ht,Mt={exports:{}};var jt,Lt={exports:{}};function Ut(){return jt||(jt=1,Lt.exports=(t=S(),I(),M(),ut(),pt(),function(){var e=t,r=e.lib,i=r.WordArray,n=r.BlockCipher,s=e.algo,o=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],h=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],u=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],l=s.DES=n.extend({_doReset:function(){for(var t=this._key.words,e=[],r=0;r<56;r++){var i=o[r]-1;e[r]=t[i>>>5]>>>31-i%32&1}for(var n=this._subKeys=[],s=0;s<16;s++){var c=n[s]=[],u=h[s];for(r=0;r<24;r++)c[r/6|0]|=e[(a[r]-1+u)%28]<<31-r%6,c[4+(r/6|0)]|=e[28+(a[r+24]-1+u)%28]<<31-r%6;for(c[0]=c[0]<<1|c[0]>>>31,r=1;r<7;r++)c[r]=c[r]>>>4*(r-1)+3;c[7]=c[7]<<5|c[7]>>>27}var l=this._invSubKeys=[];for(r=0;r<16;r++)l[r]=n[15-r]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(t,e,r){this._lBlock=t[e],this._rBlock=t[e+1],f.call(this,4,252645135),f.call(this,16,65535),p.call(this,2,858993459),p.call(this,8,16711935),f.call(this,1,1431655765);for(var i=0;i<16;i++){for(var n=r[i],s=this._lBlock,o=this._rBlock,a=0,h=0;h<8;h++)a|=c[h][((o^n[h])&u[h])>>>0];this._lBlock=o,this._rBlock=s^a}var l=this._lBlock;this._lBlock=this._rBlock,this._rBlock=l,f.call(this,1,1431655765),p.call(this,8,16711935),p.call(this,2,858993459),f.call(this,16,65535),f.call(this,4,252645135),t[e]=this._lBlock,t[e+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function f(t,e){var r=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=r,this._lBlock^=r<<t}function p(t,e){var r=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=r,this._rBlock^=r<<t}e.DES=n._createHelper(l);var d=s.TripleDES=n.extend({_doReset:function(){var t=this._key.words;if(2!==t.length&&4!==t.length&&t.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var e=t.slice(0,2),r=t.length<4?t.slice(0,2):t.slice(2,4),n=t.length<6?t.slice(0,2):t.slice(4,6);this._des1=l.createEncryptor(i.create(e)),this._des2=l.createEncryptor(i.create(r)),this._des3=l.createEncryptor(i.create(n))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=n._createHelper(d)}(),t.TripleDES)),Lt.exports;var t}var Kt,zt={exports:{}};var qt,Ft={exports:{}};var Wt,Gt,Yt,Zt,$t,Xt,Jt,Qt={exports:{}};d.exports=function(t){return t}(S(),_(),T(),R(),I(),P(),M(),U(),q(),F||(F=1,W.exports=(Jt=S(),q(),Yt=(Gt=Jt).lib.WordArray,Zt=Gt.algo,$t=Zt.SHA256,Xt=Zt.SHA224=$t.extend({_doReset:function(){this._hash=new Yt.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=$t._doFinalize.call(this);return t.sigBytes-=4,t}}),Gt.SHA224=$t._createHelper(Xt),Gt.HmacSHA224=$t._createHmacHelper(Xt),Jt.SHA224)),Z(),function(){return $||($=1,X.exports=(a=S(),_(),Z(),e=(t=a).x64,r=e.Word,i=e.WordArray,n=t.algo,s=n.SHA512,o=n.SHA384=s.extend({_doReset:function(){this._hash=new i.init([new r.init(3418070365,3238371032),new r.init(1654270250,914150663),new r.init(2438529370,812702999),new r.init(355462360,4144912697),new r.init(1731405415,4290775857),new r.init(2394180231,1750603025),new r.init(3675008525,1694076839),new r.init(1203062813,3204075428)])},_doFinalize:function(){var t=s._doFinalize.call(this);return t.sigBytes-=16,t}}),t.SHA384=s._createHelper(o),t.HmacSHA384=s._createHmacHelper(o),a.SHA384)),X.exports;var t,e,r,i,n,s,o,a}(),tt(),function(){return et||(et=1,rt.exports=(t=S(),
/** @preserve
            			(c) 2012 by Cédric Mesnil. All rights reserved.
        
            			Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
        
            			    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
            			    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
        
            			THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
            			*/
function(e){var r=t,i=r.lib,n=i.WordArray,s=i.Hasher,o=r.algo,a=n.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),h=n.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),c=n.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),u=n.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),l=n.create([0,1518500249,1859775393,2400959708,2840853838]),f=n.create([1352829926,1548603684,1836072691,2053994217,0]),p=o.RIPEMD160=s.extend({_doReset:function(){this._hash=n.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var i=e+r,n=t[i];t[i]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8)}var s,o,p,S,w,E,_,x,B,T,A,D=this._hash.words,R=l.words,k=f.words,N=a.words,I=h.words,O=c.words,C=u.words;for(E=s=D[0],_=o=D[1],x=p=D[2],B=S=D[3],T=w=D[4],r=0;r<80;r+=1)A=s+t[e+N[r]]|0,A+=r<16?d(o,p,S)+R[0]:r<32?g(o,p,S)+R[1]:r<48?v(o,p,S)+R[2]:r<64?y(o,p,S)+R[3]:m(o,p,S)+R[4],A=(A=b(A|=0,O[r]))+w|0,s=w,w=S,S=b(p,10),p=o,o=A,A=E+t[e+I[r]]|0,A+=r<16?m(_,x,B)+k[0]:r<32?y(_,x,B)+k[1]:r<48?v(_,x,B)+k[2]:r<64?g(_,x,B)+k[3]:d(_,x,B)+k[4],A=(A=b(A|=0,C[r]))+T|0,E=T,T=B,B=b(x,10),x=_,_=A;A=D[1]+p+B|0,D[1]=D[2]+S+T|0,D[2]=D[3]+w+E|0,D[3]=D[4]+s+_|0,D[4]=D[0]+o+x|0,D[0]=A},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;e[i>>>5]|=128<<24-i%32,e[14+(i+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process();for(var n=this._hash,s=n.words,o=0;o<5;o++){var a=s[o];s[o]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return n},clone:function(){var t=s.clone.call(this);return t._hash=this._hash.clone(),t}});function d(t,e,r){return t^e^r}function g(t,e,r){return t&e|~t&r}function v(t,e,r){return(t|~e)^r}function y(t,e,r){return t&r|e&~r}function m(t,e,r){return t^(e|~r)}function b(t,e){return t<<e|t>>>32-e}r.RIPEMD160=s._createHelper(p),r.HmacRIPEMD160=s._createHmacHelper(p)}(),t.RIPEMD160)),rt.exports;var t}(),st(),function(){return ot||(ot=1,at.exports=(h=S(),U(),st(),e=(t=h).lib,r=e.Base,i=e.WordArray,n=t.algo,s=n.SHA1,o=n.HMAC,a=n.PBKDF2=r.extend({cfg:r.extend({keySize:4,hasher:s,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r=this.cfg,n=o.create(r.hasher,t),s=i.create(),a=i.create([1]),h=s.words,c=a.words,u=r.keySize,l=r.iterations;h.length<u;){var f=n.update(e).finalize(a);n.reset();for(var p=f.words,d=p.length,g=f,v=1;v<l;v++){g=n.finalize(g),n.reset();for(var y=g.words,m=0;m<d;m++)p[m]^=y[m]}s.concat(f),c[0]++}return s.sigBytes=4*u,s}}),t.PBKDF2=function(t,e,r){return a.create(r).compute(t,e)},h.PBKDF2)),at.exports;var t,e,r,i,n,s,o,a,h}(),ut(),pt(),function(){return dt||(dt=1,gt.exports=(t=S(),pt(),t.mode.CFB=function(){var e=t.lib.BlockCipherMode.extend();function r(t,e,r,i){var n,s=this._iv;s?(n=s.slice(0),this._iv=void 0):n=this._prevBlock,i.encryptBlock(n,0);for(var o=0;o<r;o++)t[e+o]^=n[o]}return e.Encryptor=e.extend({processBlock:function(t,e){var i=this._cipher,n=i.blockSize;r.call(this,t,e,n,i),this._prevBlock=t.slice(e,e+n)}}),e.Decryptor=e.extend({processBlock:function(t,e){var i=this._cipher,n=i.blockSize,s=t.slice(e,e+n);r.call(this,t,e,n,i),this._prevBlock=s}}),e}(),t.mode.CFB)),gt.exports;var t}(),function(){return vt||(vt=1,yt.exports=(r=S(),pt(),r.mode.CTR=(t=r.lib.BlockCipherMode.extend(),e=t.Encryptor=t.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,n=this._iv,s=this._counter;n&&(s=this._counter=n.slice(0),this._iv=void 0);var o=s.slice(0);r.encryptBlock(o,0),s[i-1]=s[i-1]+1|0;for(var a=0;a<i;a++)t[e+a]^=o[a]}}),t.Decryptor=e,t),r.mode.CTR)),yt.exports;var t,e,r}(),St(),function(){return wt||(wt=1,Et.exports=(r=S(),pt(),r.mode.OFB=(t=r.lib.BlockCipherMode.extend(),e=t.Encryptor=t.extend({processBlock:function(t,e){var r=this._cipher,i=r.blockSize,n=this._iv,s=this._keystream;n&&(s=this._keystream=n.slice(0),this._iv=void 0),r.encryptBlock(s,0);for(var o=0;o<i;o++)t[e+o]^=s[o]}}),t.Decryptor=e,t),r.mode.OFB)),Et.exports;var t,e,r}(),function(){return _t||(_t=1,xt.exports=(e=S(),pt(),e.mode.ECB=((t=e.lib.BlockCipherMode.extend()).Encryptor=t.extend({processBlock:function(t,e){this._cipher.encryptBlock(t,e)}}),t.Decryptor=t.extend({processBlock:function(t,e){this._cipher.decryptBlock(t,e)}}),t),e.mode.ECB)),xt.exports;var t,e}(),function(){return Bt||(Bt=1,Tt.exports=(t=S(),pt(),t.pad.AnsiX923={pad:function(t,e){var r=t.sigBytes,i=4*e,n=i-r%i,s=r+n-1;t.clamp(),t.words[s>>>2]|=n<<24-s%4*8,t.sigBytes+=n},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Ansix923)),Tt.exports;var t}(),function(){return At||(At=1,Dt.exports=(t=S(),pt(),t.pad.Iso10126={pad:function(e,r){var i=4*r,n=i-e.sigBytes%i;e.concat(t.lib.WordArray.random(n-1)).concat(t.lib.WordArray.create([n<<24],1))},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},t.pad.Iso10126)),Dt.exports;var t}(),function(){return Rt||(Rt=1,kt.exports=(t=S(),pt(),t.pad.Iso97971={pad:function(e,r){e.concat(t.lib.WordArray.create([2147483648],1)),t.pad.ZeroPadding.pad(e,r)},unpad:function(e){t.pad.ZeroPadding.unpad(e),e.sigBytes--}},t.pad.Iso97971)),kt.exports;var t}(),function(){return Nt||(Nt=1,It.exports=(t=S(),pt(),t.pad.ZeroPadding={pad:function(t,e){var r=4*e;t.clamp(),t.sigBytes+=r-(t.sigBytes%r||r)},unpad:function(t){var e=t.words,r=t.sigBytes-1;for(r=t.sigBytes-1;r>=0;r--)if(e[r>>>2]>>>24-r%4*8&255){t.sigBytes=r+1;break}}},t.pad.ZeroPadding)),It.exports;var t}(),function(){return Ot||(Ot=1,Ct.exports=(t=S(),pt(),t.pad.NoPadding={pad:function(){},unpad:function(){}},t.pad.NoPadding)),Ct.exports;var t}(),function(){return Pt||(Pt=1,Vt.exports=(i=S(),pt(),e=(t=i).lib.CipherParams,r=t.enc.Hex,t.format.Hex={stringify:function(t){return t.ciphertext.toString(r)},parse:function(t){var i=r.parse(t);return e.create({ciphertext:i})}},i.format.Hex)),Vt.exports;var t,e,r,i}(),function(){return Ht||(Ht=1,Mt.exports=(t=S(),I(),M(),ut(),pt(),function(){var e=t,r=e.lib.BlockCipher,i=e.algo,n=[],s=[],o=[],a=[],h=[],c=[],u=[],l=[],f=[],p=[];!function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;var r=0,i=0;for(e=0;e<256;e++){var d=i^i<<1^i<<2^i<<3^i<<4;d=d>>>8^255&d^99,n[r]=d,s[d]=r;var g=t[r],v=t[g],y=t[v],m=257*t[d]^16843008*d;o[r]=m<<24|m>>>8,a[r]=m<<16|m>>>16,h[r]=m<<8|m>>>24,c[r]=m,m=16843009*y^65537*v^257*g^16843008*r,u[d]=m<<24|m>>>8,l[d]=m<<16|m>>>16,f[d]=m<<8|m>>>24,p[d]=m,r?(r=g^t[t[t[y^g]]],i^=t[t[i]]):r=i=1}}();var d=[0,1,2,4,8,16,32,64,128,27,54],g=i.AES=r.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4,i=4*((this._nRounds=r+6)+1),s=this._keySchedule=[],o=0;o<i;o++)o<r?s[o]=e[o]:(c=s[o-1],o%r?r>6&&o%r==4&&(c=n[c>>>24]<<24|n[c>>>16&255]<<16|n[c>>>8&255]<<8|n[255&c]):(c=n[(c=c<<8|c>>>24)>>>24]<<24|n[c>>>16&255]<<16|n[c>>>8&255]<<8|n[255&c],c^=d[o/r|0]<<24),s[o]=s[o-r]^c);for(var a=this._invKeySchedule=[],h=0;h<i;h++){if(o=i-h,h%4)var c=s[o];else c=s[o-4];a[h]=h<4||o<=4?c:u[n[c>>>24]]^l[n[c>>>16&255]]^f[n[c>>>8&255]]^p[n[255&c]]}}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,o,a,h,c,n)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,u,l,f,p,s),r=t[e+1],t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,i,n,s,o,a){for(var h=this._nRounds,c=t[e]^r[0],u=t[e+1]^r[1],l=t[e+2]^r[2],f=t[e+3]^r[3],p=4,d=1;d<h;d++){var g=i[c>>>24]^n[u>>>16&255]^s[l>>>8&255]^o[255&f]^r[p++],v=i[u>>>24]^n[l>>>16&255]^s[f>>>8&255]^o[255&c]^r[p++],y=i[l>>>24]^n[f>>>16&255]^s[c>>>8&255]^o[255&u]^r[p++],m=i[f>>>24]^n[c>>>16&255]^s[u>>>8&255]^o[255&l]^r[p++];c=g,u=v,l=y,f=m}g=(a[c>>>24]<<24|a[u>>>16&255]<<16|a[l>>>8&255]<<8|a[255&f])^r[p++],v=(a[u>>>24]<<24|a[l>>>16&255]<<16|a[f>>>8&255]<<8|a[255&c])^r[p++],y=(a[l>>>24]<<24|a[f>>>16&255]<<16|a[c>>>8&255]<<8|a[255&u])^r[p++],m=(a[f>>>24]<<24|a[c>>>16&255]<<16|a[u>>>8&255]<<8|a[255&l])^r[p++],t[e]=g,t[e+1]=v,t[e+2]=y,t[e+3]=m},keySize:8});e.AES=r._createHelper(g)}(),t.AES)),Mt.exports;var t}(),Ut(),function(){return Kt||(Kt=1,zt.exports=(t=S(),I(),M(),ut(),pt(),function(){var e=t,r=e.lib.StreamCipher,i=e.algo,n=i.RC4=r.extend({_doReset:function(){for(var t=this._key,e=t.words,r=t.sigBytes,i=this._S=[],n=0;n<256;n++)i[n]=n;n=0;for(var s=0;n<256;n++){var o=n%r,a=e[o>>>2]>>>24-o%4*8&255;s=(s+i[n]+a)%256;var h=i[n];i[n]=i[s],i[s]=h}this._i=this._j=0},_doProcessBlock:function(t,e){t[e]^=s.call(this)},keySize:8,ivSize:0});function s(){for(var t=this._S,e=this._i,r=this._j,i=0,n=0;n<4;n++){r=(r+t[e=(e+1)%256])%256;var s=t[e];t[e]=t[r],t[r]=s,i|=t[(t[e]+t[r])%256]<<24-8*n}return this._i=e,this._j=r,i}e.RC4=r._createHelper(n);var o=i.RC4Drop=n.extend({cfg:n.cfg.extend({drop:192}),_doReset:function(){n._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)s.call(this)}});e.RC4Drop=r._createHelper(o)}(),t.RC4)),zt.exports;var t}(),function(){return qt||(qt=1,Ft.exports=(t=S(),I(),M(),ut(),pt(),function(){var e=t,r=e.lib.StreamCipher,i=e.algo,n=[],s=[],o=[],a=i.Rabbit=r.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,r=0;r<4;r++)t[r]=16711935&(t[r]<<8|t[r]>>>24)|4278255360&(t[r]<<24|t[r]>>>8);var i=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],n=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];for(this._b=0,r=0;r<4;r++)h.call(this);for(r=0;r<8;r++)n[r]^=i[r+4&7];if(e){var s=e.words,o=s[0],a=s[1],c=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=c>>>16|4294901760&u,f=u<<16|65535&c;for(n[0]^=c,n[1]^=l,n[2]^=u,n[3]^=f,n[4]^=c,n[5]^=l,n[6]^=u,n[7]^=f,r=0;r<4;r++)h.call(this)}},_doProcessBlock:function(t,e){var r=this._X;h.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)n[i]=16711935&(n[i]<<8|n[i]>>>24)|4278255360&(n[i]<<24|n[i]>>>8),t[e+i]^=n[i]},blockSize:4,ivSize:2});function h(){for(var t=this._X,e=this._C,r=0;r<8;r++)s[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<s[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<s[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<s[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<s[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<s[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<s[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<s[6]>>>0?1:0)|0,this._b=e[7]>>>0<s[7]>>>0?1:0,r=0;r<8;r++){var i=t[r]+e[r],n=65535&i,a=i>>>16,h=((n*n>>>17)+n*a>>>15)+a*a,c=((4294901760&i)*i|0)+((65535&i)*i|0);o[r]=h^c}t[0]=o[0]+(o[7]<<16|o[7]>>>16)+(o[6]<<16|o[6]>>>16)|0,t[1]=o[1]+(o[0]<<8|o[0]>>>24)+o[7]|0,t[2]=o[2]+(o[1]<<16|o[1]>>>16)+(o[0]<<16|o[0]>>>16)|0,t[3]=o[3]+(o[2]<<8|o[2]>>>24)+o[1]|0,t[4]=o[4]+(o[3]<<16|o[3]>>>16)+(o[2]<<16|o[2]>>>16)|0,t[5]=o[5]+(o[4]<<8|o[4]>>>24)+o[3]|0,t[6]=o[6]+(o[5]<<16|o[5]>>>16)+(o[4]<<16|o[4]>>>16)|0,t[7]=o[7]+(o[6]<<8|o[6]>>>24)+o[5]|0}e.Rabbit=r._createHelper(a)}(),t.Rabbit)),Ft.exports;var t}(),function(){return Wt||(Wt=1,Qt.exports=(t=S(),I(),M(),ut(),pt(),function(){var e=t,r=e.lib.StreamCipher,i=e.algo,n=[],s=[],o=[],a=i.RabbitLegacy=r.extend({_doReset:function(){var t=this._key.words,e=this.cfg.iv,r=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],i=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(var n=0;n<4;n++)h.call(this);for(n=0;n<8;n++)i[n]^=r[n+4&7];if(e){var s=e.words,o=s[0],a=s[1],c=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),u=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=c>>>16|4294901760&u,f=u<<16|65535&c;for(i[0]^=c,i[1]^=l,i[2]^=u,i[3]^=f,i[4]^=c,i[5]^=l,i[6]^=u,i[7]^=f,n=0;n<4;n++)h.call(this)}},_doProcessBlock:function(t,e){var r=this._X;h.call(this),n[0]=r[0]^r[5]>>>16^r[3]<<16,n[1]=r[2]^r[7]>>>16^r[5]<<16,n[2]=r[4]^r[1]>>>16^r[7]<<16,n[3]=r[6]^r[3]>>>16^r[1]<<16;for(var i=0;i<4;i++)n[i]=16711935&(n[i]<<8|n[i]>>>24)|4278255360&(n[i]<<24|n[i]>>>8),t[e+i]^=n[i]},blockSize:4,ivSize:2});function h(){for(var t=this._X,e=this._C,r=0;r<8;r++)s[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<s[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<s[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<s[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<s[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<s[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<s[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<s[6]>>>0?1:0)|0,this._b=e[7]>>>0<s[7]>>>0?1:0,r=0;r<8;r++){var i=t[r]+e[r],n=65535&i,a=i>>>16,h=((n*n>>>17)+n*a>>>15)+a*a,c=((4294901760&i)*i|0)+((65535&i)*i|0);o[r]=h^c}t[0]=o[0]+(o[7]<<16|o[7]>>>16)+(o[6]<<16|o[6]>>>16)|0,t[1]=o[1]+(o[0]<<8|o[0]>>>24)+o[7]|0,t[2]=o[2]+(o[1]<<16|o[1]>>>16)+(o[0]<<16|o[0]>>>16)|0,t[3]=o[3]+(o[2]<<8|o[2]>>>24)+o[1]|0,t[4]=o[4]+(o[3]<<16|o[3]>>>16)+(o[2]<<16|o[2]>>>16)|0,t[5]=o[5]+(o[4]<<8|o[4]>>>24)+o[3]|0,t[6]=o[6]+(o[5]<<16|o[5]>>>16)+(o[4]<<16|o[4]>>>16)|0,t[7]=o[7]+(o[6]<<8|o[6]>>>24)+o[5]|0}e.RabbitLegacy=r._createHelper(a)}(),t.RabbitLegacy)),Qt.exports;var t}());const te=f(d.exports);function ee(t){return"0********9abcdefghijklmnopqrstuvwxyz".charAt(t)}function re(t,e){return t&e}function ie(t,e){return t|e}function ne(t,e){return t^e}function se(t,e){return t&~e}function oe(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function ae(t){for(var e=0;0!=t;)t&=t-1,++e;return e}var he,ce="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0********9+/";function ue(t){var e,r,i="";for(e=0;e+3<=t.length;e+=3)r=parseInt(t.substring(e,e+3),16),i+=ce.charAt(r>>6)+ce.charAt(63&r);for(e+1==t.length?(r=parseInt(t.substring(e,e+1),16),i+=ce.charAt(r<<2)):e+2==t.length&&(r=parseInt(t.substring(e,e+2),16),i+=ce.charAt(r>>2)+ce.charAt((3&r)<<4));(3&i.length)>0;)i+="=";return i}function le(t){var e,r="",i=0,n=0;for(e=0;e<t.length&&"="!=t.charAt(e);++e){var s=ce.indexOf(t.charAt(e));s<0||(0==i?(r+=ee(s>>2),n=3&s,i=1):1==i?(r+=ee(n<<2|s>>4),n=15&s,i=2):2==i?(r+=ee(n),r+=ee(s>>2),n=3&s,i=3):(r+=ee(n<<2|s>>4),r+=ee(15&s),i=0))}return 1==i&&(r+=ee(n<<2)),r}var fe,pe=function(t){var e;if(void 0===he){var r="0********9ABCDEF",i=" \f\n\r\t \u2028\u2029";for(he={},e=0;e<16;++e)he[r.charAt(e)]=e;for(r=r.toLowerCase(),e=10;e<16;++e)he[r.charAt(e)]=e;for(e=0;e<i.length;++e)he[i.charAt(e)]=-1}var n=[],s=0,o=0;for(e=0;e<t.length;++e){var a=t.charAt(e);if("="==a)break;if(-1!=(a=he[a])){if(void 0===a)throw new Error("Illegal character at offset "+e);s|=a,++o>=2?(n[n.length]=s,s=0,o=0):s<<=4}}if(o)throw new Error("Hex encoding incomplete: 4 bits missing");return n},de={decode:function(t){var e;if(void 0===fe){var r="= \f\n\r\t \u2028\u2029";for(fe=Object.create(null),e=0;e<64;++e)fe["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0********9+/".charAt(e)]=e;for(fe["-"]=62,fe._=63,e=0;e<r.length;++e)fe[r.charAt(e)]=-1}var i=[],n=0,s=0;for(e=0;e<t.length;++e){var o=t.charAt(e);if("="==o)break;if(-1!=(o=fe[o])){if(void 0===o)throw new Error("Illegal character at offset "+e);n|=o,++s>=4?(i[i.length]=n>>16,i[i.length]=n>>8&255,i[i.length]=255&n,n=0,s=0):n<<=6}}switch(s){case 1:throw new Error("Base64 encoding incomplete: at least 2 bits missing");case 2:i[i.length]=n>>10;break;case 3:i[i.length]=n>>16,i[i.length]=n>>8&255}return i},re:/-----BEGIN [^-]+-----([A-Za-z0-9+\/=\s]+)-----END [^-]+-----|begin-base64[^\n]+\n([A-Za-z0-9+\/=\s]+)====/,unarmor:function(t){var e=de.re.exec(t);if(e)if(e[1])t=e[1];else{if(!e[2])throw new Error("RegExp out of sync");t=e[2]}return de.decode(t)}},ge=1e13,ve=function(){function t(t){this.buf=[+t||0]}return t.prototype.mulAdd=function(t,e){var r,i,n=this.buf,s=n.length;for(r=0;r<s;++r)(i=n[r]*t+e)<ge?e=0:i-=(e=0|i/ge)*ge,n[r]=i;e>0&&(n[r]=e)},t.prototype.sub=function(t){var e,r,i=this.buf,n=i.length;for(e=0;e<n;++e)(r=i[e]-t)<0?(r+=ge,t=1):t=0,i[e]=r;for(;0===i[i.length-1];)i.pop()},t.prototype.toString=function(t){if(10!=(t||10))throw new Error("only base 10 is supported");for(var e=this.buf,r=e[e.length-1].toString(),i=e.length-2;i>=0;--i)r+=(ge+e[i]).toString().substring(1);return r},t.prototype.valueOf=function(){for(var t=this.buf,e=0,r=t.length-1;r>=0;--r)e=e*ge+t[r];return e},t.prototype.simplify=function(){var t=this.buf;return 1==t.length?t[0]:this},t}(),ye=/^(\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/,me=/^(\d\d\d\d)(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])([01]\d|2[0-3])(?:([0-5]\d)(?:([0-5]\d)(?:[.,](\d{1,3}))?)?)?(Z|[-+](?:[0]\d|1[0-2])([0-5]\d)?)?$/;function be(t,e){return t.length>e&&(t=t.substring(0,e)+"…"),t}var Se,we=function(){function t(e,r){this.hexDigits="0********9ABCDEF",e instanceof t?(this.enc=e.enc,this.pos=e.pos):(this.enc=e,this.pos=r)}return t.prototype.get=function(t){if(void 0===t&&(t=this.pos++),t>=this.enc.length)throw new Error("Requesting byte offset ".concat(t," on a stream of length ").concat(this.enc.length));return"string"==typeof this.enc?this.enc.charCodeAt(t):this.enc[t]},t.prototype.hexByte=function(t){return this.hexDigits.charAt(t>>4&15)+this.hexDigits.charAt(15&t)},t.prototype.hexDump=function(t,e,r){for(var i="",n=t;n<e;++n)if(i+=this.hexByte(this.get(n)),!0!==r)switch(15&n){case 7:i+="  ";break;case 15:i+="\n";break;default:i+=" "}return i},t.prototype.isASCII=function(t,e){for(var r=t;r<e;++r){var i=this.get(r);if(i<32||i>176)return!1}return!0},t.prototype.parseStringISO=function(t,e){for(var r="",i=t;i<e;++i)r+=String.fromCharCode(this.get(i));return r},t.prototype.parseStringUTF=function(t,e){for(var r="",i=t;i<e;){var n=this.get(i++);r+=n<128?String.fromCharCode(n):n>191&&n<224?String.fromCharCode((31&n)<<6|63&this.get(i++)):String.fromCharCode((15&n)<<12|(63&this.get(i++))<<6|63&this.get(i++))}return r},t.prototype.parseStringBMP=function(t,e){for(var r,i,n="",s=t;s<e;)r=this.get(s++),i=this.get(s++),n+=String.fromCharCode(r<<8|i);return n},t.prototype.parseTime=function(t,e,r){var i=this.parseStringISO(t,e),n=(r?ye:me).exec(i);return n?(r&&(n[1]=+n[1],n[1]+=+n[1]<70?2e3:1900),i=n[1]+"-"+n[2]+"-"+n[3]+" "+n[4],n[5]&&(i+=":"+n[5],n[6]&&(i+=":"+n[6],n[7]&&(i+="."+n[7]))),n[8]&&(i+=" UTC","Z"!=n[8]&&(i+=n[8],n[9]&&(i+=":"+n[9]))),i):"Unrecognized time: "+i},t.prototype.parseInteger=function(t,e){for(var r,i=this.get(t),n=i>127,s=n?255:0,o="";i==s&&++t<e;)i=this.get(t);if(0===(r=e-t))return n?-1:0;if(r>4){for(o=i,r<<=3;0==(128&(+o^s));)o=+o<<1,--r;o="("+r+" bit)\n"}n&&(i-=256);for(var a=new ve(i),h=t+1;h<e;++h)a.mulAdd(256,this.get(h));return o+a.toString()},t.prototype.parseBitString=function(t,e,r){for(var i=this.get(t),n="("+((e-t-1<<3)-i)+" bit)\n",s="",o=t+1;o<e;++o){for(var a=this.get(o),h=o==e-1?i:0,c=7;c>=h;--c)s+=a>>c&1?"1":"0";if(s.length>r)return n+be(s,r)}return n+s},t.prototype.parseOctetString=function(t,e,r){if(this.isASCII(t,e))return be(this.parseStringISO(t,e),r);var i=e-t,n="("+i+" byte)\n";i>(r/=2)&&(e=t+r);for(var s=t;s<e;++s)n+=this.hexByte(this.get(s));return i>r&&(n+="…"),n},t.prototype.parseOID=function(t,e,r){for(var i="",n=new ve,s=0,o=t;o<e;++o){var a=this.get(o);if(n.mulAdd(128,127&a),s+=7,!(128&a)){if(""===i)if((n=n.simplify())instanceof ve)n.sub(80),i="2."+n.toString();else{var h=n<80?n<40?0:1:2;i=h+"."+(n-40*h)}else i+="."+n.toString();if(i.length>r)return be(i,r);n=new ve,s=0}}return s>0&&(i+=".incomplete"),i},t}(),Ee=function(){function t(t,e,r,i,n){if(!(i instanceof _e))throw new Error("Invalid tag value.");this.stream=t,this.header=e,this.length=r,this.tag=i,this.sub=n}return t.prototype.typeName=function(){switch(this.tag.tagClass){case 0:switch(this.tag.tagNumber){case 0:return"EOC";case 1:return"BOOLEAN";case 2:return"INTEGER";case 3:return"BIT_STRING";case 4:return"OCTET_STRING";case 5:return"NULL";case 6:return"OBJECT_IDENTIFIER";case 7:return"ObjectDescriptor";case 8:return"EXTERNAL";case 9:return"REAL";case 10:return"ENUMERATED";case 11:return"EMBEDDED_PDV";case 12:return"UTF8String";case 16:return"SEQUENCE";case 17:return"SET";case 18:return"NumericString";case 19:return"PrintableString";case 20:return"TeletexString";case 21:return"VideotexString";case 22:return"IA5String";case 23:return"UTCTime";case 24:return"GeneralizedTime";case 25:return"GraphicString";case 26:return"VisibleString";case 27:return"GeneralString";case 28:return"UniversalString";case 30:return"BMPString"}return"Universal_"+this.tag.tagNumber.toString();case 1:return"Application_"+this.tag.tagNumber.toString();case 2:return"["+this.tag.tagNumber.toString()+"]";case 3:return"Private_"+this.tag.tagNumber.toString()}},t.prototype.content=function(t){if(void 0===this.tag)return null;void 0===t&&(t=1/0);var e=this.posContent(),r=Math.abs(this.length);if(!this.tag.isUniversal())return null!==this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);switch(this.tag.tagNumber){case 1:return 0===this.stream.get(e)?"false":"true";case 2:return this.stream.parseInteger(e,e+r);case 3:return this.sub?"("+this.sub.length+" elem)":this.stream.parseBitString(e,e+r,t);case 4:return this.sub?"("+this.sub.length+" elem)":this.stream.parseOctetString(e,e+r,t);case 6:return this.stream.parseOID(e,e+r,t);case 16:case 17:return null!==this.sub?"("+this.sub.length+" elem)":"(no elem)";case 12:return be(this.stream.parseStringUTF(e,e+r),t);case 18:case 19:case 20:case 21:case 22:case 26:return be(this.stream.parseStringISO(e,e+r),t);case 30:return be(this.stream.parseStringBMP(e,e+r),t);case 23:case 24:return this.stream.parseTime(e,e+r,23==this.tag.tagNumber)}return null},t.prototype.toString=function(){return this.typeName()+"@"+this.stream.pos+"[header:"+this.header+",length:"+this.length+",sub:"+(null===this.sub?"null":this.sub.length)+"]"},t.prototype.toPrettyString=function(t){void 0===t&&(t="");var e=t+this.typeName()+" @"+this.stream.pos;if(this.length>=0&&(e+="+"),e+=this.length,this.tag.tagConstructed?e+=" (constructed)":!this.tag.isUniversal()||3!=this.tag.tagNumber&&4!=this.tag.tagNumber||null===this.sub||(e+=" (encapsulates)"),e+="\n",null!==this.sub){t+="  ";for(var r=0,i=this.sub.length;r<i;++r)e+=this.sub[r].toPrettyString(t)}return e},t.prototype.posStart=function(){return this.stream.pos},t.prototype.posContent=function(){return this.stream.pos+this.header},t.prototype.posEnd=function(){return this.stream.pos+this.header+Math.abs(this.length)},t.prototype.toHexString=function(){return this.stream.hexDump(this.posStart(),this.posEnd(),!0)},t.decodeLength=function(t){var e=t.get(),r=127&e;if(r==e)return r;if(r>6)throw new Error("Length over 48 bits not supported at position "+(t.pos-1));if(0===r)return null;e=0;for(var i=0;i<r;++i)e=256*e+t.get();return e},t.prototype.getHexStringValue=function(){var t=this.toHexString(),e=2*this.header,r=2*this.length;return t.substr(e,r)},t.decode=function(e){var r;r=e instanceof we?e:new we(e,0);var i=new we(r),n=new _e(r),s=t.decodeLength(r),o=r.pos,a=o-i.pos,h=null,c=function(){var e=[];if(null!==s){for(var i=o+s;r.pos<i;)e[e.length]=t.decode(r);if(r.pos!=i)throw new Error("Content size is not correct for container starting at offset "+o)}else try{for(;;){var n=t.decode(r);if(n.tag.isEOC())break;e[e.length]=n}s=o-r.pos}catch(a){throw new Error("Exception while decoding undefined length content: "+a)}return e};if(n.tagConstructed)h=c();else if(n.isUniversal()&&(3==n.tagNumber||4==n.tagNumber))try{if(3==n.tagNumber&&0!=r.get())throw new Error("BIT STRINGs with unused bits cannot encapsulate.");h=c();for(var u=0;u<h.length;++u)if(h[u].tag.isEOC())throw new Error("EOC is not supposed to be actual content.")}catch(l){h=null}if(null===h){if(null===s)throw new Error("We can't skip over an invalid tag with undefined length at offset "+o);r.pos=o+Math.abs(s)}return new t(i,a,s,n,h)},t}(),_e=function(){function t(t){var e=t.get();if(this.tagClass=e>>6,this.tagConstructed=0!=(32&e),this.tagNumber=31&e,31==this.tagNumber){var r=new ve;do{e=t.get(),r.mulAdd(128,127&e)}while(128&e);this.tagNumber=r.simplify()}}return t.prototype.isUniversal=function(){return 0===this.tagClass},t.prototype.isEOC=function(){return 0===this.tagClass&&0===this.tagNumber},t}(),xe=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],Be=(1<<26)/xe[xe.length-1],Te=function(){function t(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}return t.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,i=(1<<e)-1,n=!1,s="",o=this.t,a=this.DB-o*this.DB%e;if(o-- >0)for(a<this.DB&&(r=this[o]>>a)>0&&(n=!0,s=ee(r));o>=0;)a<e?(r=(this[o]&(1<<a)-1)<<e-a,r|=this[--o]>>(a+=this.DB-e)):(r=this[o]>>(a-=e)&i,a<=0&&(a+=this.DB,--o)),r>0&&(n=!0),n&&(s+=ee(r));return n?s:"0"},t.prototype.negate=function(){var e=Ne();return t.ZERO.subTo(this,e),e},t.prototype.abs=function(){return this.s<0?this.negate():this},t.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},t.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+je(this[this.t-1]^this.s&this.DM)},t.prototype.mod=function(e){var r=Ne();return this.abs().divRemTo(e,null,r),this.s<0&&r.compareTo(t.ZERO)>0&&e.subTo(r,r),r},t.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new De(e):new Re(e),this.exp(t,r)},t.prototype.clone=function(){var t=Ne();return this.copyTo(t),t},t.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},t.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},t.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},t.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},t.prototype.toByteArray=function(){var t=this.t,e=[];e[0]=this.s;var r,i=this.DB-t*this.DB%8,n=0;if(t-- >0)for(i<this.DB&&(r=this[t]>>i)!=(this.s&this.DM)>>i&&(e[n++]=r|this.s<<this.DB-i);t>=0;)i<8?(r=(this[t]&(1<<i)-1)<<8-i,r|=this[--t]>>(i+=this.DB-8)):(r=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==n&&(128&this.s)!=(128&r)&&++n,(n>0||r!=this.s)&&(e[n++]=r);return e},t.prototype.equals=function(t){return 0==this.compareTo(t)},t.prototype.min=function(t){return this.compareTo(t)<0?this:t},t.prototype.max=function(t){return this.compareTo(t)>0?this:t},t.prototype.and=function(t){var e=Ne();return this.bitwiseTo(t,re,e),e},t.prototype.or=function(t){var e=Ne();return this.bitwiseTo(t,ie,e),e},t.prototype.xor=function(t){var e=Ne();return this.bitwiseTo(t,ne,e),e},t.prototype.andNot=function(t){var e=Ne();return this.bitwiseTo(t,se,e),e},t.prototype.not=function(){for(var t=Ne(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},t.prototype.shiftLeft=function(t){var e=Ne();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},t.prototype.shiftRight=function(t){var e=Ne();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},t.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+oe(this[t]);return this.s<0?this.t*this.DB:-1},t.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=ae(this[r]^e);return t},t.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},t.prototype.setBit=function(t){return this.changeBit(t,ie)},t.prototype.clearBit=function(t){return this.changeBit(t,se)},t.prototype.flipBit=function(t){return this.changeBit(t,ne)},t.prototype.add=function(t){var e=Ne();return this.addTo(t,e),e},t.prototype.subtract=function(t){var e=Ne();return this.subTo(t,e),e},t.prototype.multiply=function(t){var e=Ne();return this.multiplyTo(t,e),e},t.prototype.divide=function(t){var e=Ne();return this.divRemTo(t,e,null),e},t.prototype.remainder=function(t){var e=Ne();return this.divRemTo(t,null,e),e},t.prototype.divideAndRemainder=function(t){var e=Ne(),r=Ne();return this.divRemTo(t,e,r),[e,r]},t.prototype.modPow=function(t,e){var r,i,n=t.bitLength(),s=Me(1);if(n<=0)return s;r=n<18?1:n<48?3:n<144?4:n<768?5:6,i=n<8?new De(e):e.isEven()?new ke(e):new Re(e);var o=[],a=3,h=r-1,c=(1<<r)-1;if(o[1]=i.convert(this),r>1){var u=Ne();for(i.sqrTo(o[1],u);a<=c;)o[a]=Ne(),i.mulTo(u,o[a-2],o[a]),a+=2}var l,f,p=t.t-1,d=!0,g=Ne();for(n=je(t[p])-1;p>=0;){for(n>=h?l=t[p]>>n-h&c:(l=(t[p]&(1<<n+1)-1)<<h-n,p>0&&(l|=t[p-1]>>this.DB+n-h)),a=r;0==(1&l);)l>>=1,--a;if((n-=a)<0&&(n+=this.DB,--p),d)o[l].copyTo(s),d=!1;else{for(;a>1;)i.sqrTo(s,g),i.sqrTo(g,s),a-=2;a>0?i.sqrTo(s,g):(f=s,s=g,g=f),i.mulTo(g,o[l],s)}for(;p>=0&&0==(t[p]&1<<n);)i.sqrTo(s,g),f=s,s=g,g=f,--n<0&&(n=this.DB-1,--p)}return i.revert(s)},t.prototype.modInverse=function(e){var r=e.isEven();if(this.isEven()&&r||0==e.signum())return t.ZERO;for(var i=e.clone(),n=this.clone(),s=Me(1),o=Me(0),a=Me(0),h=Me(1);0!=i.signum();){for(;i.isEven();)i.rShiftTo(1,i),r?(s.isEven()&&o.isEven()||(s.addTo(this,s),o.subTo(e,o)),s.rShiftTo(1,s)):o.isEven()||o.subTo(e,o),o.rShiftTo(1,o);for(;n.isEven();)n.rShiftTo(1,n),r?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(e,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(e,h),h.rShiftTo(1,h);i.compareTo(n)>=0?(i.subTo(n,i),r&&s.subTo(a,s),o.subTo(h,o)):(n.subTo(i,n),r&&a.subTo(s,a),h.subTo(o,h))}return 0!=n.compareTo(t.ONE)?t.ZERO:h.compareTo(e)>=0?h.subtract(e):h.signum()<0?(h.addTo(e,h),h.signum()<0?h.add(e):h):h},t.prototype.pow=function(t){return this.exp(t,new Ae)},t.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var i=e;e=r,r=i}var n=e.getLowestSetBit(),s=r.getLowestSetBit();if(s<0)return e;for(n<s&&(s=n),s>0&&(e.rShiftTo(s,e),r.rShiftTo(s,r));e.signum()>0;)(n=e.getLowestSetBit())>0&&e.rShiftTo(n,e),(n=r.getLowestSetBit())>0&&r.rShiftTo(n,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return s>0&&r.lShiftTo(s,r),r},t.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=xe[xe.length-1]){for(e=0;e<xe.length;++e)if(r[0]==xe[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<xe.length;){for(var i=xe[e],n=e+1;n<xe.length&&i<Be;)i*=xe[n++];for(i=r.modInt(i);e<n;)if(i%xe[e++]==0)return!1}return r.millerRabin(t)},t.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},t.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},t.prototype.fromString=function(e,r){var i;if(16==r)i=4;else if(8==r)i=3;else if(256==r)i=8;else if(2==r)i=1;else if(32==r)i=5;else{if(4!=r)return void this.fromRadix(e,r);i=2}this.t=0,this.s=0;for(var n=e.length,s=!1,o=0;--n>=0;){var a=8==i?255&+e[n]:He(e,n);a<0?"-"==e.charAt(n)&&(s=!0):(s=!1,0==o?this[this.t++]=a:o+i>this.DB?(this[this.t-1]|=(a&(1<<this.DB-o)-1)<<o,this[this.t++]=a>>this.DB-o):this[this.t-1]|=a<<o,(o+=i)>=this.DB&&(o-=this.DB))}8==i&&0!=(128&+e[0])&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),s&&t.ZERO.subTo(this,this)},t.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},t.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},t.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},t.prototype.lShiftTo=function(t,e){for(var r=t%this.DB,i=this.DB-r,n=(1<<i)-1,s=Math.floor(t/this.DB),o=this.s<<r&this.DM,a=this.t-1;a>=0;--a)e[a+s+1]=this[a]>>i|o,o=(this[a]&n)<<r;for(a=s-1;a>=0;--a)e[a]=0;e[s]=o,e.t=this.t+s+1,e.s=this.s,e.clamp()},t.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var i=t%this.DB,n=this.DB-i,s=(1<<i)-1;e[0]=this[r]>>i;for(var o=r+1;o<this.t;++o)e[o-r-1]|=(this[o]&s)<<n,e[o-r]=this[o]>>i;i>0&&(e[this.t-r-1]|=(this.s&s)<<n),e.t=this.t-r,e.clamp()}},t.prototype.subTo=function(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]-t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i-=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i-=t[r],e[r++]=i&this.DM,i>>=this.DB;i-=t.s}e.s=i<0?-1:0,i<-1?e[r++]=this.DV+i:i>0&&(e[r++]=i),e.t=r,e.clamp()},t.prototype.multiplyTo=function(e,r){var i=this.abs(),n=e.abs(),s=i.t;for(r.t=s+n.t;--s>=0;)r[s]=0;for(s=0;s<n.t;++s)r[s+i.t]=i.am(0,n[s],r,s,0,i.t);r.s=0,r.clamp(),this.s!=e.s&&t.ZERO.subTo(r,r)},t.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var i=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,i,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},t.prototype.divRemTo=function(e,r,i){var n=e.abs();if(!(n.t<=0)){var s=this.abs();if(s.t<n.t)return null!=r&&r.fromInt(0),void(null!=i&&this.copyTo(i));null==i&&(i=Ne());var o=Ne(),a=this.s,h=e.s,c=this.DB-je(n[n.t-1]);c>0?(n.lShiftTo(c,o),s.lShiftTo(c,i)):(n.copyTo(o),s.copyTo(i));var u=o.t,l=o[u-1];if(0!=l){var f=l*(1<<this.F1)+(u>1?o[u-2]>>this.F2:0),p=this.FV/f,d=(1<<this.F1)/f,g=1<<this.F2,v=i.t,y=v-u,m=null==r?Ne():r;for(o.dlShiftTo(y,m),i.compareTo(m)>=0&&(i[i.t++]=1,i.subTo(m,i)),t.ONE.dlShiftTo(u,m),m.subTo(o,o);o.t<u;)o[o.t++]=0;for(;--y>=0;){var b=i[--v]==l?this.DM:Math.floor(i[v]*p+(i[v-1]+g)*d);if((i[v]+=o.am(0,b,i,y,0,u))<b)for(o.dlShiftTo(y,m),i.subTo(m,i);i[v]<--b;)i.subTo(m,i)}null!=r&&(i.drShiftTo(u,r),a!=h&&t.ZERO.subTo(r,r)),i.t=u,i.clamp(),c>0&&i.rShiftTo(c,i),a<0&&t.ZERO.subTo(i,i)}}},t.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},t.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},t.prototype.exp=function(e,r){if(e>4294967295||e<1)return t.ONE;var i=Ne(),n=Ne(),s=r.convert(this),o=je(e)-1;for(s.copyTo(i);--o>=0;)if(r.sqrTo(i,n),(e&1<<o)>0)r.mulTo(n,s,i);else{var a=i;i=n,n=a}return r.revert(i)},t.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},t.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),i=Me(r),n=Ne(),s=Ne(),o="";for(this.divRemTo(i,n,s);n.signum()>0;)o=(r+s.intValue()).toString(t).substr(1)+o,n.divRemTo(i,n,s);return s.intValue().toString(t)+o},t.prototype.fromRadix=function(e,r){this.fromInt(0),null==r&&(r=10);for(var i=this.chunkSize(r),n=Math.pow(r,i),s=!1,o=0,a=0,h=0;h<e.length;++h){var c=He(e,h);c<0?"-"==e.charAt(h)&&0==this.signum()&&(s=!0):(a=r*a+c,++o>=i&&(this.dMultiply(n),this.dAddOffset(a,0),o=0,a=0))}o>0&&(this.dMultiply(Math.pow(r,o)),this.dAddOffset(a,0)),s&&t.ZERO.subTo(this,this)},t.prototype.fromNumber=function(e,r,i){if("number"==typeof r)if(e<2)this.fromInt(1);else for(this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),ie,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(r);)this.dAddOffset(2,0),this.bitLength()>e&&this.subTo(t.ONE.shiftLeft(e-1),this);else{var n=[],s=7&e;n.length=1+(e>>3),r.nextBytes(n),s>0?n[0]&=(1<<s)-1:n[0]=0,this.fromString(n,256)}},t.prototype.bitwiseTo=function(t,e,r){var i,n,s=Math.min(t.t,this.t);for(i=0;i<s;++i)r[i]=e(this[i],t[i]);if(t.t<this.t){for(n=t.s&this.DM,i=s;i<this.t;++i)r[i]=e(this[i],n);r.t=this.t}else{for(n=this.s&this.DM,i=s;i<t.t;++i)r[i]=e(n,t[i]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},t.prototype.changeBit=function(e,r){var i=t.ONE.shiftLeft(e);return this.bitwiseTo(i,r,i),i},t.prototype.addTo=function(t,e){for(var r=0,i=0,n=Math.min(t.t,this.t);r<n;)i+=this[r]+t[r],e[r++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i+=t.s;r<this.t;)i+=this[r],e[r++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;r<t.t;)i+=t[r],e[r++]=i&this.DM,i>>=this.DB;i+=t.s}e.s=i<0?-1:0,i>0?e[r++]=i:i<-1&&(e[r++]=this.DV+i),e.t=r,e.clamp()},t.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},t.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},t.prototype.multiplyLowerTo=function(t,e,r){var i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(var n=r.t-this.t;i<n;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t[i],r,i,0,e-i);r.clamp()},t.prototype.multiplyUpperTo=function(t,e,r){--e;var i=r.t=this.t+t.t-e;for(r.s=0;--i>=0;)r[i]=0;for(i=Math.max(e-this.t,0);i<t.t;++i)r[this.t+i-e]=this.am(e-i,t[i],r,0,0,this.t+i-e);r.clamp(),r.drShiftTo(1,r)},t.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var i=this.t-1;i>=0;--i)r=(e*r+this[i])%t;return r},t.prototype.millerRabin=function(e){var r=this.subtract(t.ONE),i=r.getLowestSetBit();if(i<=0)return!1;var n=r.shiftRight(i);(e=e+1>>1)>xe.length&&(e=xe.length);for(var s=Ne(),o=0;o<e;++o){s.fromInt(xe[Math.floor(Math.random()*xe.length)]);var a=s.modPow(n,this);if(0!=a.compareTo(t.ONE)&&0!=a.compareTo(r)){for(var h=1;h++<i&&0!=a.compareTo(r);)if(0==(a=a.modPowInt(2,this)).compareTo(t.ONE))return!1;if(0!=a.compareTo(r))return!1}}return!0},t.prototype.square=function(){var t=Ne();return this.squareTo(t),t},t.prototype.gcda=function(t,e){var r=this.s<0?this.negate():this.clone(),i=t.s<0?t.negate():t.clone();if(r.compareTo(i)<0){var n=r;r=i,i=n}var s=r.getLowestSetBit(),o=i.getLowestSetBit();if(o<0)e(r);else{s<o&&(o=s),o>0&&(r.rShiftTo(o,r),i.rShiftTo(o,i));var a=function(){(s=r.getLowestSetBit())>0&&r.rShiftTo(s,r),(s=i.getLowestSetBit())>0&&i.rShiftTo(s,i),r.compareTo(i)>=0?(r.subTo(i,r),r.rShiftTo(1,r)):(i.subTo(r,i),i.rShiftTo(1,i)),r.signum()>0?setTimeout(a,0):(o>0&&i.lShiftTo(o,i),setTimeout((function(){e(i)}),0))};setTimeout(a,10)}},t.prototype.fromNumberAsync=function(e,r,i,n){if("number"==typeof r)if(e<2)this.fromInt(1);else{this.fromNumber(e,i),this.testBit(e-1)||this.bitwiseTo(t.ONE.shiftLeft(e-1),ie,this),this.isEven()&&this.dAddOffset(1,0);var s=this,o=function(){s.dAddOffset(2,0),s.bitLength()>e&&s.subTo(t.ONE.shiftLeft(e-1),s),s.isProbablePrime(r)?setTimeout((function(){n()}),0):setTimeout(o,0)};setTimeout(o,0)}else{var a=[],h=7&e;a.length=1+(e>>3),r.nextBytes(a),h>0?a[0]&=(1<<h)-1:a[0]=0,this.fromString(a,256)}},t}(),Ae=function(){function t(){}return t.prototype.convert=function(t){return t},t.prototype.revert=function(t){return t},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},t.prototype.sqrTo=function(t,e){t.squareTo(e)},t}(),De=function(){function t(t){this.m=t}return t.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),Re=function(){function t(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}return t.prototype.convert=function(t){var e=Ne();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(Te.ZERO)>0&&this.m.subTo(e,e),e},t.prototype.revert=function(t){var e=Ne();return t.copyTo(e),this.reduce(e),e},t.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],i=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,i,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}(),ke=function(){function t(t){this.m=t,this.r2=Ne(),this.q3=Ne(),Te.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t)}return t.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=Ne();return t.copyTo(e),this.reduce(e),e},t.prototype.revert=function(t){return t},t.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},t.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},t.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},t}();function Ne(){return new Te(null)}function Ie(t,e){return new Te(t,e)}var Oe="undefined"!=typeof navigator;Oe&&"Microsoft Internet Explorer"==navigator.appName?(Te.prototype.am=function(t,e,r,i,n,s){for(var o=32767&e,a=e>>15;--s>=0;){var h=32767&this[t],c=this[t++]>>15,u=a*h+c*o;n=((h=o*h+((32767&u)<<15)+r[i]+(1073741823&n))>>>30)+(u>>>15)+a*c+(n>>>30),r[i++]=1073741823&h}return n},Se=30):Oe&&"Netscape"!=navigator.appName?(Te.prototype.am=function(t,e,r,i,n,s){for(;--s>=0;){var o=e*this[t++]+r[i]+n;n=Math.floor(o/67108864),r[i++]=67108863&o}return n},Se=26):(Te.prototype.am=function(t,e,r,i,n,s){for(var o=16383&e,a=e>>14;--s>=0;){var h=16383&this[t],c=this[t++]>>14,u=a*h+c*o;n=((h=o*h+((16383&u)<<14)+r[i]+n)>>28)+(u>>14)+a*c,r[i++]=268435455&h}return n},Se=28),Te.prototype.DB=Se,Te.prototype.DM=(1<<Se)-1,Te.prototype.DV=1<<Se;Te.prototype.FV=Math.pow(2,52),Te.prototype.F1=52-Se,Te.prototype.F2=2*Se-52;var Ce,Pe,Ve=[];for(Ce="0".charCodeAt(0),Pe=0;Pe<=9;++Pe)Ve[Ce++]=Pe;for(Ce="a".charCodeAt(0),Pe=10;Pe<36;++Pe)Ve[Ce++]=Pe;for(Ce="A".charCodeAt(0),Pe=10;Pe<36;++Pe)Ve[Ce++]=Pe;function He(t,e){var r=Ve[t.charCodeAt(e)];return null==r?-1:r}function Me(t){var e=Ne();return e.fromInt(t),e}function je(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}Te.ZERO=Me(0),Te.ONE=Me(1);var Le=function(){function t(){this.i=0,this.j=0,this.S=[]}return t.prototype.init=function(t){var e,r,i;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,i=this.S[e],this.S[e]=this.S[r],this.S[r]=i;this.i=0,this.j=0},t.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]},t}();var Ue,Ke,ze=null;if(null==ze){ze=[],Ke=0;var qe=void 0;if("undefined"!=typeof window&&v&&v.getRandomValues){var Fe=new Uint32Array(256);for(v.getRandomValues(Fe),qe=0;qe<Fe.length;++qe)ze[Ke++]=255&Fe[qe]}var We=0,Ge=function(t){if((We=We||0)>=256||Ke>=256)window.removeEventListener?window.removeEventListener("mousemove",Ge,!1):window.detachEvent&&window.detachEvent("onmousemove",Ge);else try{var e=t.x+t.y;ze[Ke++]=255&e,We+=1}catch(r){}};"undefined"!=typeof window&&(window.addEventListener?window.addEventListener("mousemove",Ge,!1):window.attachEvent&&window.attachEvent("onmousemove",Ge))}function Ye(){if(null==Ue){for(Ue=new Le;Ke<256;){var t=Math.floor(65536*Math.random());ze[Ke++]=255&t}for(Ue.init(ze),Ke=0;Ke<ze.length;++Ke)ze[Ke]=0;Ke=0}return Ue.next()}var Ze=function(){function t(){}return t.prototype.nextBytes=function(t){for(var e=0;e<t.length;++e)t[e]=Ye()},t}();var $e=function(){function t(){this.n=null,this.e=0,this.d=null,this.p=null,this.q=null,this.dmp1=null,this.dmq1=null,this.coeff=null}return t.prototype.doPublic=function(t){return t.modPowInt(this.e,this.n)},t.prototype.doPrivate=function(t){if(null==this.p||null==this.q)return t.modPow(this.d,this.n);for(var e=t.mod(this.p).modPow(this.dmp1,this.p),r=t.mod(this.q).modPow(this.dmq1,this.q);e.compareTo(r)<0;)e=e.add(this.p);return e.subtract(r).multiply(this.coeff).mod(this.p).multiply(this.q).add(r)},t.prototype.setPublic=function(t,r){null!=t&&null!=r&&t.length>0&&r.length>0?(this.n=Ie(t,16),this.e=parseInt(r,16)):e("error","at node_modules/jsencrypt/lib/lib/jsbn/rsa.js:114","Invalid RSA public key")},t.prototype.encrypt=function(t){var r=this.n.bitLength()+7>>3,i=function(t,r){if(r<t.length+11)return e("error","at node_modules/jsencrypt/lib/lib/jsbn/rsa.js:37","Message too long for RSA"),null;for(var i=[],n=t.length-1;n>=0&&r>0;){var s=t.charCodeAt(n--);s<128?i[--r]=s:s>127&&s<2048?(i[--r]=63&s|128,i[--r]=s>>6|192):(i[--r]=63&s|128,i[--r]=s>>6&63|128,i[--r]=s>>12|224)}i[--r]=0;for(var o=new Ze,a=[];r>2;){for(a[0]=0;0==a[0];)o.nextBytes(a);i[--r]=a[0]}return i[--r]=2,i[--r]=0,new Te(i)}(t,r);if(null==i)return null;var n=this.doPublic(i);if(null==n)return null;for(var s=n.toString(16),o=s.length,a=0;a<2*r-o;a++)s="0"+s;return s},t.prototype.setPrivate=function(t,r,i){null!=t&&null!=r&&t.length>0&&r.length>0?(this.n=Ie(t,16),this.e=parseInt(r,16),this.d=Ie(i,16)):e("error","at node_modules/jsencrypt/lib/lib/jsbn/rsa.js:146","Invalid RSA private key")},t.prototype.setPrivateEx=function(t,r,i,n,s,o,a,h){null!=t&&null!=r&&t.length>0&&r.length>0?(this.n=Ie(t,16),this.e=parseInt(r,16),this.d=Ie(i,16),this.p=Ie(n,16),this.q=Ie(s,16),this.dmp1=Ie(o,16),this.dmq1=Ie(a,16),this.coeff=Ie(h,16)):e("error","at node_modules/jsencrypt/lib/lib/jsbn/rsa.js:163","Invalid RSA private key")},t.prototype.generate=function(t,e){var r=new Ze,i=t>>1;this.e=parseInt(e,16);for(var n=new Te(e,16);;){for(;this.p=new Te(t-i,1,r),0!=this.p.subtract(Te.ONE).gcd(n).compareTo(Te.ONE)||!this.p.isProbablePrime(10););for(;this.q=new Te(i,1,r),0!=this.q.subtract(Te.ONE).gcd(n).compareTo(Te.ONE)||!this.q.isProbablePrime(10););if(this.p.compareTo(this.q)<=0){var s=this.p;this.p=this.q,this.q=s}var o=this.p.subtract(Te.ONE),a=this.q.subtract(Te.ONE),h=o.multiply(a);if(0==h.gcd(n).compareTo(Te.ONE)){this.n=this.p.multiply(this.q),this.d=n.modInverse(h),this.dmp1=this.d.mod(o),this.dmq1=this.d.mod(a),this.coeff=this.q.modInverse(this.p);break}}},t.prototype.decrypt=function(t){var e=Ie(t,16),r=this.doPrivate(e);return null==r?null:function(t,e){var r=t.toByteArray(),i=0;for(;i<r.length&&0==r[i];)++i;if(r.length-i!=e-1||2!=r[i])return null;++i;for(;0!=r[i];)if(++i>=r.length)return null;var n="";for(;++i<r.length;){var s=255&r[i];s<128?n+=String.fromCharCode(s):s>191&&s<224?(n+=String.fromCharCode((31&s)<<6|63&r[i+1]),++i):(n+=String.fromCharCode((15&s)<<12|(63&r[i+1])<<6|63&r[i+2]),i+=2)}return n}(r,this.n.bitLength()+7>>3)},t.prototype.generateAsync=function(t,e,r){var i=new Ze,n=t>>1;this.e=parseInt(e,16);var s=new Te(e,16),o=this,a=function(){var e=function(){if(o.p.compareTo(o.q)<=0){var t=o.p;o.p=o.q,o.q=t}var e=o.p.subtract(Te.ONE),i=o.q.subtract(Te.ONE),n=e.multiply(i);0==n.gcd(s).compareTo(Te.ONE)?(o.n=o.p.multiply(o.q),o.d=s.modInverse(n),o.dmp1=o.d.mod(e),o.dmq1=o.d.mod(i),o.coeff=o.q.modInverse(o.p),setTimeout((function(){r()}),0)):setTimeout(a,0)},h=function(){o.q=Ne(),o.q.fromNumberAsync(n,1,i,(function(){o.q.subtract(Te.ONE).gcda(s,(function(t){0==t.compareTo(Te.ONE)&&o.q.isProbablePrime(10)?setTimeout(e,0):setTimeout(h,0)}))}))},c=function(){o.p=Ne(),o.p.fromNumberAsync(t-n,1,i,(function(){o.p.subtract(Te.ONE).gcda(s,(function(t){0==t.compareTo(Te.ONE)&&o.p.isProbablePrime(10)?setTimeout(h,0):setTimeout(c,0)}))}))};setTimeout(c,0)};setTimeout(a,0)},t.prototype.sign=function(t,r,i){var n=function(t,r){if(r<t.length+22)return e("error","at node_modules/jsencrypt/lib/lib/jsbn/rsa.js:23","Message too long for RSA"),null;for(var i=r-t.length-6,n="",s=0;s<i;s+=2)n+="ff";return Ie("0001"+n+"00"+t,16)}((Xe[i]||"")+r(t).toString(),this.n.bitLength()/4);if(null==n)return null;var s=this.doPrivate(n);if(null==s)return null;var o=s.toString(16);return 0==(1&o.length)?o:"0"+o},t.prototype.verify=function(t,e,r){var i=Ie(e,16),n=this.doPublic(i);return null==n?null:function(t){for(var e in Xe)if(Xe.hasOwnProperty(e)){var r=Xe[e],i=r.length;if(t.substr(0,i)==r)return t.substr(i)}return t}
/*!
  Copyright (c) 2011, Yahoo! Inc. All rights reserved.
  Code licensed under the BSD License:
  http://developer.yahoo.com/yui/license.html
  version: 2.9.0
  */(n.toString(16).replace(/^1f+00/,""))==r(t).toString()},t}();var Xe={md2:"3020300c06082a864886f70d020205000410",md5:"3020300c06082a864886f70d020505000410",sha1:"3021300906052b0e03021a05000414",sha224:"302d300d06096086480165030402040500041c",sha256:"3031300d060960864801650304020105000420",sha384:"3041300d060960864801650304020205000430",sha512:"3051300d060960864801650304020305000440",ripemd160:"3021300906052b2403020105000414"};var Je={};Je.lang={extend:function(t,e,r){if(!e||!t)throw new Error("YAHOO.lang.extend failed, please check that all dependencies are included.");var i=function(){};if(i.prototype=e.prototype,t.prototype=new i,t.prototype.constructor=t,t.superclass=e.prototype,e.prototype.constructor==Object.prototype.constructor&&(e.prototype.constructor=e),r){var n;for(n in r)t.prototype[n]=r[n];var s=function(){},o=["toString","valueOf"];try{/MSIE/.test(navigator.userAgent)&&(s=function(t,e){for(n=0;n<o.length;n+=1){var r=o[n],i=e[r];"function"==typeof i&&i!=Object.prototype[r]&&(t[r]=i)}})}catch(a){}s(t.prototype,r)}}};
/**
   * @fileOverview
   * @name asn1-1.0.js
   * <AUTHOR>
   * @version asn1 1.0.13 (2017-Jun-02)
   * @since jsrsasign 2.1
   * @license <a href="https://kjur.github.io/jsrsasign/license/">MIT License</a>
   */
var Qe={};void 0!==Qe.asn1&&Qe.asn1||(Qe.asn1={}),Qe.asn1.ASN1Util=new function(){this.integerToByteHex=function(t){var e=t.toString(16);return e.length%2==1&&(e="0"+e),e},this.bigIntToMinTwosComplementsHex=function(t){var e=t.toString(16);if("-"!=e.substr(0,1))e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{var r=e.substr(1).length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);for(var i="",n=0;n<r;n++)i+="f";e=new Te(i,16).xor(t).add(Te.ONE).toString(16).replace(/^-/,"")}return e},this.getPEMStringFromHex=function(t,e){return hextopem(t,e)},this.newObject=function(t){var e=Qe.asn1,r=e.DERBoolean,i=e.DERInteger,n=e.DERBitString,s=e.DEROctetString,o=e.DERNull,a=e.DERObjectIdentifier,h=e.DEREnumerated,c=e.DERUTF8String,u=e.DERNumericString,l=e.DERPrintableString,f=e.DERTeletexString,p=e.DERIA5String,d=e.DERUTCTime,g=e.DERGeneralizedTime,v=e.DERSequence,y=e.DERSet,m=e.DERTaggedObject,b=e.ASN1Util.newObject,S=Object.keys(t);if(1!=S.length)throw"key of param shall be only one.";var w=S[0];if(-1==":bool:int:bitstr:octstr:null:oid:enum:utf8str:numstr:prnstr:telstr:ia5str:utctime:gentime:seq:set:tag:".indexOf(":"+w+":"))throw"undefined key: "+w;if("bool"==w)return new r(t[w]);if("int"==w)return new i(t[w]);if("bitstr"==w)return new n(t[w]);if("octstr"==w)return new s(t[w]);if("null"==w)return new o(t[w]);if("oid"==w)return new a(t[w]);if("enum"==w)return new h(t[w]);if("utf8str"==w)return new c(t[w]);if("numstr"==w)return new u(t[w]);if("prnstr"==w)return new l(t[w]);if("telstr"==w)return new f(t[w]);if("ia5str"==w)return new p(t[w]);if("utctime"==w)return new d(t[w]);if("gentime"==w)return new g(t[w]);if("seq"==w){for(var E=t[w],_=[],x=0;x<E.length;x++){var B=b(E[x]);_.push(B)}return new v({array:_})}if("set"==w){for(E=t[w],_=[],x=0;x<E.length;x++){B=b(E[x]);_.push(B)}return new y({array:_})}if("tag"==w){var T=t[w];if("[object Array]"===Object.prototype.toString.call(T)&&3==T.length){var A=b(T[2]);return new m({tag:T[0],explicit:T[1],obj:A})}var D={};if(void 0!==T.explicit&&(D.explicit=T.explicit),void 0!==T.tag&&(D.tag=T.tag),void 0===T.obj)throw"obj shall be specified for 'tag'.";return D.obj=b(T.obj),new m(D)}},this.jsonToASN1HEX=function(t){return this.newObject(t).getEncodedHex()}},Qe.asn1.ASN1Util.oidHexToInt=function(t){for(var e="",r=parseInt(t.substr(0,2),16),i=(e=Math.floor(r/40)+"."+r%40,""),n=2;n<t.length;n+=2){var s=("00000000"+parseInt(t.substr(n,2),16).toString(2)).slice(-8);if(i+=s.substr(1,7),"0"==s.substr(0,1))e=e+"."+new Te(i,2).toString(10),i=""}return e},Qe.asn1.ASN1Util.oidIntToHex=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=new Te(t,10).toString(2),n=7-i.length%7;7==n&&(n=0);for(var s="",o=0;o<n;o++)s+="0";i=s+i;for(o=0;o<i.length-1;o+=7){var a=i.substr(o,7);o!=i.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",n=t.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=e(s),n.splice(0,2);for(var o=0;o<n.length;o++)i+=r(n[o]);return i},Qe.asn1.ASN1Object=function(){this.getLengthHexFromValue=function(){if(void 0===this.hV||null==this.hV)throw"this.hV is null or undefined.";if(this.hV.length%2==1)throw"value hex must be even length: n="+"".length+",v="+this.hV;var t=this.hV.length/2,e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;var r=e.length/2;if(r>15)throw"ASN.1 length too long to represent by 8x: n = "+t.toString(16);return(128+r).toString(16)+e},this.getEncodedHex=function(){return(null==this.hTLV||this.isModified)&&(this.hV=this.getFreshValueHex(),this.hL=this.getLengthHexFromValue(),this.hTLV=this.hT+this.hL+this.hV,this.isModified=!1),this.hTLV},this.getValueHex=function(){return this.getEncodedHex(),this.hV},this.getFreshValueHex=function(){return""}},Qe.asn1.DERAbstractString=function(t){Qe.asn1.DERAbstractString.superclass.constructor.call(this),this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(this.s)},this.setStringHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?this.setString(t):void 0!==t.str?this.setString(t.str):void 0!==t.hex&&this.setStringHex(t.hex))},Je.lang.extend(Qe.asn1.DERAbstractString,Qe.asn1.ASN1Object),Qe.asn1.DERAbstractTime=function(t){Qe.asn1.DERAbstractTime.superclass.constructor.call(this),this.localDateToUTC=function(t){return utc=t.getTime()+6e4*t.getTimezoneOffset(),new Date(utc)},this.formatDate=function(t,e,r){var i=this.zeroPadding,n=this.localDateToUTC(t),s=String(n.getFullYear());"utc"==e&&(s=s.substr(2,2));var o=s+i(String(n.getMonth()+1),2)+i(String(n.getDate()),2)+i(String(n.getHours()),2)+i(String(n.getMinutes()),2)+i(String(n.getSeconds()),2);if(!0===r){var a=n.getMilliseconds();if(0!=a){var h=i(String(a),3);o=o+"."+(h=h.replace(/[0]+$/,""))}}return o+"Z"},this.zeroPadding=function(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t},this.getString=function(){return this.s},this.setString=function(t){this.hTLV=null,this.isModified=!0,this.s=t,this.hV=stohex(t)},this.setByDateValue=function(t,e,r,i,n,s){var o=new Date(Date.UTC(t,e-1,r,i,n,s,0));this.setByDate(o)},this.getFreshValueHex=function(){return this.hV}},Je.lang.extend(Qe.asn1.DERAbstractTime,Qe.asn1.ASN1Object),Qe.asn1.DERAbstractStructured=function(t){Qe.asn1.DERAbstractString.superclass.constructor.call(this),this.setByASN1ObjectArray=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array=t},this.appendASN1Object=function(t){this.hTLV=null,this.isModified=!0,this.asn1Array.push(t)},this.asn1Array=new Array,void 0!==t&&void 0!==t.array&&(this.asn1Array=t.array)},Je.lang.extend(Qe.asn1.DERAbstractStructured,Qe.asn1.ASN1Object),Qe.asn1.DERBoolean=function(){Qe.asn1.DERBoolean.superclass.constructor.call(this),this.hT="01",this.hTLV="0101ff"},Je.lang.extend(Qe.asn1.DERBoolean,Qe.asn1.ASN1Object),Qe.asn1.DERInteger=function(t){Qe.asn1.DERInteger.superclass.constructor.call(this),this.hT="02",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=Qe.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new Te(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.bigint?this.setByBigInteger(t.bigint):void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},Je.lang.extend(Qe.asn1.DERInteger,Qe.asn1.ASN1Object),Qe.asn1.DERBitString=function(t){if(void 0!==t&&void 0!==t.obj){var e=Qe.asn1.ASN1Util.newObject(t.obj);t.hex="00"+e.getEncodedHex()}Qe.asn1.DERBitString.superclass.constructor.call(this),this.hT="03",this.setHexValueIncludingUnusedBits=function(t){this.hTLV=null,this.isModified=!0,this.hV=t},this.setUnusedBitsAndHexValue=function(t,e){if(t<0||7<t)throw"unused bits shall be from 0 to 7: u = "+t;var r="0"+t;this.hTLV=null,this.isModified=!0,this.hV=r+e},this.setByBinaryString=function(t){var e=8-(t=t.replace(/0+$/,"")).length%8;8==e&&(e=0);for(var r=0;r<=e;r++)t+="0";var i="";for(r=0;r<t.length-1;r+=8){var n=t.substr(r,8),s=parseInt(n,2).toString(16);1==s.length&&(s="0"+s),i+=s}this.hTLV=null,this.isModified=!0,this.hV="0"+e+i},this.setByBooleanArray=function(t){for(var e="",r=0;r<t.length;r++)1==t[r]?e+="1":e+="0";this.setByBinaryString(e)},this.newFalseArray=function(t){for(var e=new Array(t),r=0;r<t;r++)e[r]=!1;return e},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t&&t.toLowerCase().match(/^[0-9a-f]+$/)?this.setHexValueIncludingUnusedBits(t):void 0!==t.hex?this.setHexValueIncludingUnusedBits(t.hex):void 0!==t.bin?this.setByBinaryString(t.bin):void 0!==t.array&&this.setByBooleanArray(t.array))},Je.lang.extend(Qe.asn1.DERBitString,Qe.asn1.ASN1Object),Qe.asn1.DEROctetString=function(t){if(void 0!==t&&void 0!==t.obj){var e=Qe.asn1.ASN1Util.newObject(t.obj);t.hex=e.getEncodedHex()}Qe.asn1.DEROctetString.superclass.constructor.call(this,t),this.hT="04"},Je.lang.extend(Qe.asn1.DEROctetString,Qe.asn1.DERAbstractString),Qe.asn1.DERNull=function(){Qe.asn1.DERNull.superclass.constructor.call(this),this.hT="05",this.hTLV="0500"},Je.lang.extend(Qe.asn1.DERNull,Qe.asn1.ASN1Object),Qe.asn1.DERObjectIdentifier=function(t){var e=function(t){var e=t.toString(16);return 1==e.length&&(e="0"+e),e},r=function(t){var r="",i=new Te(t,10).toString(2),n=7-i.length%7;7==n&&(n=0);for(var s="",o=0;o<n;o++)s+="0";i=s+i;for(o=0;o<i.length-1;o+=7){var a=i.substr(o,7);o!=i.length-7&&(a="1"+a),r+=e(parseInt(a,2))}return r};Qe.asn1.DERObjectIdentifier.superclass.constructor.call(this),this.hT="06",this.setValueHex=function(t){this.hTLV=null,this.isModified=!0,this.s=null,this.hV=t},this.setValueOidString=function(t){if(!t.match(/^[0-9.]+$/))throw"malformed oid string: "+t;var i="",n=t.split("."),s=40*parseInt(n[0])+parseInt(n[1]);i+=e(s),n.splice(0,2);for(var o=0;o<n.length;o++)i+=r(n[o]);this.hTLV=null,this.isModified=!0,this.s=null,this.hV=i},this.setValueName=function(t){var e=Qe.asn1.x509.OID.name2oid(t);if(""===e)throw"DERObjectIdentifier oidName undefined: "+t;this.setValueOidString(e)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&("string"==typeof t?t.match(/^[0-2].[0-9.]+$/)?this.setValueOidString(t):this.setValueName(t):void 0!==t.oid?this.setValueOidString(t.oid):void 0!==t.hex?this.setValueHex(t.hex):void 0!==t.name&&this.setValueName(t.name))},Je.lang.extend(Qe.asn1.DERObjectIdentifier,Qe.asn1.ASN1Object),Qe.asn1.DEREnumerated=function(t){Qe.asn1.DEREnumerated.superclass.constructor.call(this),this.hT="0a",this.setByBigInteger=function(t){this.hTLV=null,this.isModified=!0,this.hV=Qe.asn1.ASN1Util.bigIntToMinTwosComplementsHex(t)},this.setByInteger=function(t){var e=new Te(String(t),10);this.setByBigInteger(e)},this.setValueHex=function(t){this.hV=t},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.int?this.setByInteger(t.int):"number"==typeof t?this.setByInteger(t):void 0!==t.hex&&this.setValueHex(t.hex))},Je.lang.extend(Qe.asn1.DEREnumerated,Qe.asn1.ASN1Object),Qe.asn1.DERUTF8String=function(t){Qe.asn1.DERUTF8String.superclass.constructor.call(this,t),this.hT="0c"},Je.lang.extend(Qe.asn1.DERUTF8String,Qe.asn1.DERAbstractString),Qe.asn1.DERNumericString=function(t){Qe.asn1.DERNumericString.superclass.constructor.call(this,t),this.hT="12"},Je.lang.extend(Qe.asn1.DERNumericString,Qe.asn1.DERAbstractString),Qe.asn1.DERPrintableString=function(t){Qe.asn1.DERPrintableString.superclass.constructor.call(this,t),this.hT="13"},Je.lang.extend(Qe.asn1.DERPrintableString,Qe.asn1.DERAbstractString),Qe.asn1.DERTeletexString=function(t){Qe.asn1.DERTeletexString.superclass.constructor.call(this,t),this.hT="14"},Je.lang.extend(Qe.asn1.DERTeletexString,Qe.asn1.DERAbstractString),Qe.asn1.DERIA5String=function(t){Qe.asn1.DERIA5String.superclass.constructor.call(this,t),this.hT="16"},Je.lang.extend(Qe.asn1.DERIA5String,Qe.asn1.DERAbstractString),Qe.asn1.DERUTCTime=function(t){Qe.asn1.DERUTCTime.superclass.constructor.call(this,t),this.hT="17",this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"utc"),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{12}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date))},Je.lang.extend(Qe.asn1.DERUTCTime,Qe.asn1.DERAbstractTime),Qe.asn1.DERGeneralizedTime=function(t){Qe.asn1.DERGeneralizedTime.superclass.constructor.call(this,t),this.hT="18",this.withMillis=!1,this.setByDate=function(t){this.hTLV=null,this.isModified=!0,this.date=t,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)},this.getFreshValueHex=function(){return void 0===this.date&&void 0===this.s&&(this.date=new Date,this.s=this.formatDate(this.date,"gen",this.withMillis),this.hV=stohex(this.s)),this.hV},void 0!==t&&(void 0!==t.str?this.setString(t.str):"string"==typeof t&&t.match(/^[0-9]{14}Z$/)?this.setString(t):void 0!==t.hex?this.setStringHex(t.hex):void 0!==t.date&&this.setByDate(t.date),!0===t.millis&&(this.withMillis=!0))},Je.lang.extend(Qe.asn1.DERGeneralizedTime,Qe.asn1.DERAbstractTime),Qe.asn1.DERSequence=function(t){Qe.asn1.DERSequence.superclass.constructor.call(this,t),this.hT="30",this.getFreshValueHex=function(){for(var t="",e=0;e<this.asn1Array.length;e++){t+=this.asn1Array[e].getEncodedHex()}return this.hV=t,this.hV}},Je.lang.extend(Qe.asn1.DERSequence,Qe.asn1.DERAbstractStructured),Qe.asn1.DERSet=function(t){Qe.asn1.DERSet.superclass.constructor.call(this,t),this.hT="31",this.sortFlag=!0,this.getFreshValueHex=function(){for(var t=new Array,e=0;e<this.asn1Array.length;e++){var r=this.asn1Array[e];t.push(r.getEncodedHex())}return 1==this.sortFlag&&t.sort(),this.hV=t.join(""),this.hV},void 0!==t&&void 0!==t.sortflag&&0==t.sortflag&&(this.sortFlag=!1)},Je.lang.extend(Qe.asn1.DERSet,Qe.asn1.DERAbstractStructured),Qe.asn1.DERTaggedObject=function(t){Qe.asn1.DERTaggedObject.superclass.constructor.call(this),this.hT="a0",this.hV="",this.isExplicit=!0,this.asn1Object=null,this.setASN1Object=function(t,e,r){this.hT=e,this.isExplicit=t,this.asn1Object=r,this.isExplicit?(this.hV=this.asn1Object.getEncodedHex(),this.hTLV=null,this.isModified=!0):(this.hV=null,this.hTLV=r.getEncodedHex(),this.hTLV=this.hTLV.replace(/^../,e),this.isModified=!1)},this.getFreshValueHex=function(){return this.hV},void 0!==t&&(void 0!==t.tag&&(this.hT=t.tag),void 0!==t.explicit&&(this.isExplicit=t.explicit),void 0!==t.obj&&(this.asn1Object=t.obj,this.setASN1Object(this.isExplicit,this.hT,this.asn1Object)))},Je.lang.extend(Qe.asn1.DERTaggedObject,Qe.asn1.ASN1Object);var tr,er=function(){var t=function(e,r){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function i(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(i.prototype=r.prototype,new i)}}(),rr=function(t){function e(r){var i=t.call(this)||this;return r&&("string"==typeof r?i.parseKey(r):(e.hasPrivateKeyProperty(r)||e.hasPublicKeyProperty(r))&&i.parsePropertiesFrom(r)),i}return er(e,t),e.prototype.parseKey=function(t){try{var e=0,r=0,i=/^\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\s*)+$/.test(t)?pe(t):de.unarmor(t),n=Ee.decode(i);if(3===n.sub.length&&(n=n.sub[2].sub[0]),9===n.sub.length){e=n.sub[1].getHexStringValue(),this.n=Ie(e,16),r=n.sub[2].getHexStringValue(),this.e=parseInt(r,16);var s=n.sub[3].getHexStringValue();this.d=Ie(s,16);var o=n.sub[4].getHexStringValue();this.p=Ie(o,16);var a=n.sub[5].getHexStringValue();this.q=Ie(a,16);var h=n.sub[6].getHexStringValue();this.dmp1=Ie(h,16);var c=n.sub[7].getHexStringValue();this.dmq1=Ie(c,16);var u=n.sub[8].getHexStringValue();this.coeff=Ie(u,16)}else{if(2!==n.sub.length)return!1;if(n.sub[0].sub){var l=n.sub[1].sub[0];e=l.sub[0].getHexStringValue(),this.n=Ie(e,16),r=l.sub[1].getHexStringValue(),this.e=parseInt(r,16)}else e=n.sub[0].getHexStringValue(),this.n=Ie(e,16),r=n.sub[1].getHexStringValue(),this.e=parseInt(r,16)}return!0}catch(f){return!1}},e.prototype.getPrivateBaseKey=function(){var t={array:[new Qe.asn1.DERInteger({int:0}),new Qe.asn1.DERInteger({bigint:this.n}),new Qe.asn1.DERInteger({int:this.e}),new Qe.asn1.DERInteger({bigint:this.d}),new Qe.asn1.DERInteger({bigint:this.p}),new Qe.asn1.DERInteger({bigint:this.q}),new Qe.asn1.DERInteger({bigint:this.dmp1}),new Qe.asn1.DERInteger({bigint:this.dmq1}),new Qe.asn1.DERInteger({bigint:this.coeff})]};return new Qe.asn1.DERSequence(t).getEncodedHex()},e.prototype.getPrivateBaseKeyB64=function(){return ue(this.getPrivateBaseKey())},e.prototype.getPublicBaseKey=function(){var t=new Qe.asn1.DERSequence({array:[new Qe.asn1.DERObjectIdentifier({oid:"1.2.840.113549.1.1.1"}),new Qe.asn1.DERNull]}),e=new Qe.asn1.DERSequence({array:[new Qe.asn1.DERInteger({bigint:this.n}),new Qe.asn1.DERInteger({int:this.e})]}),r=new Qe.asn1.DERBitString({hex:"00"+e.getEncodedHex()});return new Qe.asn1.DERSequence({array:[t,r]}).getEncodedHex()},e.prototype.getPublicBaseKeyB64=function(){return ue(this.getPublicBaseKey())},e.wordwrap=function(t,e){if(!t)return t;var r="(.{1,"+(e=e||64)+"})( +|$\n?)|(.{1,"+e+"})";return t.match(RegExp(r,"g")).join("\n")},e.prototype.getPrivateKey=function(){var t="-----BEGIN RSA PRIVATE KEY-----\n";return t+=e.wordwrap(this.getPrivateBaseKeyB64())+"\n",t+="-----END RSA PRIVATE KEY-----"},e.prototype.getPublicKey=function(){var t="-----BEGIN PUBLIC KEY-----\n";return t+=e.wordwrap(this.getPublicBaseKeyB64())+"\n",t+="-----END PUBLIC KEY-----"},e.hasPublicKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")},e.hasPrivateKeyProperty=function(t){return(t=t||{}).hasOwnProperty("n")&&t.hasOwnProperty("e")&&t.hasOwnProperty("d")&&t.hasOwnProperty("p")&&t.hasOwnProperty("q")&&t.hasOwnProperty("dmp1")&&t.hasOwnProperty("dmq1")&&t.hasOwnProperty("coeff")},e.prototype.parsePropertiesFrom=function(t){this.n=t.n,this.e=t.e,t.hasOwnProperty("d")&&(this.d=t.d,this.p=t.p,this.q=t.q,this.dmp1=t.dmp1,this.dmq1=t.dmq1,this.coeff=t.coeff)},e}($e),ir="undefined"!=typeof process?null===(tr={})||void 0===tr?void 0:tr.npm_package_version:void 0,nr=function(){function t(t){void 0===t&&(t={}),t=t||{},this.default_key_size=t.default_key_size?parseInt(t.default_key_size,10):1024,this.default_public_exponent=t.default_public_exponent||"010001",this.log=t.log||!1,this.key=null}return t.prototype.setKey=function(t){this.log&&this.key&&e("warn","at node_modules/jsencrypt/lib/JSEncrypt.js:37","A key was already set, overriding existing."),this.key=new rr(t)},t.prototype.setPrivateKey=function(t){this.setKey(t)},t.prototype.setPublicKey=function(t){this.setKey(t)},t.prototype.decrypt=function(t){try{return this.getKey().decrypt(le(t))}catch(e){return!1}},t.prototype.encrypt=function(t){try{return ue(this.getKey().encrypt(t))}catch(e){return!1}},t.prototype.sign=function(t,e,r){try{return ue(this.getKey().sign(t,e,r))}catch(i){return!1}},t.prototype.verify=function(t,e,r){try{return this.getKey().verify(t,le(e),r)}catch(i){return!1}},t.prototype.getKey=function(t){if(!this.key){if(this.key=new rr,t&&"[object Function]"==={}.toString.call(t))return void this.key.generateAsync(this.default_key_size,this.default_public_exponent,t);this.key.generate(this.default_key_size,this.default_public_exponent)}return this.key},t.prototype.getPrivateKey=function(){return this.getKey().getPrivateKey()},t.prototype.getPrivateKeyB64=function(){return this.getKey().getPrivateBaseKeyB64()},t.prototype.getPublicKey=function(){return this.getKey().getPublicKey()},t.prototype.getPublicKeyB64=function(){return this.getKey().getPublicBaseKeyB64()},t.version=ir,t}();const sr={RSA:{KEY_SIZE:2048,PUBLIC_KEY:"-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmWPDFsI9N+4Hsmo2\nkwtPENGaJ+9Kiq4Nqscdac0LHfHLTEhPuPnnfYZ+g050Ag4IoS+C6U3MQM1P\ngi4IuhL4Ef2awrMJxf4HwuVxtIZy+oiT8SRQU84/jgcpi9Omoi2wKcRc5Bpr\nfp4wI2nzb9bS397KsPA0b/HSix+xlCkBCqTyL3HjFOBNSxsU3o0F0DyesomK\nG5B6J9Csj8ALWLs/JUsZNfO1tMyHN2invduNPwvn7dETAGCeQ1t6Of986twv\nEE8jDxEPQ35mZVF2cDkcvSSb8aJLzlyZS6zzmYF1UsfT+Z5j3lYUIIbVfacR\n3V/FYL4qZx+0eYY2VrgWhoEZqQIDAQAB\n-----END PUBLIC KEY-----",ALGORITHM:"RSA-OAEP",HASH:"SHA-256"},AES:{KEY_LENGTH:32,MODE:"CBC",PADDING:"Pkcs7",CHARSET:"UTF-8",IV_LENGTH:16},REQUEST:{ENABLE_ENCRYPTION:!0,ENCRYPTED_METHODS:["POST","PUT","PATCH"],ENCRYPTED_PATHS:["/api/auth/login","/api/bank/search","/api/user/profile"],TIMEOUT:1e4},DEVELOPMENT:{ENABLE_DEBUG_LOG:!1,ENABLE_CRYPTO_TEST:!1,USE_MOCK_DATA:!1,TEST_AES_KEY:"test-aes-key-32-characters-long",SKIP_RSA_VALIDATION:!1}};function or(){return sr.AES}function ar(){return sr.DEVELOPMENT.ENABLE_DEBUG_LOG}class hr{static getRSAPublicKey(){return sr.RSA.PUBLIC_KEY}static setRSAPublicKey(t){if(!this.validateRSAPublicKey(t))throw new Error("无效的RSA公钥格式");e("log","at utils/crypto.js:27","RSA公钥已更新")}static generateAESKey(){const t=or(),e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0********9";let r="";for(let i=0;i<t.KEY_LENGTH;i++)r+=e.charAt(Math.floor(Math.random()*e.length));return r}static aesEncrypt(t,r){try{const e=or();ar();const i=te.lib.WordArray.random(16);ar();const n=te.AES.encrypt(t,te.enc.Utf8.parse(r),{iv:i,mode:te.mode.CBC,padding:te.pad[e.PADDING]}),s=i.concat(n.ciphertext).toString(te.enc.Base64);return ar(),s}catch(i){throw e("error","at utils/crypto.js:97","AES加密失败:",i),new Error("数据加密失败: "+i.message)}}static aesDecrypt(t,r){try{const e=or();ar();let i=t;const n=te.enc.Base64.parse(i);if(ar(),n.sigBytes<16)throw new Error("加密数据长度不足，无法提取IV");const s=te.lib.WordArray.create(n.words.slice(0,4));s.sigBytes=16;const o=te.lib.WordArray.create(n.words.slice(4),n.sigBytes-16);ar();const a=te.AES.decrypt({ciphertext:o},te.enc.Utf8.parse(r),{iv:s,mode:te.mode.CBC,padding:te.pad[e.PADDING]}).toString(te.enc.Utf8);if(!a)throw new Error("解密结果为空，可能是密钥错误、IV错误或数据损坏");return ar(),a}catch(i){throw e("error","at utils/crypto.js:175","AES解密失败:",i),new Error("数据解密失败: "+i.message)}}static isBase64(t){try{return!!/^[A-Za-z0-9+/]*={0,2}$/.test(t)&&(t.length%4==0&&(atob(t),!0))}catch(e){return!1}}static rsaEncrypt(t){try{const r=this.getRSAPublicKey();ar(),t.length>200&&e("warn","at utils/crypto.js:224","RSA加密数据较长，可能失败。建议数据长度小于200字符");const i=new nr;i.setPublicKey(r);const n=i.encrypt(t);if(!n)throw new Error("RSA加密失败，可能是公钥格式错误或数据过长");return ar(),n}catch(r){throw e("error","at utils/crypto.js:242","RSA加密失败:",r),new Error("密钥加密失败: "+r.message)}}static rsaDecrypt(t,r){try{const e=new nr;e.setPrivateKey(r);const i=e.decrypt(t);if(!i)throw new Error("RSA解密失败，可能是私钥错误或数据损坏");return i}catch(i){throw e("error","at utils/crypto.js:266","RSA解密失败:",i),new Error("密钥解密失败")}}static encryptRequest(t){try{ar();const e=this.generateAESKey();t.aesKey=e,ar();const r=JSON.stringify(t),i=this.rsaEncrypt(r);return ar(),{encryptedData:i,aesKey:e}}catch(r){throw e("error","at utils/crypto.js:317","请求加密失败:",r),new Error("请求加密失败: "+r.message)}}static decryptResponse(t,r){try{ar();const e=this.aesDecrypt(t,r),i=JSON.parse(e);return ar(),i}catch(i){throw e("error","at utils/crypto.js:347","响应解密失败:",i),new Error("响应解密失败: "+i.message)}}static validateRSAPublicKey(t){try{return/^-----BEGIN PUBLIC KEY-----[\s\S]*-----END PUBLIC KEY-----$/.test(t.trim())}catch(e){return!1}}static testEncryption(){try{e("log","at utils/crypto.js:372","开始加密解密测试...");const t="Hello, World! 测试数据 123",r=this.generateAESKey(),i=this.aesEncrypt(t,r);if(this.aesDecrypt(i,r)!==t)throw new Error("AES加密解密测试失败");e("log","at utils/crypto.js:385","AES加密解密测试通过");const n={aesKey:r},s=this.rsaEncrypt(JSON.stringify(n));if(!s||0===s.length)throw new Error("RSA加密测试失败");e("log","at utils/crypto.js:395","RSA加密测试通过");const o={message:"test",timestamp:Date.now()},a=this.encryptRequest(o);if(!a.encryptedData||!a.encryptedKey)throw new Error("混合加密测试失败");return e("log","at utils/crypto.js:405","混合加密测试通过"),e("log","at utils/crypto.js:406","所有加密测试通过！"),!0}catch(t){return e("error","at utils/crypto.js:410","加密测试失败:",t),!1}}}const cr=t=>hr.encryptRequest(t),ur=(t,e)=>hr.decryptResponse(t,e),lr={BASE_URL:"https://hd.peixue100.cn/ylhapp",ENDPOINTS:{LOGIN:"/bankNum/jscode2session",BANK_SEARCH:"/bankNum/queryBankInfo",BANK_DETAIL:"/bankNum/detail",BANK_LIST:"/bankNum/list",USER_PROFILE:"/bankNum/user/profile",FEEDBACK:"/bankNum/feedback"},REQUEST:{TIMEOUT:1e4,RETRY_COUNT:3,RETRY_DELAY:1e3},STATUS_CODES:{SUCCESS:200,UNAUTHORIZED:401,FORBIDDEN:403,NOT_FOUND:404,SERVER_ERROR:500},BUSINESS_CODES:{SUCCESS:200,PARAM_ERROR:400,AUTH_FAILED:401,NO_DATA:404,SERVER_ERROR:500}};function fr(t){return`${lr.BASE_URL}${lr.ENDPOINTS[t]||t}`}const pr={DEFAULT_LIMIT:20,MAX_LIMIT:100,SEARCH_TYPES:{KEYWORD:"keyword",BANK_CODE:"bank_code",BANK_NAME:"bank_name",REGION:"region"}},dr={RETRY_CONDITIONS:["request:fail","timeout","network error","connection refused"],getRetryDelay:t=>Math.min(1e3*Math.pow(2,t),1e4),shouldRetry:(t,e)=>!(e>=lr.REQUEST.RETRY_COUNT)&&(!!t.errMsg&&dr.RETRY_CONDITIONS.some((e=>t.errMsg.includes(e))))},gr={handleStandardResponse(t){if(t.statusCode!==lr.STATUS_CODES.SUCCESS)throw new Error(`HTTP错误: ${t.statusCode}`);return{success:!0,data:t.data.data,message:t.data.message,timestamp:t.data.timestamp||Date.now()}},handleBankSearchResponse(t){const e=this.handleStandardResponse(t);if(!e.data)return[];return(Array.isArray(e.data)?e.data:[e.data]).filter((t=>t&&t.code))},handleErrorResponse(t){let e="未知错误",r="UNKNOWN_ERROR";return t.errMsg?t.errMsg.includes("request:fail")?(e="网络请求失败",r="NETWORK_ERROR"):t.errMsg.includes("timeout")&&(e="请求超时",r="TIMEOUT_ERROR"):t.message&&(e=t.message,r="BUSINESS_ERROR"),{success:!1,error:e,code:r,timestamp:Date.now()}}},vr={buildSearchParams:t=>({keyword:t.keyword||"",type:t.type||pr.SEARCH_TYPES.KEYWORD,limit:Math.min(t.limit||pr.DEFAULT_LIMIT,pr.MAX_LIMIT),timestamp:Date.now()}),validateSearchParams:t=>!(!t.keyword||"string"!=typeof t.keyword)&&0!==t.keyword.trim().length,formatBankInfo:t=>({code:t.bankNum||"",bankName:t.bankDetail||t.bankDetail||"",branchName:t.bankDetail||t.bankDetail||"",address:t.bankDetail||"",phone:t.phone||"",distance:t.matching||""})};function yr(){const r=t.ref(""),i=t.ref([]),n=t.ref(!1),s=t.ref(null),l=t.computed((()=>i.value.length>0)),f=t.computed((()=>!n.value&&!l.value)),p=t.computed((()=>n.value||""===r.value.trim())),d=async()=>{if(r.value.trim()){n.value=!0,s.value=null,i.value=[];try{e("log","at composables/useBankSearch.js:38","开始银行查询，关键字:",r.value);const t=await class{static async searchBankCode(t){if(!vr.validateSearchParams({keyword:t}))throw new Error("搜索关键字不能为空");const r=function(t){return t?/^\d{12}$/.test(t)?h:Object.keys(o).some((e=>t.includes(e)))?c:["北京","上海","广州","深圳","杭州","南京","武汉","成都","重庆","西安"].some((e=>t.includes(e)))?u:a:a}(t);e("log","at utils/api.js:24",`搜索类型: ${r}, 关键字: ${t}`);const i={openId:uni.getStorageSync("user_openid"),query:t};return await this.requestWithFallback(i,t,r)}static async requestWithFallback(t,r,i){let n=null;for(let o=0;o<3;o++)try{e("log","at utils/api.js:50",`银行查询尝试 ${o+1}/${dr.getRetryDelay.length}`);const r=cr(t);e("log","at utils/api.js:54","银行查询请求已加密");const i=await uni.request({url:fr("BANK_SEARCH"),method:"POST",data:{parameter:r.encryptedData},header:{"Content-Type":"application/json"},timeout:1e4});e("log","at utils/api.js:69","银行查询响应:",i);const n=gr.handleBankSearchResponse(i);if(200==i.statusCode){if(200==i.data.code){const t=ur(i.data.data,r.aesKey);e("log","at utils/api.js:78","银行查询解密成功:",t);const n=Array.isArray(t.bankInfoList)?t.bankInfoList.map((t=>vr.formatBankInfo(t))):[vr.formatBankInfo(t.bankInfoList)];return e("log","at utils/api.js:84","银行查询结果已格式化:",n),n.filter((t=>t.code))}return uni.showToast({title:i.data.msg,icon:"none",duration:2e3}),[]}return n}catch(s){if(n=s,e("error","at utils/api.js:101",`银行查询尝试 ${o+1} 失败:`,s),!dr.shouldRetry(s,o))break;if(o<2){const t=dr.getRetryDelay(o);e("log","at utils/api.js:111",`等待 ${t}ms 后重试...`),await new Promise((e=>setTimeout(e,t)))}}return e("log","at utils/api.js:118","API请求失败，使用模拟数据:",null==n?void 0:n.message),this.getMockData(r,i)}static getMockData(t,e="keyword"){const r=t.toLowerCase();return[{code:"************",bankName:"中国工商银行股份有限公司北京国家文化与金融合作示范区支行",branchName:"营业部：中国工商银行股份有限公司北京王府井支行",distance:"100",address:"北京市东城区王府井大街",phone:"010-********"},{code:"************",bankName:"中国工商银行股份有限公司北京分行",branchName:"营业部：中国工商银行股份有限公司北京分行营业部",distance:"200",address:"北京市西城区复兴门内大街",phone:"010-********"},{code:"************",bankName:"中国农业银行股份有限公司北京分行",branchName:"营业部：中国农业银行股份有限公司北京分行营业部",distance:"150",address:"北京市东城区建国门内大街",phone:"010-********"},{code:"************",bankName:"中国银行股份有限公司北京分行",branchName:"营业部：中国银行股份有限公司北京分行营业部",distance:"180",address:"北京市西城区复兴门外大街",phone:"010-********"},{code:"************",bankName:"中国建设银行股份有限公司北京分行",branchName:"营业部：中国建设银行股份有限公司北京分行营业部",distance:"120",address:"北京市西城区金融大街",phone:"010-********"}].filter((i=>e===h?i.code===t||i.code.includes(t):e===c?i.bankName.includes(t)||i.branchName.includes(t)||r.includes("工行")&&i.bankName.includes("工商银行")||r.includes("农行")&&i.bankName.includes("农业银行")||r.includes("中行")&&i.bankName.includes("中国银行")||r.includes("建行")&&i.bankName.includes("建设银行"):e===u?i.address&&i.address.includes(t):i.bankName.includes(t)||i.branchName.includes(t)||i.code.includes(t)||i.address&&i.address.includes(t)||r.includes("工行")&&i.bankName.includes("工商银行")||r.includes("农行")&&i.bankName.includes("农业银行")||r.includes("中行")&&i.bankName.includes("中国银行")||r.includes("建行")&&i.bankName.includes("建设银行")||r.includes("北京")&&i.address&&i.address.includes("北京")))}static async getBankDetail(t){try{if(!t||"string"!=typeof t||12!==t.length)throw new Error("联行号格式错误");const r={bankCode:t,timestamp:Date.now()},i=cr(r);e("log","at utils/api.js:229","银行详情查询请求已加密");const n=await uni.request({url:fr("BANK_DETAIL"),method:"POST",data:{parameter:i.encryptedData},header:{"Content-Type":"application/json"},timeout:1e4});if(e("log","at utils/api.js:244","银行详情查询响应:",n),200===n.statusCode&&200==n.data.code){const t=ur(n.data.data,i.aesKey);return e("log","at utils/api.js:250","银行详情解密成功:",t),vr.formatBankInfo(t)}e("log","at utils/api.js:257","API请求失败，使用模拟数据");const s=this.getMockData(t,h);return s.length>0?s[0]:null}catch(s){if(e("error","at utils/api.js:262","获取银行详情失败:",s),s.errMsg&&s.errMsg.includes("request:fail")){e("log","at utils/api.js:266","网络请求失败，使用模拟数据");const r=this.getMockData(t,h);return r.length>0?r[0]:null}throw new Error("获取详情失败: "+s.message)}}}.searchBankCode(r.value);e("log","at composables/useBankSearch.js:41","银行查询结果:",t),i.value=t||[],t&&0===t.length||t&&t.length}catch(t){s.value=t.message||"查询失败",e("error","at composables/useBankSearch.js:59","银行查询失败:",t);let r="查询失败，请重试";t.message&&t.message.includes("网络")?r="网络连接失败，请检查网络":t.message&&t.message.includes("解密")&&(r="数据解密失败，请重试"),uni.showToast({title:r,icon:"none",duration:2e3})}finally{n.value=!1}}else uni.showToast({title:"请输入搜索关键字",icon:"none"})};return{searchKeyword:r,searchResults:i,isLoading:n,error:s,hasResults:l,showNoResult:f,isSearchDisabled:p,executeSearch:d,pasteAndSearch:async()=>{try{const t=await uni.getClipboardData();t.data&&t.data.trim()?(r.value=t.data.trim(),await d()):uni.showToast({title:"剪贴板为空",icon:"none"})}catch(t){e("error","at composables/useBankSearch.js:95","粘贴失败:",t),uni.showToast({title:"粘贴失败",icon:"none"})}},clearSearch:()=>{r.value="",i.value=[],s.value=null},selectResult:t=>{t.code&&uni.setClipboardData({data:t.code,success:()=>{uni.showToast({title:"联行号已复制",icon:"success"})},fail:()=>{uni.showToast({title:"复制失败",icon:"none"})}})},refreshSearch:()=>{r.value.trim()&&d()}}}__definePage("pages/index/index",{__name:"index",setup(r){const{isLoggedIn:i,userInfo:n,openid:s,isLogging:o,needLogin:a,checkLoginStatus:h,wxLogin:c,ensureLogin:u}=function(){const r=t.ref(!1),i=t.ref(null),n=t.ref(""),s=t.ref(!1),o=t.ref(null),a=t.computed((()=>r.value&&n.value)),h=t.computed((()=>!a.value)),c=()=>{try{const t=uni.getStorageSync("user_openid"),s=uni.getStorageSync("user_info");return t?(n.value=t,i.value=JSON.parse(s),r.value=!0,e("log","at composables/useAuth.js:33","用户已登录:",n.value),!0):(e("log","at composables/useAuth.js:37","用户未登录"),!1)}catch(t){return e("error","at composables/useAuth.js:40","检查登录状态失败:",t),!1}},u=async()=>{if(!s.value){s.value=!0,o.value=null;try{e("log","at composables/useAuth.js:55","开始微信登录...");const t=await new Promise(((t,e)=>{uni.login({provider:"weixin",success:t,fail:e})}));if(!t.code)throw new Error("获取登录凭证失败");e("log","at composables/useAuth.js:70","获取到登录code:",t.code);const o=await l(t.code);if(o.openId)return n.value=o.openId,i.value=o.userInfo||{},r.value=!0,uni.setStorageSync("user_openid",n.value),uni.setStorageSync("user_info",JSON.stringify(i.value)),e("log","at composables/useAuth.js:85","登录成功:",n.value),!0;throw new Error(o.message||"登录失败")}catch(t){return e("error","at composables/useAuth.js:98","登录失败:",t),o.value=t.message||"登录失败",!1}finally{s.value=!1}}},l=async t=>{try{const r=cr({weChatCode:t});e("log","at composables/useAuth.js:127","发送登录请求到服务器...");const i=await uni.request({url:"https://hd.peixue100.cn/ylhapp/bankNum/jscode2session",method:"POST",data:{parameter:r.encryptedData},header:{"Content-Type":"application/json"}});if(200===i.statusCode&&200==i.data.code)return ur(i.data.data,r.aesKey);throw new Error("服务器响应异常")}catch(r){if(e("error","at composables/useAuth.js:151","获取openid失败:",r),r.errMsg&&r.errMsg.includes("request:fail"))return e("log","at composables/useAuth.js:155","网络请求失败，使用模拟数据"),{success:!0,openid:`mock_openid_${Date.now()}`,userInfo:{nickname:"测试用户",avatar:""},message:"登录成功（模拟）"};throw r}};return{isLoggedIn:r,userInfo:i,openid:n,isLogging:s,loginError:o,isAuthenticated:a,needLogin:h,checkLoginStatus:c,wxLogin:u,logout:()=>{try{r.value=!1,i.value=null,n.value="",o.value=null,uni.removeStorageSync("user_openid"),uni.removeStorageSync("user_info"),e("log","at composables/useAuth.js:186","用户已退出登录"),uni.showToast({title:"已退出登录",icon:"success"})}catch(t){e("error","at composables/useAuth.js:193","退出登录失败:",t)}},ensureLogin:async()=>!!c()||await u(),getUserProfile:async()=>{try{const t=await new Promise(((t,e)=>{uni.getUserProfile({desc:"用于完善用户资料",success:t,fail:e})}));return t.userInfo&&(i.value={...i.value,...t.userInfo},uni.setStorageSync("user_info",JSON.stringify(i.value))),t.userInfo}catch(t){throw e("error","at composables/useAuth.js:237","获取用户信息失败:",t),t}}}}(),{searchKeyword:l,searchResults:f,isLoading:p,hasResults:d,showNoResult:g,isSearchDisabled:v,executeSearch:y,pasteAndSearch:m,clearSearch:b,selectResult:S}=yr(),{formatCode:w}={formatCode:t=>t?t.replace(/(\d{3})(\d{3})(\d{3})(\d{3})/,"$1-$2-$3-$4"):"",validateCode:t=>!!t&&/^\d{12}$/.test(t.replace(/[-\s]/g,"")),highlightKeyword:(t,e)=>{if(!t||!e)return t;const r=new RegExp(`(${e})`,"gi");return t.replace(r,'<span class="highlight">$1</span>')}},E=t.ref(!1);t.ref(!0),t.onMounted((async()=>{e("log","at pages/index/index.vue:186","AI联行助手页面已加载");if(!h()){E.value=!0;try{await c(),E.value=!1}catch(t){e("log","at pages/index/index.vue:200","自动登录失败，需要用户手动登录")}}}));const _=async()=>{try{if(!(await u()))return uni.showToast({title:"请先登录",icon:"none"}),void(E.value=!0);await y()}catch(t){e("error","at pages/index/index.vue:222","搜索失败:",t)}},x=async()=>{try{if(!(await u()))return uni.showToast({title:"请先登录",icon:"none"}),void(E.value=!0);await m()}catch(t){e("error","at pages/index/index.vue:243","粘贴搜索失败:",t)}},B=b,T=w;return(e,r)=>(t.openBlock(),t.createElementBlock("view",{class:"container"},[t.createElementVNode("view",{class:"content"},[t.createElementVNode("view",{class:"qr-section"},[t.createElementVNode("view",{class:"qr-code"},[t.createElementVNode("image",{class:"qr-image",src:"/static/logo.jpg",mode:"aspectFit"})]),t.createElementVNode("view",{class:"website-info"},[t.createElementVNode("text",{class:"website-title"},"AI联行助手"),t.createElementVNode("text",{class:"website-url"},"依托AI大模型能力，对各大银行主页公开的联行号提供查询，精确到支行点"),t.createElementVNode("text",{class:"website-url"})])]),t.createElementVNode("view",{class:"search-section"},[t.createElementVNode("view",{class:"search-input-wrapper"},[t.withDirectives(t.createElementVNode("input",{class:"search-input","onUpdate:modelValue":r[0]||(r[0]=e=>t.isRef(l)?l.value=e:null),maxlength:"30",placeholder:"请输入银行/地区名/关键字/联行号","placeholder-style":"color: #999;"},null,512),[[t.vModelText,t.unref(l)]])]),t.createElementVNode("text",{class:"search-tip"},"请输入银行/地区名/关键字，例如：工行北京"),t.createElementVNode("text",{class:"search-tip"},"也可以输入12位联行号，例如：************")]),t.createElementVNode("view",{class:"button-section"},[t.createElementVNode("button",{class:t.normalizeClass(["action-button search-btn",{disabled:t.unref(v)}]),disabled:t.unref(v),onClick:_},t.toDisplayString(t.unref(p)?"搜索中...":"搜索"),11,["disabled"]),t.createElementVNode("button",{class:t.normalizeClass(["action-button paste-btn",{disabled:t.unref(p)}]),disabled:t.unref(p),onClick:x}," 粘贴 ",10,["disabled"]),t.createElementVNode("button",{class:"action-button clear-btn",onClick:r[1]||(r[1]=(...e)=>t.unref(B)&&t.unref(B)(...e))}," 清除 ")]),t.unref(d)?(t.openBlock(),t.createElementBlock("view",{key:0,class:"result-section"},[t.createElementVNode("view",{class:"result-header"},[t.createElementVNode("text",{class:"result-title"},"查询结果")]),t.createElementVNode("view",{class:"result-list"},[(t.openBlock(!0),t.createElementBlock(t.Fragment,null,t.renderList(t.unref(f),((e,r)=>(t.openBlock(),t.createElementBlock("view",{class:"result-item",key:r,onClick:r=>t.unref(S)(e)},[t.createElementVNode("view",{class:"result-code"},t.toDisplayString(t.unref(T)(e.code)),1),t.createElementVNode("view",{class:"result-info"},[t.createElementVNode("text",{class:"bank-name"},t.toDisplayString(e.bankName),1),t.createElementVNode("text",{class:"branch-name"},t.toDisplayString(e.branchName),1)]),e.distance?(t.openBlock(),t.createElementBlock("view",{key:0,class:"result-distance"},[t.createElementVNode("text",{class:"distance-text"},"匹配度："+t.toDisplayString(e.distance),1)])):t.createCommentVNode("",!0)],8,["onClick"])))),128))])])):t.createCommentVNode("",!0),t.unref(p)?(t.openBlock(),t.createElementBlock("view",{key:1,class:"loading"},[t.createElementVNode("text",{class:"loading-text"},"查询中...")])):t.createCommentVNode("",!0),t.unref(g)?(t.openBlock(),t.createElementBlock("view",{key:2,class:"no-result"},[t.createElementVNode("text",{class:"no-result-text"},"未找到相关结果")])):t.createCommentVNode("",!0)])]))}});const mr={__name:"App",setup:t=>(s((()=>{e("log","at App.vue:5","App Launch"),uni.setEnableDebug({enableDebug:!1})})),i((()=>{e("log","at App.vue:12","App Show")})),n((()=>{e("log","at App.vue:16","App Hide")})),()=>{})};const{app:br,Vuex:Sr,Pinia:wr}={app:t.createVueApp(mr)};uni.Vuex=Sr,uni.Pinia=wr,br.provide("__globalStyles",__uniConfig.styles),br._component.mpType="app",br._component.render=()=>{},br.mount("#app")}(Vue);
