
.container {
		min-height: 100vh;
		background-color: #f5f5f5;
}

	/* 顶部标题栏 */
.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #e0e0e0;
}
.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
}
.header-actions {
		display: flex;
		gap: 20rpx;
}
.action-btn {
		font-size: 32rpx;
		color: #666666;
		padding: 10rpx;
}

	/* 主要内容区域 */
.content {
		padding: 30rpx;
}

	/* 二维码和网站信息区域 */
.qr-section {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.qr-code {
		width: 120rpx;
		height: 120rpx;
		margin-right: 30rpx;
}
.qr-image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
}
.website-info {
		flex: 1;
}
.website-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
}
.website-name {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: #4CAF50;
		margin-bottom: 8rpx;
}
.website-url {
		display: block;
		font-size: 28rpx;
		color: #666666;
}

	/* 搜索区域 */
.search-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.search-input-wrapper {
		position: relative;
		margin-bottom: 20rpx;
}
.search-input {
		width: 100%;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		padding: 0 20rpx;
		font-size: 32rpx;
		background-color: #fafafa;
		box-sizing: border-box;
}
.search-input:focus {
		border-color: #4CAF50;
		background-color: #ffffff;
}
.search-tip {
		display: block;
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 8rpx;
		line-height: 1.4;
}

	/* 按钮区域 */
.button-section {
		display: flex;
		gap: 20rpx;
		margin-bottom: 30rpx;
}
.action-button {
		flex: 1;
		height: 80rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
}
.search-btn {
		background-color: #4CAF50;
		color: #ffffff;
}
.paste-btn {
		background-color: #2196F3;
		color: #ffffff;
}
.clear-btn {
		background-color: #FF9800;
		color: #ffffff;
}
.action-button:active {
		opacity: 0.8;
		transform: scale(0.98);
}
.action-button.disabled {
		opacity: 0.5;
		cursor: not-allowed;
}
.action-button.disabled:active {
		transform: none;
		opacity: 0.5;
}

	/* 查询结果区域 */
.result-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 30rpx;
}
.result-header {
		background-color: #f8f9fa;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #e0e0e0;
}
.result-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
}
.result-item {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		position: relative;
}
.result-item:last-child {
		border-bottom: none;
}
.result-item:active {
		background-color: #f8f9fa;
}
.result-code {
		font-size: 36rpx;
		font-weight: bold;
		color: #2196F3;
		font-family: 'Courier New', monospace;
}
.result-info {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
}
.bank-name {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
		line-height: 1.4;
}
.branch-name {
		font-size: 24rpx;
		color: #666666;
		line-height: 1.4;
}
.result-distance {
		position: absolute;
		top: 30rpx;
		right: 30rpx;
}
.distance-text {
		font-size: 24rpx;
		color: #999999;
		background-color: #f0f0f0;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
}

	/* 加载状态 */
.loading {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 60rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.loading-text {
		font-size: 28rpx;
		color: #666666;
}

	/* 无结果提示 */
.no-result {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 60rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.no-result-text {
		font-size: 28rpx;
		color: #999999;
}

	/* 响应式设计 */
@media (max-width: 750rpx) {
.qr-section {
			flex-direction: column;
			text-align: center;
}
.qr-code {
			margin-right: 0;
			margin-bottom: 20rpx;
}
.button-section {
			flex-direction: column;
}
.action-button {
			margin-bottom: 15rpx;
}
}
