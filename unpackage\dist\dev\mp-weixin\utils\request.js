"use strict";
const common_vendor = require("../common/vendor.js");
const utils_crypto = require("./crypto.js");
const API_CONFIG = {
  baseURL: "https://your-api-domain.com/api",
  // 替换为实际的API地址
  timeout: 1e4,
  header: {
    "Content-Type": "application/json"
  }
};
class EncryptedRequest {
  /**
   * 发送加密请求
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  static async request(options) {
    const {
      url,
      method = "GET",
      data = {},
      header = {},
      needAuth = true,
      encrypt = true
    } = options;
    try {
      let requestData = data;
      let aesKey = null;
      if (encrypt && (method === "POST" || method === "PUT")) {
        const encryptedRequest = utils_crypto.crypto.encryptRequest(data);
        requestData = {
          encryptedData: encryptedRequest.encryptedData,
          encryptedKey: encryptedRequest.encryptedKey
        };
        aesKey = encryptedRequest.aesKey;
        common_vendor.index.__f__("log", "at utils/request.js:49", "请求数据已加密，AES密钥已保存");
      }
      const requestHeader = { ...API_CONFIG.header, ...header };
      if (needAuth) {
        const openid = common_vendor.index.getStorageSync("user_openid");
        if (openid) {
          requestHeader["Authorization"] = `Bearer ${openid}`;
        }
      }
      common_vendor.index.__f__("log", "at utils/request.js:62", `发送${encrypt ? "加密" : "普通"}请求:`, {
        url: `${API_CONFIG.baseURL}${url}`,
        method,
        encrypt
      });
      const response = await common_vendor.index.request({
        url: `${API_CONFIG.baseURL}${url}`,
        method,
        data: requestData,
        header: requestHeader,
        timeout: API_CONFIG.timeout
      });
      if (response.statusCode === 200) {
        let responseData = response.data;
        if (encrypt && aesKey && responseData) {
          if (responseData.encryptedData) {
            common_vendor.index.__f__("log", "at utils/request.js:84", "检测到加密响应，开始解密...");
            responseData = utils_crypto.crypto.decryptResponse(responseData.encryptedData, aesKey);
          } else if (typeof responseData === "string" && responseData.length > 0) {
            common_vendor.index.__f__("log", "at utils/request.js:88", "检测到完全加密的响应，开始解密...");
            responseData = utils_crypto.crypto.decryptResponse(responseData, aesKey);
          } else {
            common_vendor.index.__f__("log", "at utils/request.js:91", "响应未加密或格式异常");
          }
        }
        return {
          success: true,
          data: responseData,
          statusCode: response.statusCode
        };
      } else {
        let errorMessage = `请求失败: ${response.statusCode}`;
        if (response.data && response.data.message) {
          errorMessage += ` - ${response.data.message}`;
        }
        throw new Error(errorMessage);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/request.js:110", "请求失败:", error);
      if (error.errMsg && error.errMsg.includes("request:fail")) {
        throw new Error("网络连接失败，请检查网络设置");
      }
      throw error;
    }
  }
  /**
   * GET请求
   * @param {string} url - 请求URL
   * @param {Object} params - 查询参数
   * @param {Object} options - 其他选项
   * @returns {Promise<Object>} 响应数据
   */
  static async get(url, params = {}, options = {}) {
    const queryString = Object.keys(params).map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join("&");
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    return this.request({
      url: fullUrl,
      method: "GET",
      encrypt: false,
      // GET请求通常不加密
      ...options
    });
  }
  /**
   * POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} options - 其他选项
   * @returns {Promise<Object>} 响应数据
   */
  static async post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: "POST",
      data,
      encrypt: true,
      // POST请求默认加密
      ...options
    });
  }
  /**
   * PUT请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求数据
   * @param {Object} options - 其他选项
   * @returns {Promise<Object>} 响应数据
   */
  static async put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: "PUT",
      data,
      encrypt: true,
      ...options
    });
  }
  /**
   * DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} options - 其他选项
   * @returns {Promise<Object>} 响应数据
   */
  static async delete(url, options = {}) {
    return this.request({
      url,
      method: "DELETE",
      encrypt: false,
      ...options
    });
  }
}
const http = {
  get: (url, params, options) => EncryptedRequest.get(url, params, options),
  post: (url, data, options) => EncryptedRequest.post(url, data, options),
  put: (url, data, options) => EncryptedRequest.put(url, data, options),
  delete: (url, options) => EncryptedRequest.delete(url, options)
};
exports.http = http;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/request.js.map
