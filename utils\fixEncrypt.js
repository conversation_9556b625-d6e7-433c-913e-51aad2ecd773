
// 引入RSA库（需适配小程序环境）
const JSEncrypt = require('./utils/jsencrypt.min.js');
const CryptoJS = require('./utils/crypto-js');

// 生成随机AES密钥（16字节）
function generateAESKey() {
  return CryptoJS.lib.WordArray.random(16).toString();
}

// RSA加密AES密钥
function encryptAESKeyWithRSA(aesKey, publicKey) {
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(publicKey);
  return encryptor.encrypt(aesKey);
}

// AES加密业务数据
function encryptDataWithAES(data, aesKey) {
  const key = CryptoJS.enc.Utf8.parse(aesKey);
  const iv = CryptoJS.enc.Utf8.parse(aesKey.substring(0, 16));
  return CryptoJS.AES.encrypt(data, key, { 
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  }).toString();
}

// 示例调用
const publicKey = '-----BEGIN PUBLIC KEY-----...';
const rawData = { phone: '13800138000' };
const aesKey = generateAESKey();
const encryptedAESKey = encryptAESKeyWithRSA(aesKey, publicKey);
const encryptedData = encryptDataWithAES(JSON.stringify(rawData), aesKey);

wx.request({
  url: 'https://api.example.com/secure',
  data: {
    key: encryptedAESKey,
    data: encryptedData
  }
});
