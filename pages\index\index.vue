<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<!-- <view class="header">
			<text class="header-title">AI联行助手</text>
			<view class="header-actions">
				<text class="action-btn">•••</text>
				<text class="action-btn">—</text>
				<text class="action-btn">⊙</text>
			</view>
		</view> -->

		<!-- 主要内容区域 -->
		<view class="content">
			<!-- 二维码和网站信息 -->
			<view class="qr-section">
				<view class="qr-code">
					<image class="qr-image" src="/static/logo.jpg" mode="aspectFit"></image>
				</view>
				<view class="website-info">
					<text class="website-title">AI联行助手</text>
					<text class="website-url">依托AI大模型能力，对各大银行主页公开的联行号提供查询，精确到支行点</text>
					<text class="website-url"></text>
				</view>
			</view>

			<!-- 搜索区域 -->
			<view class="search-section">
				<view class="search-input-wrapper">
					<input
						class="search-input"
						v-model="searchKeyword"
						placeholder="工行北京王府井支行"
						placeholder-style="color: #999;"
					/>
				</view>
				<text class="search-tip">请输入银行/地区名/关键字，例如：工行北京</text>
				<text class="search-tip">也可以输入12位联行号，例如：102100000072</text>
			</view>

			<!-- 按钮区域 -->
			<view class="button-section">
				<button
					class="action-button search-btn"
					:disabled="isSearchDisabled"
					:class="{ disabled: isSearchDisabled }"
					@click="handleSearch"
				>
					{{ isLoading ? '搜索中...' : '搜索' }}
				</button>
				<button
					class="action-button paste-btn"
					:disabled="isLoading"
					:class="{ disabled: isLoading }"
					@click="handlePaste"
				>
					粘贴
				</button>
				<button
					class="action-button clear-btn"
					@click="handleClear"
				>
					清除
				</button>
			</view>

			<!-- 查询结果区域 -->
			<view class="result-section" v-if="hasResults">
				<view class="result-header">
					<text class="result-title">查询结果</text>
				</view>
				<view class="result-list">
					<view
						class="result-item"
						v-for="(item, index) in searchResults"
						:key="index"
						@click="selectResult(item)"
					>
						<view class="result-code">{{formatBankCodeDisplay(item.code)}}</view>
						<view class="result-info">
							<text class="bank-name">{{item.bankName}}</text>
							<text class="branch-name">{{item.branchName}}</text>
						</view>
						<view class="result-distance" v-if="item.distance">
							<text class="distance-text">距离{{item.distance}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载状态 -->
			<view class="loading" v-if="isLoading">
				<text class="loading-text">查询中...</text>
			</view>

			<!-- 无结果提示 -->
			<view class="no-result" v-if="showNoResult">
				<text class="no-result-text">未找到相关结果</text>
			</view>
		</view>

		<!-- 登录弹窗 -->
		<!-- <view class="login-modal" v-if="showLoginModal" @click="closeLoginModal">
			<view class="login-content" @click.stop>
				<view class="login-header">
					<text class="login-title">欢迎使用AI联行助手</text>
					<text class="login-subtitle">需要登录后才能使用查询功能</text>
				</view>

				<view class="login-body">
					<view class="login-icon">
						<text class="icon-text">🔐</text>
					</view>
					<text class="login-desc">为了提供更好的服务体验，请先进行微信授权登录</text>
				</view>

				<view class="login-footer">
					<button
						class="login-btn"
						:loading="isLogging"
						:disabled="isLogging"
						@click="handleLogin"
					>
						{{ isLogging ? '登录中...' : '微信授权登录' }}
					</button>
					<button class="cancel-btn" @click="closeLoginModal">稍后再说</button>
				</view>
			</view>
		</view> -->

		<!-- 用户信息显示 -->
		<!-- <view class="user-info" v-if="isLoggedIn && userInfo">
			<text class="user-welcome">欢迎，{{ userInfo.nickname || '用户' }}</text>
			<text class="user-id">ID: {{ openid.substring(0, 8) }}...</text>
		</view> -->

		<!-- 开发测试按钮 -->
		<!-- <view class="dev-tools" v-if="isDevelopment">
			<button class="dev-btn" @click="goToTestPage">🔧 加密测试</button>
		</view> -->
	</view>
</template>

<script setup>
	import { onMounted, ref } from 'vue'
	import { useBankSearch, useBankCodeFormatter } from '@/composables/useBankSearch.js'
	import { useAuth } from '@/composables/useAuth.js'

	// 使用认证组合式函数
	const {
		isLoggedIn,
		userInfo,
		openid,
		isLogging,
		needLogin,
		checkLoginStatus,
		wxLogin,
		ensureLogin
	} = useAuth()

	// 使用银行搜索组合式函数
	const {
		searchKeyword,
		searchResults,
		isLoading,
		hasResults,
		showNoResult,
		isSearchDisabled,
		executeSearch,
		pasteAndSearch,
		clearSearch,
		selectResult
	} = useBankSearch()

	const { formatCode } = useBankCodeFormatter()

	// 显示登录弹窗
	const showLoginModal = ref(false)

	// 开发模式标识
	const isDevelopment = ref(true) // 生产环境设置为false

	// 页面加载时的初始化操作
	onMounted(async () => {
		console.log('AI联行助手页面已加载')

		// 检查登录状态
		const isLoggedInLocal = checkLoginStatus()

		if (!isLoggedInLocal) {
			// 如果未登录，显示登录提示
			showLoginModal.value = true

			// 自动尝试登录
			try {
				await wxLogin()
				showLoginModal.value = false
			} catch (error) {
				console.log('自动登录失败，需要用户手动登录')
			}
		}
	})

	// 处理搜索（确保已登录）
	const handleSearch = async () => {
		try {
			// 确保用户已登录
			const loginSuccess = await ensureLogin()
			if (!loginSuccess) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				showLoginModal.value = true
				return
			}

			// 执行搜索
			await executeSearch()
		} catch (error) {
			console.error('搜索失败:', error)
		}
	}

	// 处理粘贴（确保已登录）
	const handlePaste = async () => {
		try {
			// 确保用户已登录
			const loginSuccess = await ensureLogin()
			if (!loginSuccess) {
				uni.showToast({
					title: '请先登录',
					icon: 'none'
				})
				showLoginModal.value = true
				return
			}

			// 执行粘贴搜索
			await pasteAndSearch()
		} catch (error) {
			console.error('粘贴搜索失败:', error)
		}
	}

	// 处理清除
	const handleClear = clearSearch

	// 格式化银行代码显示
	const formatBankCodeDisplay = formatCode

	// 手动登录
	const handleLogin = async () => {
		try {
			const success = await wxLogin()
			if (success) {
				showLoginModal.value = false
			}
		} catch (error) {
			console.error('登录失败:', error)
		}
	}

	// 关闭登录弹窗
	const closeLoginModal = () => {
		showLoginModal.value = false
	}

	// 跳转到测试页面
	const goToTestPage = () => {
		uni.navigateTo({
			url: '/pages/test/crypto'
		})
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	/* 顶部标题栏 */
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #e0e0e0;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}

	.header-actions {
		display: flex;
		gap: 20rpx;
	}

	.action-btn {
		font-size: 32rpx;
		color: #666666;
		padding: 10rpx;
	}

	/* 主要内容区域 */
	.content {
		padding: 30rpx;
	}

	/* 二维码和网站信息区域 */
	.qr-section {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.qr-code {
		width: 120rpx;
		height: 120rpx;
		margin-right: 30rpx;
	}

	.qr-image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}

	.website-info {
		flex: 1;
	}

	.website-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}

	.website-name {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: #4CAF50;
		margin-bottom: 8rpx;
	}

	.website-url {
		display: block;
		font-size: 28rpx;
		color: #666666;
	}

	/* 搜索区域 */
	.search-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.search-input-wrapper {
		position: relative;
		margin-bottom: 20rpx;
	}

	.search-input {
		width: 100%;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		padding: 0 20rpx;
		font-size: 32rpx;
		background-color: #fafafa;
		box-sizing: border-box;
	}

	.search-input:focus {
		border-color: #4CAF50;
		background-color: #ffffff;
	}

	.search-tip {
		display: block;
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 8rpx;
		line-height: 1.4;
	}

	/* 按钮区域 */
	.button-section {
		display: flex;
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	.action-button {
		flex: 1;
		height: 80rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.search-btn {
		background-color: #4CAF50;
		color: #ffffff;
	}

	.paste-btn {
		background-color: #2196F3;
		color: #ffffff;
	}

	.clear-btn {
		background-color: #FF9800;
		color: #ffffff;
	}

	.action-button:active {
		opacity: 0.8;
		transform: scale(0.98);
	}

	.action-button.disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.action-button.disabled:active {
		transform: none;
		opacity: 0.5;
	}

	/* 查询结果区域 */
	.result-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 30rpx;
	}

	.result-header {
		background-color: #f8f9fa;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #e0e0e0;
	}

	.result-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}



	.result-item {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		position: relative;
	}

	.result-item:last-child {
		border-bottom: none;
	}

	.result-item:active {
		background-color: #f8f9fa;
	}

	.result-code {
		font-size: 36rpx;
		font-weight: bold;
		color: #2196F3;
		font-family: 'Courier New', monospace;
	}

	.result-info {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.bank-name {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
		line-height: 1.4;
	}

	.branch-name {
		font-size: 24rpx;
		color: #666666;
		line-height: 1.4;
	}

	.result-distance {
		position: absolute;
		top: 30rpx;
		right: 30rpx;
	}

	.distance-text {
		font-size: 24rpx;
		color: #999999;
		background-color: #f0f0f0;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
	}

	/* 加载状态 */
	.loading {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 60rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.loading-text {
		font-size: 28rpx;
		color: #666666;
	}

	/* 无结果提示 */
	.no-result {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 60rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.no-result-text {
		font-size: 28rpx;
		color: #999999;
	}

	/* 登录弹窗样式 */
	.login-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.login-content {
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 60rpx 40rpx;
		margin: 40rpx;
		max-width: 600rpx;
		width: 100%;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
	}

	.login-header {
		text-align: center;
		margin-bottom: 40rpx;
	}

	.login-title {
		display: block;
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 16rpx;
	}

	.login-subtitle {
		display: block;
		font-size: 28rpx;
		color: #666666;
		line-height: 1.4;
	}

	.login-body {
		text-align: center;
		margin-bottom: 50rpx;
	}

	.login-icon {
		margin-bottom: 30rpx;
	}

	.icon-text {
		font-size: 80rpx;
	}

	.login-desc {
		font-size: 28rpx;
		color: #666666;
		line-height: 1.5;
	}

	.login-footer {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.login-btn {
		background-color: #4CAF50;
		color: #ffffff;
		border: none;
		border-radius: 50rpx;
		height: 88rpx;
		font-size: 32rpx;
		font-weight: bold;
	}

	.login-btn[disabled] {
		opacity: 0.6;
	}

	.cancel-btn {
		background-color: transparent;
		color: #999999;
		border: 1rpx solid #e0e0e0;
		border-radius: 50rpx;
		height: 80rpx;
		font-size: 28rpx;
	}

	/* 用户信息样式 */
	.user-info {
		position: fixed;
		top: 120rpx;
		right: 30rpx;
		background-color: rgba(76, 175, 80, 0.1);
		border: 1rpx solid #4CAF50;
		border-radius: 20rpx;
		padding: 20rpx;
		z-index: 100;
	}

	.user-welcome {
		display: block;
		font-size: 24rpx;
		color: #4CAF50;
		font-weight: bold;
		margin-bottom: 8rpx;
	}

	.user-id {
		display: block;
		font-size: 20rpx;
		color: #666666;
	}

	/* 开发工具样式 */
	.dev-tools {
		position: fixed;
		bottom: 30rpx;
		right: 30rpx;
		z-index: 999;
	}

	.dev-btn {
		background-color: #FF5722;
		color: #ffffff;
		border: none;
		border-radius: 50rpx;
		padding: 20rpx 30rpx;
		font-size: 24rpx;
		box-shadow: 0 4rpx 12rpx rgba(255, 87, 34, 0.3);
	}

	.dev-btn:active {
		transform: scale(0.95);
	}

	/* 响应式设计 */
	@media (max-width: 750rpx) {
		.qr-section {
			flex-direction: column;
			text-align: center;
		}

		.qr-code {
			margin-right: 0;
			margin-bottom: 20rpx;
		}

		.button-section {
			flex-direction: column;
		}

		.action-button {
			margin-bottom: 15rpx;
		}

		.login-content {
			margin: 20rpx;
			padding: 40rpx 30rpx;
		}

		.user-info {
			top: 100rpx;
			right: 20rpx;
			padding: 15rpx;
		}
	}
</style>
