<template>
	<view class="container">
		<!-- 顶部标题栏 -->
		<view class="header">
			<text class="header-title">联行号快查</text>
			<view class="header-actions">
				<text class="action-btn">•••</text>
				<text class="action-btn">—</text>
				<text class="action-btn">⊙</text>
			</view>
		</view>

		<!-- 主要内容区域 -->
		<view class="content">
			<!-- 二维码和网站信息 -->
			<view class="qr-section">
				<view class="qr-code">
					<image class="qr-image" src="/static/logo.png" mode="aspectFit"></image>
				</view>
				<view class="website-info">
					<text class="website-title">联行号快查</text>
					<text class="website-name">查行号.com</text>
					<text class="website-url">www.chahanghao.com</text>
				</view>
			</view>

			<!-- 搜索区域 -->
			<view class="search-section">
				<view class="search-input-wrapper">
					<input
						class="search-input"
						v-model="searchKeyword"
						placeholder="工行北京王府井支行"
						placeholder-style="color: #999;"
					/>
				</view>
				<text class="search-tip">请输入银行/地区名/关键字，例如：工行北京或X1行</text>
				<text class="search-tip">或拨打电话12306查询，例如：10210002503</text>
			</view>

			<!-- 按钮区域 -->
			<view class="button-section">
				<button class="action-button search-btn" @click="handleSearch">搜索</button>
				<button class="action-button paste-btn" @click="handlePaste">粘贴</button>
				<button class="action-button clear-btn" @click="handleClear">清除</button>
			</view>

			<!-- 查询结果区域 -->
			<view class="result-section" v-if="searchResults.length > 0">
				<view class="result-header">
					<text class="result-title">查询结果</text>
				</view>
				<view class="result-list">
					<view
						class="result-item"
						v-for="(item, index) in searchResults"
						:key="index"
						@click="selectResult(item)"
					>
						<view class="result-code">{{formatBankCode(item.code)}}</view>
						<view class="result-info">
							<text class="bank-name">{{item.bankName}}</text>
							<text class="branch-name">{{item.branchName}}</text>
						</view>
						<view class="result-distance" v-if="item.distance">
							<text class="distance-text">距离{{item.distance}}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载状态 -->
			<view class="loading" v-if="isLoading">
				<text class="loading-text">查询中...</text>
			</view>

			<!-- 无结果提示 -->
			<view class="no-result" v-if="showNoResult">
				<text class="no-result-text">未找到相关结果</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { BankCodeAPI, formatBankCode } from '@/utils/api.js'

	export default {
		data() {
			return {
				searchKeyword: '',
				searchResults: [],
				isLoading: false,
				showNoResult: false
			}
		},
		onLoad() {
			// 页面加载时的初始化操作
		},
		methods: {
			// 搜索功能
			async handleSearch() {
				if (!this.searchKeyword.trim()) {
					uni.showToast({
						title: '请输入搜索关键字',
						icon: 'none'
					});
					return;
				}

				this.isLoading = true;
				this.showNoResult = false;
				this.searchResults = [];

				try {
					// 调用查询接口
					const result = await this.searchBankCode(this.searchKeyword);
					if (result && result.length > 0) {
						this.searchResults = result;
					} else {
						this.showNoResult = true;
					}
				} catch (error) {
					console.error('查询失败:', error);
					uni.showToast({
						title: '查询失败，请重试',
						icon: 'none'
					});
				} finally {
					this.isLoading = false;
				}
			},

			// 粘贴功能
			async handlePaste() {
				try {
					// 从剪贴板获取内容
					const clipboardData = await uni.getClipboardData();
					if (clipboardData.data) {
						this.searchKeyword = clipboardData.data;
						// 自动执行搜索
						this.handleSearch();
					} else {
						uni.showToast({
							title: '剪贴板为空',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('粘贴失败:', error);
					uni.showToast({
						title: '粘贴失败',
						icon: 'none'
					});
				}
			},

			// 清除功能
			handleClear() {
				this.searchKeyword = '';
				this.searchResults = [];
				this.showNoResult = false;
				this.isLoading = false;
			},

			// 选择查询结果
			selectResult(item) {
				// 复制联行号到剪贴板
				uni.setClipboardData({
					data: item.code,
					success: () => {
						uni.showToast({
							title: '联行号已复制',
							icon: 'success'
						});
					}
				});
			},

			// 调用查询接口
			async searchBankCode(keyword) {
				try {
					return await BankCodeAPI.searchBankCode(keyword);
				} catch (error) {
					console.error('查询失败:', error);
					throw error;
				}
			},

			// 格式化银行代码显示
			formatBankCode(code) {
				return formatBankCode(code);
			}
		}
	}
</script>

<style>
	.container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	/* 顶部标题栏 */
	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #ffffff;
		border-bottom: 1rpx solid #e0e0e0;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
	}

	.header-actions {
		display: flex;
		gap: 20rpx;
	}

	.action-btn {
		font-size: 32rpx;
		color: #666666;
		padding: 10rpx;
	}

	/* 主要内容区域 */
	.content {
		padding: 30rpx;
	}

	/* 二维码和网站信息区域 */
	.qr-section {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.qr-code {
		width: 120rpx;
		height: 120rpx;
		margin-right: 30rpx;
	}

	.qr-image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}

	.website-info {
		flex: 1;
	}

	.website-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 10rpx;
	}

	.website-name {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: #4CAF50;
		margin-bottom: 8rpx;
	}

	.website-url {
		display: block;
		font-size: 28rpx;
		color: #666666;
	}

	/* 搜索区域 */
	.search-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.search-input-wrapper {
		position: relative;
		margin-bottom: 20rpx;
	}

	.search-input {
		width: 100%;
		height: 80rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 12rpx;
		padding: 0 20rpx;
		font-size: 32rpx;
		background-color: #fafafa;
		box-sizing: border-box;
	}

	.search-input:focus {
		border-color: #4CAF50;
		background-color: #ffffff;
	}

	.search-tip {
		display: block;
		font-size: 24rpx;
		color: #999999;
		margin-bottom: 8rpx;
		line-height: 1.4;
	}

	/* 按钮区域 */
	.button-section {
		display: flex;
		gap: 20rpx;
		margin-bottom: 30rpx;
	}

	.action-button {
		flex: 1;
		height: 80rpx;
		border-radius: 12rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.search-btn {
		background-color: #4CAF50;
		color: #ffffff;
	}

	.paste-btn {
		background-color: #2196F3;
		color: #ffffff;
	}

	.clear-btn {
		background-color: #FF9800;
		color: #ffffff;
	}

	.action-button:active {
		opacity: 0.8;
		transform: scale(0.98);
	}

	/* 查询结果区域 */
	.result-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
		margin-bottom: 30rpx;
	}

	.result-header {
		background-color: #f8f9fa;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #e0e0e0;
	}

	.result-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}



	.result-item {
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
		display: flex;
		flex-direction: column;
		gap: 15rpx;
		position: relative;
	}

	.result-item:last-child {
		border-bottom: none;
	}

	.result-item:active {
		background-color: #f8f9fa;
	}

	.result-code {
		font-size: 36rpx;
		font-weight: bold;
		color: #2196F3;
		font-family: 'Courier New', monospace;
	}

	.result-info {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.bank-name {
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
		line-height: 1.4;
	}

	.branch-name {
		font-size: 24rpx;
		color: #666666;
		line-height: 1.4;
	}

	.result-distance {
		position: absolute;
		top: 30rpx;
		right: 30rpx;
	}

	.distance-text {
		font-size: 24rpx;
		color: #999999;
		background-color: #f0f0f0;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
	}

	/* 加载状态 */
	.loading {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 60rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.loading-text {
		font-size: 28rpx;
		color: #666666;
	}

	/* 无结果提示 */
	.no-result {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 60rpx;
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.no-result-text {
		font-size: 28rpx;
		color: #999999;
	}

	/* 响应式设计 */
	@media (max-width: 750rpx) {
		.qr-section {
			flex-direction: column;
			text-align: center;
		}

		.qr-code {
			margin-right: 0;
			margin-bottom: 20rpx;
		}

		.button-section {
			flex-direction: column;
		}

		.action-button {
			margin-bottom: 15rpx;
		}
	}
</style>
