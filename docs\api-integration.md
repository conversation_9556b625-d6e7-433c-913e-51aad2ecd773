# 银行查询API集成说明

本文档详细说明了银行查询功能与后台API的集成实现，包括加密通信、错误处理和降级策略。

## 🔗 API接口配置

### 基础配置
```javascript
// config/api.js
export const API_CONFIG = {
  BASE_URL: 'http://hd.peixue100.cn/ylhapp/bankNum',
  ENDPOINTS: {
    LOGIN: '/jscode2session',
    BANK_SEARCH: '/search',
    BANK_DETAIL: '/detail'
  }
}
```

### 接口地址
- **登录接口**: `http://hd.peixue100.cn/ylhapp/bankNum/jscode2session`
- **银行查询**: `http://hd.peixue100.cn/ylhapp/bankNum/search`
- **银行详情**: `http://hd.peixue100.cn/ylhapp/bankNum/detail`

## 🔐 加密通信流程

### 1. 请求加密
```javascript
// 准备请求参数
const requestData = {
  keyword: "工商银行",
  type: "bank_name",
  limit: 20,
  timestamp: Date.now()
}

// 混合加密
const encryptedRequest = crypto.encryptRequest(requestData)

// 发送请求
const response = await uni.request({
  url: getApiUrl('BANK_SEARCH'),
  method: 'POST',
  data: {
    parameter: encryptedRequest.encryptedData
  }
})
```

### 2. 响应解密
```javascript
// 检查响应状态
if (response.statusCode === 200 && response.data.code == 200) {
  // 解密响应数据
  const decryptedData = crypto.decryptResponse(
    response.data.data, 
    encryptedRequest.aesKey
  )
  
  // 处理解密后的数据
  return decryptedData
}
```

## 📊 数据格式规范

### 请求格式
```json
{
  "parameter": "RSA+AES加密后的Base64字符串"
}
```

### 加密前的请求数据
```json
{
  "keyword": "工商银行",
  "type": "bank_name",
  "limit": 20,
  "timestamp": *************
}
```

### 响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": "AES加密后的响应数据",
  "timestamp": *************
}
```

### 解密后的响应数据
```json
{
  "success": true,
  "results": [
    {
      "code": "************",
      "bankName": "中国工商银行",
      "branchName": "北京分行营业部",
      "address": "北京市西城区复兴门内大街55号",
      "phone": "010-********"
    }
  ]
}
```

## 🛠 API实现细节

### 银行查询API
```javascript
// utils/api.js
static async searchBankCode(keyword) {
  // 1. 参数验证
  if (!ApiUtils.validateSearchParams({ keyword })) {
    throw new Error('搜索关键字不能为空')
  }

  // 2. 检测搜索类型
  const searchType = detectSearchType(keyword)

  // 3. 构建请求参数
  const requestData = ApiUtils.buildSearchParams({
    keyword: keyword,
    type: searchType,
    limit: 20
  })

  // 4. 加密请求
  const encryptedRequest = crypto.encryptRequest(requestData)

  // 5. 发送请求
  const response = await uni.request({
    url: getApiUrl('BANK_SEARCH'),
    method: 'POST',
    data: { parameter: encryptedRequest.encryptedData }
  })

  // 6. 解密响应
  const decryptedData = crypto.decryptResponse(
    response.data.data, 
    encryptedRequest.aesKey
  )

  // 7. 格式化结果
  return decryptedData.map(item => ApiUtils.formatBankInfo(item))
}
```

### 重试机制
```javascript
// 带重试的API请求
static async requestWithFallback(requestData, keyword, searchType) {
  let lastError = null

  // 最多重试3次
  for (let attempt = 0; attempt < 3; attempt++) {
    try {
      // 发送请求
      const response = await this.sendRequest(requestData)
      return this.processResponse(response)
      
    } catch (error) {
      lastError = error
      
      // 检查是否应该重试
      if (!RETRY_CONFIG.shouldRetry(error, attempt)) {
        break
      }
      
      // 等待重试延迟
      const delay = RETRY_CONFIG.getRetryDelay(attempt)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  // 所有重试失败，使用模拟数据
  return this.getMockData(keyword, searchType)
}
```

## 🔄 降级策略

### 1. API失败降级
```javascript
// API请求失败时自动使用模拟数据
try {
  const results = await this.callRealAPI(requestData)
  return results
} catch (error) {
  console.log('API请求失败，使用模拟数据:', error.message)
  return this.getMockData(keyword, searchType)
}
```

### 2. 网络错误处理
```javascript
// 网络错误检测
if (error.errMsg && error.errMsg.includes('request:fail')) {
  console.log('网络连接失败，使用本地数据')
  return this.getLocalData(keyword)
}
```

### 3. 解密失败处理
```javascript
// 解密失败时的处理
try {
  const decryptedData = crypto.decryptResponse(encryptedData, aesKey)
  return decryptedData
} catch (decryptError) {
  console.error('数据解密失败:', decryptError)
  throw new Error('数据解密失败，请重试')
}
```

## 🧪 测试验证

### 1. API连通性测试
```javascript
// 测试API是否可访问
const testBankAPI = async () => {
  const testKeywords = ['工商银行', '************', '北京', '建行']
  
  for (const keyword of testKeywords) {
    try {
      const results = await BankCodeAPI.searchBankCode(keyword)
      console.log(`查询"${keyword}"成功:`, results.length, '条结果')
    } catch (error) {
      console.error(`查询"${keyword}"失败:`, error.message)
    }
  }
}
```

### 2. 加密通信测试
```javascript
// 测试加密解密流程
const testEncryptedCommunication = async () => {
  const testData = { keyword: "测试", timestamp: Date.now() }
  
  // 加密
  const encrypted = crypto.encryptRequest(testData)
  console.log('加密成功:', encrypted.encryptedData.length, '字符')
  
  // 模拟服务器响应
  const mockResponse = JSON.stringify({ success: true, data: "测试响应" })
  const encryptedResponse = crypto.aesEncrypt(mockResponse, encrypted.aesKey)
  
  // 解密
  const decrypted = crypto.decryptResponse(encryptedResponse, encrypted.aesKey)
  console.log('解密成功:', decrypted)
}
```

### 3. 错误处理测试
```javascript
// 测试各种错误情况
const testErrorHandling = async () => {
  // 测试空关键字
  try {
    await BankCodeAPI.searchBankCode('')
  } catch (error) {
    console.log('空关键字错误处理正常:', error.message)
  }
  
  // 测试网络错误
  // 模拟网络断开...
  
  // 测试解密错误
  // 模拟错误的加密数据...
}
```

## 📱 前端集成

### 在组件中使用
```vue
<script setup>
import { useBankSearch } from '@/composables/useBankSearch.js'

const {
  searchKeyword,
  searchResults,
  isLoading,
  executeSearch
} = useBankSearch()

// 执行搜索
const handleSearch = async () => {
  try {
    await executeSearch()
    console.log('搜索完成:', searchResults.value)
  } catch (error) {
    console.error('搜索失败:', error)
  }
}
</script>
```

### 错误提示处理
```javascript
// 在useBankSearch.js中
const executeSearch = async () => {
  try {
    const result = await BankCodeAPI.searchBankCode(searchKeyword.value)
    searchResults.value = result || []
    
    if (result.length > 0) {
      uni.showToast({
        title: `找到 ${result.length} 条结果`,
        icon: 'success'
      })
    } else {
      uni.showToast({
        title: '未找到相关结果',
        icon: 'none'
      })
    }
  } catch (error) {
    uni.showToast({
      title: error.message || '查询失败',
      icon: 'none'
    })
  }
}
```

## 🔧 配置管理

### 环境配置
```javascript
// 开发环境
const DEV_CONFIG = {
  API_BASE_URL: 'http://dev.peixue100.cn/ylhapp/bankNum',
  ENABLE_MOCK: true,
  DEBUG_MODE: true
}

// 生产环境
const PROD_CONFIG = {
  API_BASE_URL: 'http://hd.peixue100.cn/ylhapp/bankNum',
  ENABLE_MOCK: false,
  DEBUG_MODE: false
}
```

### 动态配置
```javascript
// 根据环境自动选择配置
const getApiConfig = () => {
  const env = process.env.NODE_ENV || 'development'
  return env === 'production' ? PROD_CONFIG : DEV_CONFIG
}
```

## 🚀 性能优化

### 1. 请求缓存
```javascript
// 缓存查询结果
const resultCache = new Map()

const searchWithCache = async (keyword) => {
  const cacheKey = `search_${keyword}`
  
  if (resultCache.has(cacheKey)) {
    console.log('使用缓存结果')
    return resultCache.get(cacheKey)
  }
  
  const results = await BankCodeAPI.searchBankCode(keyword)
  resultCache.set(cacheKey, results)
  
  return results
}
```

### 2. 请求防抖
```javascript
// 防抖搜索
let searchTimer = null

const debouncedSearch = (keyword) => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    executeSearch(keyword)
  }, 300)
}
```

## 📋 部署清单

### 1. 服务器端要求
- ✅ 支持RSA解密（私钥）
- ✅ 支持AES解密
- ✅ 正确的响应格式
- ✅ CORS配置

### 2. 客户端配置
- ✅ 正确的API地址
- ✅ RSA公钥配置
- ✅ 错误处理机制
- ✅ 降级策略

### 3. 测试验证
- ✅ API连通性测试
- ✅ 加密解密测试
- ✅ 错误处理测试
- ✅ 性能测试

## 🔍 故障排除

### 常见问题
1. **API请求失败**: 检查网络连接和API地址
2. **解密失败**: 验证RSA公钥和AES密钥
3. **数据格式错误**: 检查服务器响应格式
4. **超时错误**: 调整超时时间或优化网络

### 调试方法
```javascript
// 启用详细日志
const DEBUG_MODE = true

if (DEBUG_MODE) {
  console.log('请求参数:', requestData)
  console.log('加密数据:', encryptedRequest)
  console.log('响应数据:', response)
  console.log('解密结果:', decryptedData)
}
```
