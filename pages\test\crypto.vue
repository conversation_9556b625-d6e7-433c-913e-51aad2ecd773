<template>
	<view class="container">
		<view class="header">
			<text class="title">加密功能测试</text>
		</view>
		
		<view class="content">
			<!-- 测试控制区域 -->
			<view class="test-section">
				<view class="section-title">
					<text class="title-text">加密解密测试</text>
				</view>
				
				<view class="test-controls">
					<button class="test-btn" @click="testAESEncryption">测试AES加密</button>
					<button class="test-btn" @click="testIVExtraction">测试IV提取</button>
					<button class="test-btn" @click="testRSAEncryption">测试RSA加密</button>
					<button class="test-btn" @click="testHybridEncryption">测试混合加密</button>
					<button class="test-btn" @click="testFullProcess">测试完整流程</button>
				</view>
			</view>
			
			<!-- 测试输入区域 -->
			<view class="input-section">
				<view class="section-title">
					<text class="title-text">测试数据</text>
				</view>
				
				<textarea 
					class="test-input" 
					v-model="testData" 
					placeholder="输入要测试的数据..."
				></textarea>
				
				<view class="input-controls">
					<button class="control-btn" @click="generateTestData">生成测试数据</button>
					<button class="control-btn" @click="clearTestData">清空数据</button>
				</view>
			</view>
			
			<!-- 测试结果区域 -->
			<view class="result-section">
				<view class="section-title">
					<text class="title-text">测试结果</text>
				</view>
				
				<view class="result-item" v-for="(result, index) in testResults" :key="index">
					<view class="result-header">
						<text class="result-title">{{ result.title }}</text>
						<text class="result-status" :class="result.success ? 'success' : 'error'">
							{{ result.success ? '✓ 成功' : '✗ 失败' }}
						</text>
					</view>
					
					<view class="result-content">
						<text class="result-text">{{ result.message }}</text>
						<view class="result-data" v-if="result.data">
							<text class="data-label">数据:</text>
							<text class="data-content">{{ result.data }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 密钥信息区域 -->
			<view class="key-section">
				<view class="section-title">
					<text class="title-text">密钥信息</text>
				</view>
				
				<view class="key-item">
					<text class="key-label">RSA公钥状态:</text>
					<text class="key-status" :class="rsaKeyValid ? 'valid' : 'invalid'">
						{{ rsaKeyValid ? '有效' : '无效' }}
					</text>
				</view>
				
				<view class="key-item">
					<text class="key-label">当前AES密钥:</text>
					<text class="key-value">{{ currentAESKey || '未生成' }}</text>
				</view>
				
				<button class="key-btn" @click="generateNewKeys">生成新密钥</button>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, onMounted } from 'vue'
	import { CryptoUtil } from '@/utils/crypto.js'
	import { keyManager } from '@/utils/keyGenerator.js'
	import CryptoJS from 'crypto-js'
	
	// 响应式数据
	const testData = ref('Hello, World! 这是一个测试数据 123456')
	const testResults = ref([])
	const rsaKeyValid = ref(false)
	const currentAESKey = ref('')
	
	// 页面加载时初始化
	onMounted(() => {
		initializeTest()
	})
	
	// 初始化测试环境
	const initializeTest = () => {
		try {
			// 验证RSA公钥
			rsaKeyValid.value = CryptoUtil.validateRSAPublicKey(CryptoUtil.getRSAPublicKey())
			
			// 生成AES密钥
			currentAESKey.value = CryptoUtil.generateAESKey()
			
			addTestResult('初始化', true, '测试环境初始化成功')
		} catch (error) {
			addTestResult('初始化', false, '测试环境初始化失败: ' + error.message)
		}
	}
	
	// 测试AES加密
	const testAESEncryption = async () => {
		try {
			const data = testData.value || '默认测试数据'
			const key = CryptoUtil.generateAESKey()

			// 加密
			const encrypted = CryptoUtil.aesEncrypt(data, key)
			addTestResult('AES加密', true,
				'数据加密成功（包含IV）',
				`加密结果: ${encrypted.substring(0, 50)}...`
			)

			// 验证加密结果是Base64格式
			const isBase64 = CryptoUtil.isBase64(encrypted)
			addTestResult('Base64格式验证', isBase64,
				isBase64 ? '加密结果为有效的Base64格式' : '加密结果不是有效的Base64格式'
			)

			// 验证IV提取
			const encryptedBytes = CryptoJS.enc.Base64.parse(encrypted)
			const hasValidLength = encryptedBytes.sigBytes >= 16
			addTestResult('IV长度验证', hasValidLength,
				hasValidLength ? `数据长度${encryptedBytes.sigBytes}字节，包含16字节IV` : '数据长度不足，无法包含IV'
			)

			// 解密
			const decrypted = CryptoUtil.aesDecrypt(encrypted, key)
			const success = decrypted === data

			addTestResult('AES解密', success,
				success ? '数据解密成功，与原数据一致' : '数据解密失败，与原数据不一致',
				success ? `原数据: ${data}` : `解密结果: ${decrypted}`
			)

			// 测试IV提取详情
			if (success) {
				const ivHex = encryptedBytes.toString(CryptoJS.enc.Hex).substring(0, 32)
				addTestResult('IV提取详情', true,
					'成功提取IV并解密',
					`提取的IV (前16字节): ${ivHex}`
				)
			}

		} catch (error) {
			addTestResult('AES加密解密', false, '测试失败: ' + error.message)
		}
	}

	// 测试IV提取功能
	const testIVExtraction = async () => {
		try {
			const testData = 'Hello, World! 这是IV提取测试数据 🔐'
			const key = CryptoUtil.generateAESKey()

			addTestResult('IV提取测试开始', true, `测试数据: ${testData}`)

			// 1. 加密数据
			const encrypted = CryptoUtil.aesEncrypt(testData, key)
			addTestResult('步骤1: 加密', true,
				'数据加密完成',
				`加密结果长度: ${encrypted.length} 字符`
			)

			// 2. 手动解析加密数据
			const encryptedBytes = CryptoJS.enc.Base64.parse(encrypted)
			addTestResult('步骤2: Base64解析', true,
				'成功解析Base64数据',
				`总字节数: ${encryptedBytes.sigBytes}`
			)

			// 3. 提取IV（前16字节）
			const ivBytes = CryptoJS.lib.WordArray.create(encryptedBytes.words.slice(0, 4))
			ivBytes.sigBytes = 16
			const ivHex = ivBytes.toString(CryptoJS.enc.Hex)
			addTestResult('步骤3: IV提取', true,
				'成功提取IV',
				`IV (Hex): ${ivHex}`
			)

			// 4. 提取实际加密数据（剩余字节）
			const actualDataBytes = CryptoJS.lib.WordArray.create(
				encryptedBytes.words.slice(4),
				encryptedBytes.sigBytes - 16
			)
			addTestResult('步骤4: 数据提取', true,
				'成功提取加密数据',
				`数据字节数: ${actualDataBytes.sigBytes}`
			)

			// 5. 使用提取的IV和数据进行解密
			const decrypted = CryptoJS.AES.decrypt(
				{ ciphertext: actualDataBytes },
				CryptoJS.enc.Utf8.parse(key),
				{
					iv: ivBytes,
					mode: CryptoJS.mode.CBC,
					padding: CryptoJS.pad.Pkcs7
				}
			)

			const decryptedText = decrypted.toString(CryptoJS.enc.Utf8)
			const success = decryptedText === testData

			addTestResult('步骤5: 手动解密', success,
				success ? '手动解密成功！' : '手动解密失败',
				`解密结果: ${decryptedText}`
			)

			// 6. 使用工具类方法解密（验证一致性）
			const utilDecrypted = CryptoUtil.aesDecrypt(encrypted, key)
			const utilSuccess = utilDecrypted === testData

			addTestResult('步骤6: 工具类解密', utilSuccess,
				utilSuccess ? '工具类解密成功！' : '工具类解密失败',
				`解密结果: ${utilDecrypted}`
			)

			// 7. 验证两种方法结果一致
			const consistent = decryptedText === utilDecrypted
			addTestResult('步骤7: 一致性验证', consistent,
				consistent ? '手动解密与工具类解密结果一致' : '两种解密方法结果不一致',
				consistent ? '✅ 测试通过' : '❌ 测试失败'
			)

		} catch (error) {
			addTestResult('IV提取测试', false, '测试失败: ' + error.message)
		}
	}

	// 测试RSA加密
	const testRSAEncryption = async () => {
		try {
			const data = JSON.stringify({ aesKey: currentAESKey.value })
			
			// RSA加密
			const encrypted = CryptoUtil.rsaEncrypt(data)
			addTestResult('RSA加密', true, 'RSA加密成功', encrypted.substring(0, 50) + '...')
			
		} catch (error) {
			addTestResult('RSA加密', false, '测试失败: ' + error.message)
		}
	}
	
	// 测试混合加密
	const testHybridEncryption = async () => {
		try {
			const data = {
				message: testData.value || '默认测试数据',
				timestamp: Date.now(),
				userId: 'test-user-123'
			}
			
			// 混合加密
			const encryptedRequest = CryptoUtil.encryptRequest(data)
			
			addTestResult('混合加密', true, '混合加密成功', 
				`加密数据长度: ${encryptedRequest.encryptedData.length}, 密钥长度: ${encryptedRequest.encryptedKey.length}`
			)
			
			// 模拟解密响应
			const mockResponse = JSON.stringify({
				success: true,
				message: '这是服务器返回的加密响应',
				data: { result: 'test-result' }
			})
			
			const encryptedResponse = CryptoUtil.aesEncrypt(mockResponse, encryptedRequest.aesKey)
			const decryptedResponse = CryptoUtil.decryptResponse(encryptedResponse, encryptedRequest.aesKey)
			
			addTestResult('响应解密', true, '响应解密成功', JSON.stringify(decryptedResponse))
			
		} catch (error) {
			addTestResult('混合加密', false, '测试失败: ' + error.message)
		}
	}
	
	// 测试完整流程
	const testFullProcess = async () => {
		try {
			// 运行完整的加密测试
			const success = CryptoUtil.testEncryption()
			addTestResult('完整流程测试', success, 
				success ? '所有加密功能测试通过' : '加密功能测试失败'
			)
		} catch (error) {
			addTestResult('完整流程测试', false, '测试失败: ' + error.message)
		}
	}
	
	// 生成测试数据
	const generateTestData = () => {
		const samples = [
			'Hello, World! 测试数据',
			'{"name":"张三","age":25,"city":"北京"}',
			'这是一个包含中文的测试字符串，用于验证加密功能是否正常工作。',
			'1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
			'特殊字符测试: !@#$%^&*()_+-=[]{}|;:,.<>?'
		]
		
		testData.value = samples[Math.floor(Math.random() * samples.length)]
	}
	
	// 清空测试数据
	const clearTestData = () => {
		testData.value = ''
	}
	
	// 生成新密钥
	const generateNewKeys = async () => {
		try {
			// 生成新的AES密钥
			currentAESKey.value = CryptoUtil.generateAESKey()
			
			// 可以选择生成新的RSA密钥对（需要时间较长）
			// const keyPair = await keyManager.generateKeyPair()
			// CryptoUtil.setRSAPublicKey(keyPair.publicKey)
			// rsaKeyValid.value = true
			
			addTestResult('密钥生成', true, '新密钥生成成功')
		} catch (error) {
			addTestResult('密钥生成', false, '密钥生成失败: ' + error.message)
		}
	}
	
	// 添加测试结果
	const addTestResult = (title, success, message, data = null) => {
		testResults.value.unshift({
			title,
			success,
			message,
			data,
			timestamp: new Date().toLocaleTimeString()
		})
		
		// 限制结果数量
		if (testResults.value.length > 10) {
			testResults.value = testResults.value.slice(0, 10)
		}
	}
</script>

<style>
	.container {
		padding: 30rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}

	.header {
		text-align: center;
		margin-bottom: 40rpx;
	}

	.title {
		font-size: 40rpx;
		font-weight: bold;
		color: #333333;
	}

	.content {
		display: flex;
		flex-direction: column;
		gap: 30rpx;
	}

	/* 通用区域样式 */
	.test-section,
	.input-section,
	.result-section,
	.key-section {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	.section-title {
		margin-bottom: 20rpx;
		border-bottom: 2rpx solid #e0e0e0;
		padding-bottom: 15rpx;
	}

	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
	}

	/* 测试控制区域 */
	.test-controls {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
	}

	.test-btn {
		flex: 1;
		min-width: 200rpx;
		height: 70rpx;
		background-color: #4CAF50;
		color: #ffffff;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
	}

	/* 输入区域 */
	.test-input {
		width: 100%;
		min-height: 150rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 8rpx;
		padding: 20rpx;
		font-size: 28rpx;
		margin-bottom: 20rpx;
		box-sizing: border-box;
	}

	.input-controls {
		display: flex;
		gap: 15rpx;
	}

	.control-btn {
		flex: 1;
		height: 60rpx;
		background-color: #2196F3;
		color: #ffffff;
		border: none;
		border-radius: 8rpx;
		font-size: 26rpx;
	}

	/* 结果区域 */
	.result-item {
		border: 1rpx solid #e0e0e0;
		border-radius: 8rpx;
		padding: 20rpx;
		margin-bottom: 15rpx;
	}

	.result-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.result-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333333;
	}

	.result-status {
		font-size: 24rpx;
		padding: 5rpx 15rpx;
		border-radius: 20rpx;
	}

	.result-status.success {
		background-color: #e8f5e8;
		color: #4CAF50;
	}

	.result-status.error {
		background-color: #ffeaea;
		color: #f44336;
	}

	.result-content {
		font-size: 26rpx;
		color: #666666;
		line-height: 1.4;
	}

	.result-data {
		margin-top: 10rpx;
		padding: 15rpx;
		background-color: #f8f9fa;
		border-radius: 6rpx;
	}

	.data-label {
		font-weight: bold;
		color: #333333;
	}

	.data-content {
		display: block;
		margin-top: 8rpx;
		word-break: break-all;
		font-family: monospace;
	}

	/* 密钥区域 */
	.key-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 15rpx;
		padding: 15rpx;
		background-color: #f8f9fa;
		border-radius: 8rpx;
	}

	.key-label {
		font-size: 28rpx;
		color: #333333;
	}

	.key-status.valid {
		color: #4CAF50;
		font-weight: bold;
	}

	.key-status.invalid {
		color: #f44336;
		font-weight: bold;
	}

	.key-value {
		font-size: 24rpx;
		color: #666666;
		font-family: monospace;
		max-width: 300rpx;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.key-btn {
		width: 100%;
		height: 70rpx;
		background-color: #FF9800;
		color: #ffffff;
		border: none;
		border-radius: 8rpx;
		font-size: 28rpx;
		margin-top: 20rpx;
	}
</style>
